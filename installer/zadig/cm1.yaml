---
# Source: zadig/templates/aslan-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: aslan-config
  namespace: zadig-test
  labels:
    app.kubernetes.io/component: aslan
    helm.sh/chart: zadig-3.4.0
    app.kubernetes.io/name: zadig
    app.kubernetes.io/instance: "zadig"
    app.kubernetes.io/version: "3.4.0"
    app.kubernetes.io/managed-by: "Helm"
data:
  # --------------------------------------------------------------------------------------
  # common
  # --------------------------------------------------------------------------------------
  ADDRESS: https://devops-test.scitix-inner.ai
  IMAGE_PULL_POLICY: Always

  # --------------------------------------------------------------------------------------
  # logging
  # level: 0(Debug), 1(Info), 2(Warn), 3(Error), 4(Panic), 5(Fatal)
  # --------------------------------------------------------------------------------------
  LOG_LEVEL: info

  # --------------------------------------------------------------------------------------
  # mongo
  # --------------------------------------------------------------------------------------
  MONGODB_CONNECTION_STRING: *************************************
  ASLAN_DB: zadig

  # --------------------------------------------------------------------------------------
  # kube
  # --------------------------------------------------------------------------------------
  KUBE_SERVER_ADDR: ""

  # --------------------------------------------------------------------------------------
  # build
  # --------------------------------------------------------------------------------------

  REAPER_IMAGE: koderover.tencentcloudcr.com/koderover-public/build-base:${BuildOS}-with-kubectl
  REAPER_BINARY_FILE: http://resource-server/reaper
  PREDATOR_IMAGE:  koderover.tencentcloudcr.com/koderover-public/predator-plugin:3.4.0
  JENKINS_BUILD_IMAGE: koderover.tencentcloudcr.com/koderover-public/jenkins-plugin:3.4.0
  PACKAGER_IMAGE:  koderover.tencentcloudcr.com/koderover-public/packager-plugin:3.4.0

  # --------------------------------------------------------------------------------------
  # github app
  # --------------------------------------------------------------------------------------
  GITHUB_KNOWN_HOST: ""
  GITHUB_SSH_KEY: ""

  # --------------------------------------------------------------------------------------
  # docker
  # --------------------------------------------------------------------------------------
  DOCKER_HOSTS: tcp://dind-0.dind:2375,tcp://dind-1.dind:2375,tcp://dind-2.dind:2375
  POETRY_API_ROOT_KEY: 9F11B4E503C7F2B5

  # -------------------------------------------------------------------------------
  # mysql settings due to the merge of user/config service
  # -------------------------------------------------------------------------------
  MYSQL_USER_DB: scitix_devops
  MYSQL_DEX_DB: scitix_devops
  MYSQL_HOST: ************:3306
  MYSQL_USER: ucpinfra
  MYSQL_PASSWORD: "<B9cg8vdV"

  # -------------------------------------------------------------------------------
  # user service environment variables
  # -------------------------------------------------------------------------------
  ISSUER_URL: https://console.scitix-inner.ai/dex
  CLIENT_ID: devops-zadig-op-id
  CLIENT_SECRET: devops-zadig-op-secret
  REDIRECT_URI: https://devops-test.scitix-inner.ai/api/v1/callback
  SCOPES: "openid,profile,email,offline_access,groups,federated:id"
  TOKEN_EXPIRES_AT: "1440"

  # -------------------------------------------------------------------------------
  # UNKNOWN USE
  # -------------------------------------------------------------------------------
  USE_CLASSIC_BUILD: "false"
  CUSTOM_DNS_NOT_SUPPORTED: "false"
  OLD_ENV_SUPPORTED: "false"

  ENABLE_TRANSACTION: "false"

  HUB_AGENT_IMAGE: koderover.tencentcloudcr.com/koderover-public/hub-agent:3.4.0
  EXECUTOR_IMAGE: koderover.tencentcloudcr.com/koderover-public/executor:3.4.0
  DIND_IMAGE: koderover.tencentcloudcr.com/koderover-public/docker:20.10.14-dind

  SERVICE_START_TIMEOUT: "600"

  S3STORAGE_ENDPOINT: kr-minio:9000
  S3STORAGE_AK: AKIAIOSFODNN72019EXAMPLE
  S3STORAGE_SK: wJalrXUtnFEMI2019K7MDENGbPxRfiCYEXAMPLEKEY
  S3STORAGE_BUCKET: bucket
  S3STORAGE_PROTOCOL: http

  REDIS_HOST: kr-redis
  REDIS_PORT: "6379"
  REDIS_USERNAME: ""
  REDIS_PASSWORD: ""
  REDIS_COMMON_CACHE_DB: "1"

  KODESPACE_VERSION: v1.1.0
  CHART_VERSION: 3.4.0
  ZADIG_AGENT_VERSION: v3.4.0
  ZADIG_AGENT_REPO_URL: https://resources.koderover.com/dist
