/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/


// This file was autogenerated by go-to-protobuf. Do not edit it manually!

syntax = "proto2";

package k8s.io.api.authorization.v1;

import "k8s.io/apimachinery/pkg/apis/meta/v1/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/generated.proto";
import "k8s.io/apimachinery/pkg/runtime/schema/generated.proto";

// Package-wide variables from generator "generated".
option go_package = "k8s.io/api/authorization/v1";

// ExtraValue masks the value so protobuf can generate
// +protobuf.nullable=true
// +protobuf.options.(gogoproto.goproto_stringer)=false
message ExtraValue {
  // items, if empty, will result in an empty slice

  repeated string items = 1;
}

// LocalSubjectAccessReview checks whether or not a user or group can perform an action in a given namespace.
// Having a namespace scoped resource makes it much easier to grant namespace scoped policy that includes permissions
// checking.
message LocalSubjectAccessReview {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec holds information about the request being evaluated.  spec.namespace must be equal to the namespace
  // you made the request against.  If empty, it is defaulted.
  optional SubjectAccessReviewSpec spec = 2;

  // Status is filled in by the server and indicates whether the request is allowed or not
  // +optional
  optional SubjectAccessReviewStatus status = 3;
}

// NonResourceAttributes includes the authorization attributes available for non-resource requests to the Authorizer interface
message NonResourceAttributes {
  // Path is the URL path of the request
  // +optional
  optional string path = 1;

  // Verb is the standard HTTP verb
  // +optional
  optional string verb = 2;
}

// NonResourceRule holds information that describes a rule for the non-resource
message NonResourceRule {
  // Verb is a list of kubernetes non-resource API verbs, like: get, post, put, delete, patch, head, options.  "*" means all.
  repeated string verbs = 1;

  // NonResourceURLs is a set of partial urls that a user should have access to.  *s are allowed, but only as the full,
  // final step in the path.  "*" means all.
  // +optional
  repeated string nonResourceURLs = 2;
}

// ResourceAttributes includes the authorization attributes available for resource requests to the Authorizer interface
message ResourceAttributes {
  // Namespace is the namespace of the action being requested.  Currently, there is no distinction between no namespace and all namespaces
  // "" (empty) is defaulted for LocalSubjectAccessReviews
  // "" (empty) is empty for cluster-scoped resources
  // "" (empty) means "all" for namespace scoped resources from a SubjectAccessReview or SelfSubjectAccessReview
  // +optional
  optional string namespace = 1;

  // Verb is a kubernetes resource API verb, like: get, list, watch, create, update, delete, proxy.  "*" means all.
  // +optional
  optional string verb = 2;

  // Group is the API Group of the Resource.  "*" means all.
  // +optional
  optional string group = 3;

  // Version is the API Version of the Resource.  "*" means all.
  // +optional
  optional string version = 4;

  // Resource is one of the existing resource types.  "*" means all.
  // +optional
  optional string resource = 5;

  // Subresource is one of the existing resource types.  "" means none.
  // +optional
  optional string subresource = 6;

  // Name is the name of the resource being requested for a "get" or deleted for a "delete". "" (empty) means all.
  // +optional
  optional string name = 7;
}

// ResourceRule is the list of actions the subject is allowed to perform on resources. The list ordering isn't significant,
// may contain duplicates, and possibly be incomplete.
message ResourceRule {
  // Verb is a list of kubernetes resource API verbs, like: get, list, watch, create, update, delete, proxy.  "*" means all.
  repeated string verbs = 1;

  // APIGroups is the name of the APIGroup that contains the resources.  If multiple API groups are specified, any action requested against one of
  // the enumerated resources in any API group will be allowed.  "*" means all.
  // +optional
  repeated string apiGroups = 2;

  // Resources is a list of resources this rule applies to.  "*" means all in the specified apiGroups.
  //  "*/foo" represents the subresource 'foo' for all resources in the specified apiGroups.
  // +optional
  repeated string resources = 3;

  // ResourceNames is an optional white list of names that the rule applies to.  An empty set means that everything is allowed.  "*" means all.
  // +optional
  repeated string resourceNames = 4;
}

// SelfSubjectAccessReview checks whether or the current user can perform an action.  Not filling in a
// spec.namespace means "in all namespaces".  Self is a special case, because users should always be able
// to check whether they can perform an action
message SelfSubjectAccessReview {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec holds information about the request being evaluated.  user and groups must be empty
  optional SelfSubjectAccessReviewSpec spec = 2;

  // Status is filled in by the server and indicates whether the request is allowed or not
  // +optional
  optional SubjectAccessReviewStatus status = 3;
}

// SelfSubjectAccessReviewSpec is a description of the access request.  Exactly one of ResourceAuthorizationAttributes
// and NonResourceAuthorizationAttributes must be set
message SelfSubjectAccessReviewSpec {
  // ResourceAuthorizationAttributes describes information for a resource access request
  // +optional
  optional ResourceAttributes resourceAttributes = 1;

  // NonResourceAttributes describes information for a non-resource access request
  // +optional
  optional NonResourceAttributes nonResourceAttributes = 2;
}

// SelfSubjectRulesReview enumerates the set of actions the current user can perform within a namespace.
// The returned list of actions may be incomplete depending on the server's authorization mode,
// and any errors experienced during the evaluation. SelfSubjectRulesReview should be used by UIs to show/hide actions,
// or to quickly let an end user reason about their permissions. It should NOT Be used by external systems to
// drive authorization decisions as this raises confused deputy, cache lifetime/revocation, and correctness concerns.
// SubjectAccessReview, and LocalAccessReview are the correct way to defer authorization decisions to the API server.
message SelfSubjectRulesReview {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec holds information about the request being evaluated.
  optional SelfSubjectRulesReviewSpec spec = 2;

  // Status is filled in by the server and indicates the set of actions a user can perform.
  // +optional
  optional SubjectRulesReviewStatus status = 3;
}

// SelfSubjectRulesReviewSpec defines the specification for SelfSubjectRulesReview.
message SelfSubjectRulesReviewSpec {
  // Namespace to evaluate rules for. Required.
  optional string namespace = 1;
}

// SubjectAccessReview checks whether or not a user or group can perform an action.
message SubjectAccessReview {
  // Standard list metadata.
  // More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#metadata
  // +optional
  optional k8s.io.apimachinery.pkg.apis.meta.v1.ObjectMeta metadata = 1;

  // Spec holds information about the request being evaluated
  optional SubjectAccessReviewSpec spec = 2;

  // Status is filled in by the server and indicates whether the request is allowed or not
  // +optional
  optional SubjectAccessReviewStatus status = 3;
}

// SubjectAccessReviewSpec is a description of the access request.  Exactly one of ResourceAuthorizationAttributes
// and NonResourceAuthorizationAttributes must be set
message SubjectAccessReviewSpec {
  // ResourceAuthorizationAttributes describes information for a resource access request
  // +optional
  optional ResourceAttributes resourceAttributes = 1;

  // NonResourceAttributes describes information for a non-resource access request
  // +optional
  optional NonResourceAttributes nonResourceAttributes = 2;

  // User is the user you're testing for.
  // If you specify "User" but not "Groups", then is it interpreted as "What if User were not a member of any groups
  // +optional
  optional string user = 3;

  // Groups is the groups you're testing for.
  // +optional
  repeated string groups = 4;

  // Extra corresponds to the user.Info.GetExtra() method from the authenticator.  Since that is input to the authorizer
  // it needs a reflection here.
  // +optional
  map<string, ExtraValue> extra = 5;

  // UID information about the requesting user.
  // +optional
  optional string uid = 6;
}

// SubjectAccessReviewStatus
message SubjectAccessReviewStatus {
  // Allowed is required. True if the action would be allowed, false otherwise.
  optional bool allowed = 1;

  // Denied is optional. True if the action would be denied, otherwise
  // false. If both allowed is false and denied is false, then the
  // authorizer has no opinion on whether to authorize the action. Denied
  // may not be true if Allowed is true.
  // +optional
  optional bool denied = 4;

  // Reason is optional.  It indicates why a request was allowed or denied.
  // +optional
  optional string reason = 2;

  // EvaluationError is an indication that some error occurred during the authorization check.
  // It is entirely possible to get an error and be able to continue determine authorization status in spite of it.
  // For instance, RBAC can be missing a role, but enough roles are still present and bound to reason about the request.
  // +optional
  optional string evaluationError = 3;
}

// SubjectRulesReviewStatus contains the result of a rules check. This check can be incomplete depending on
// the set of authorizers the server is configured with and any errors experienced during evaluation.
// Because authorization rules are additive, if a rule appears in a list it's safe to assume the subject has that permission,
// even if that list is incomplete.
message SubjectRulesReviewStatus {
  // ResourceRules is the list of actions the subject is allowed to perform on resources.
  // The list ordering isn't significant, may contain duplicates, and possibly be incomplete.
  repeated ResourceRule resourceRules = 1;

  // NonResourceRules is the list of actions the subject is allowed to perform on non-resources.
  // The list ordering isn't significant, may contain duplicates, and possibly be incomplete.
  repeated NonResourceRule nonResourceRules = 2;

  // Incomplete is true when the rules returned by this call are incomplete. This is most commonly
  // encountered when an authorizer, such as an external authorizer, doesn't support rules evaluation.
  optional bool incomplete = 3;

  // EvaluationError can appear in combination with Rules. It indicates an error occurred during
  // rule evaluation, such as an authorizer that doesn't support rule evaluation, and that
  // ResourceRules and/or NonResourceRules may be incomplete.
  // +optional
  optional string evaluationError = 4;
}

