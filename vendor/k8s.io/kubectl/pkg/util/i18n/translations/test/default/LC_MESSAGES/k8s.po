# Test translations for unit tests.
# Copyright (C) 2016
# This file is distributed under the same license as the Kubernetes package.
# <NAME_EMAIL>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: gettext-go-examples-hello\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2013-12-12 20:03+0000\n"
"PO-Revision-Date: 2016-12-13 21:35-0800\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.6.10\n"
"X-Poedit-SourceCharset: UTF-8\n"
"Language-Team: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Language: en\n"

msgid "test_plural"
msgid_plural "test_plural"
msgstr[0] "there was %d item"
msgstr[1] "there were %d items"

msgid "test_string"
msgstr "foo"
