// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

package ecr

import (
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awsutil"
	"github.com/aws/aws-sdk-go/aws/request"
	"github.com/aws/aws-sdk-go/private/protocol"
	"github.com/aws/aws-sdk-go/private/protocol/jsonrpc"
)

const opBatchCheckLayerAvailability = "BatchCheckLayerAvailability"

// BatchCheckLayerAvailabilityRequest generates a "aws/request.Request" representing the
// client's request for the BatchCheckLayerAvailability operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See BatchCheckLayerAvailability for more information on using the BatchCheckLayerAvailability
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the BatchCheckLayerAvailabilityRequest method.
//	req, resp := client.BatchCheckLayerAvailabilityRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/BatchCheckLayerAvailability
func (c *ECR) BatchCheckLayerAvailabilityRequest(input *BatchCheckLayerAvailabilityInput) (req *request.Request, output *BatchCheckLayerAvailabilityOutput) {
	op := &request.Operation{
		Name:       opBatchCheckLayerAvailability,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &BatchCheckLayerAvailabilityInput{}
	}

	output = &BatchCheckLayerAvailabilityOutput{}
	req = c.newRequest(op, input, output)
	return
}

// BatchCheckLayerAvailability API operation for Amazon EC2 Container Registry.
//
// Checks the availability of one or more image layers in a repository.
//
// When an image is pushed to a repository, each image layer is checked to verify
// if it has been uploaded before. If it has been uploaded, then the image layer
// is skipped.
//
// This operation is used by the Amazon ECR proxy and is not generally used
// by customers for pulling and pushing images. In most cases, you should use
// the docker CLI to pull, tag, and push images.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation BatchCheckLayerAvailability for usage and error information.
//
// Returned Error Types:
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/BatchCheckLayerAvailability
func (c *ECR) BatchCheckLayerAvailability(input *BatchCheckLayerAvailabilityInput) (*BatchCheckLayerAvailabilityOutput, error) {
	req, out := c.BatchCheckLayerAvailabilityRequest(input)
	return out, req.Send()
}

// BatchCheckLayerAvailabilityWithContext is the same as BatchCheckLayerAvailability with the addition of
// the ability to pass a context and additional request options.
//
// See BatchCheckLayerAvailability for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) BatchCheckLayerAvailabilityWithContext(ctx aws.Context, input *BatchCheckLayerAvailabilityInput, opts ...request.Option) (*BatchCheckLayerAvailabilityOutput, error) {
	req, out := c.BatchCheckLayerAvailabilityRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opBatchDeleteImage = "BatchDeleteImage"

// BatchDeleteImageRequest generates a "aws/request.Request" representing the
// client's request for the BatchDeleteImage operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See BatchDeleteImage for more information on using the BatchDeleteImage
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the BatchDeleteImageRequest method.
//	req, resp := client.BatchDeleteImageRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/BatchDeleteImage
func (c *ECR) BatchDeleteImageRequest(input *BatchDeleteImageInput) (req *request.Request, output *BatchDeleteImageOutput) {
	op := &request.Operation{
		Name:       opBatchDeleteImage,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &BatchDeleteImageInput{}
	}

	output = &BatchDeleteImageOutput{}
	req = c.newRequest(op, input, output)
	return
}

// BatchDeleteImage API operation for Amazon EC2 Container Registry.
//
// Deletes a list of specified images within a repository. Images are specified
// with either an imageTag or imageDigest.
//
// You can remove a tag from an image by specifying the image's tag in your
// request. When you remove the last tag from an image, the image is deleted
// from your repository.
//
// You can completely delete an image (and all of its tags) by specifying the
// image's digest in your request.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation BatchDeleteImage for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/BatchDeleteImage
func (c *ECR) BatchDeleteImage(input *BatchDeleteImageInput) (*BatchDeleteImageOutput, error) {
	req, out := c.BatchDeleteImageRequest(input)
	return out, req.Send()
}

// BatchDeleteImageWithContext is the same as BatchDeleteImage with the addition of
// the ability to pass a context and additional request options.
//
// See BatchDeleteImage for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) BatchDeleteImageWithContext(ctx aws.Context, input *BatchDeleteImageInput, opts ...request.Option) (*BatchDeleteImageOutput, error) {
	req, out := c.BatchDeleteImageRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opBatchGetImage = "BatchGetImage"

// BatchGetImageRequest generates a "aws/request.Request" representing the
// client's request for the BatchGetImage operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See BatchGetImage for more information on using the BatchGetImage
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the BatchGetImageRequest method.
//	req, resp := client.BatchGetImageRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/BatchGetImage
func (c *ECR) BatchGetImageRequest(input *BatchGetImageInput) (req *request.Request, output *BatchGetImageOutput) {
	op := &request.Operation{
		Name:       opBatchGetImage,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &BatchGetImageInput{}
	}

	output = &BatchGetImageOutput{}
	req = c.newRequest(op, input, output)
	return
}

// BatchGetImage API operation for Amazon EC2 Container Registry.
//
// Gets detailed information for an image. Images are specified with either
// an imageTag or imageDigest.
//
// When an image is pulled, the BatchGetImage API is called once to retrieve
// the image manifest.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation BatchGetImage for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/BatchGetImage
func (c *ECR) BatchGetImage(input *BatchGetImageInput) (*BatchGetImageOutput, error) {
	req, out := c.BatchGetImageRequest(input)
	return out, req.Send()
}

// BatchGetImageWithContext is the same as BatchGetImage with the addition of
// the ability to pass a context and additional request options.
//
// See BatchGetImage for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) BatchGetImageWithContext(ctx aws.Context, input *BatchGetImageInput, opts ...request.Option) (*BatchGetImageOutput, error) {
	req, out := c.BatchGetImageRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opBatchGetRepositoryScanningConfiguration = "BatchGetRepositoryScanningConfiguration"

// BatchGetRepositoryScanningConfigurationRequest generates a "aws/request.Request" representing the
// client's request for the BatchGetRepositoryScanningConfiguration operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See BatchGetRepositoryScanningConfiguration for more information on using the BatchGetRepositoryScanningConfiguration
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the BatchGetRepositoryScanningConfigurationRequest method.
//	req, resp := client.BatchGetRepositoryScanningConfigurationRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/BatchGetRepositoryScanningConfiguration
func (c *ECR) BatchGetRepositoryScanningConfigurationRequest(input *BatchGetRepositoryScanningConfigurationInput) (req *request.Request, output *BatchGetRepositoryScanningConfigurationOutput) {
	op := &request.Operation{
		Name:       opBatchGetRepositoryScanningConfiguration,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &BatchGetRepositoryScanningConfigurationInput{}
	}

	output = &BatchGetRepositoryScanningConfigurationOutput{}
	req = c.newRequest(op, input, output)
	return
}

// BatchGetRepositoryScanningConfiguration API operation for Amazon EC2 Container Registry.
//
// Gets the scanning configuration for one or more repositories.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation BatchGetRepositoryScanningConfiguration for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/BatchGetRepositoryScanningConfiguration
func (c *ECR) BatchGetRepositoryScanningConfiguration(input *BatchGetRepositoryScanningConfigurationInput) (*BatchGetRepositoryScanningConfigurationOutput, error) {
	req, out := c.BatchGetRepositoryScanningConfigurationRequest(input)
	return out, req.Send()
}

// BatchGetRepositoryScanningConfigurationWithContext is the same as BatchGetRepositoryScanningConfiguration with the addition of
// the ability to pass a context and additional request options.
//
// See BatchGetRepositoryScanningConfiguration for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) BatchGetRepositoryScanningConfigurationWithContext(ctx aws.Context, input *BatchGetRepositoryScanningConfigurationInput, opts ...request.Option) (*BatchGetRepositoryScanningConfigurationOutput, error) {
	req, out := c.BatchGetRepositoryScanningConfigurationRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opCompleteLayerUpload = "CompleteLayerUpload"

// CompleteLayerUploadRequest generates a "aws/request.Request" representing the
// client's request for the CompleteLayerUpload operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See CompleteLayerUpload for more information on using the CompleteLayerUpload
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the CompleteLayerUploadRequest method.
//	req, resp := client.CompleteLayerUploadRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/CompleteLayerUpload
func (c *ECR) CompleteLayerUploadRequest(input *CompleteLayerUploadInput) (req *request.Request, output *CompleteLayerUploadOutput) {
	op := &request.Operation{
		Name:       opCompleteLayerUpload,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &CompleteLayerUploadInput{}
	}

	output = &CompleteLayerUploadOutput{}
	req = c.newRequest(op, input, output)
	return
}

// CompleteLayerUpload API operation for Amazon EC2 Container Registry.
//
// Informs Amazon ECR that the image layer upload has completed for a specified
// registry, repository name, and upload ID. You can optionally provide a sha256
// digest of the image layer for data validation purposes.
//
// When an image is pushed, the CompleteLayerUpload API is called once per each
// new image layer to verify that the upload has completed.
//
// This operation is used by the Amazon ECR proxy and is not generally used
// by customers for pulling and pushing images. In most cases, you should use
// the docker CLI to pull, tag, and push images.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation CompleteLayerUpload for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - UploadNotFoundException
//     The upload could not be found, or the specified upload ID is not valid for
//     this repository.
//
//   - InvalidLayerException
//     The layer digest calculation performed by Amazon ECR upon receipt of the
//     image layer does not match the digest specified.
//
//   - LayerPartTooSmallException
//     Layer parts must be at least 5 MiB in size.
//
//   - LayerAlreadyExistsException
//     The image layer already exists in the associated repository.
//
//   - EmptyUploadException
//     The specified layer upload does not contain any layer parts.
//
//   - KmsException
//     The operation failed due to a KMS exception.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/CompleteLayerUpload
func (c *ECR) CompleteLayerUpload(input *CompleteLayerUploadInput) (*CompleteLayerUploadOutput, error) {
	req, out := c.CompleteLayerUploadRequest(input)
	return out, req.Send()
}

// CompleteLayerUploadWithContext is the same as CompleteLayerUpload with the addition of
// the ability to pass a context and additional request options.
//
// See CompleteLayerUpload for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) CompleteLayerUploadWithContext(ctx aws.Context, input *CompleteLayerUploadInput, opts ...request.Option) (*CompleteLayerUploadOutput, error) {
	req, out := c.CompleteLayerUploadRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opCreatePullThroughCacheRule = "CreatePullThroughCacheRule"

// CreatePullThroughCacheRuleRequest generates a "aws/request.Request" representing the
// client's request for the CreatePullThroughCacheRule operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See CreatePullThroughCacheRule for more information on using the CreatePullThroughCacheRule
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the CreatePullThroughCacheRuleRequest method.
//	req, resp := client.CreatePullThroughCacheRuleRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/CreatePullThroughCacheRule
func (c *ECR) CreatePullThroughCacheRuleRequest(input *CreatePullThroughCacheRuleInput) (req *request.Request, output *CreatePullThroughCacheRuleOutput) {
	op := &request.Operation{
		Name:       opCreatePullThroughCacheRule,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &CreatePullThroughCacheRuleInput{}
	}

	output = &CreatePullThroughCacheRuleOutput{}
	req = c.newRequest(op, input, output)
	return
}

// CreatePullThroughCacheRule API operation for Amazon EC2 Container Registry.
//
// Creates a pull through cache rule. A pull through cache rule provides a way
// to cache images from an external public registry in your Amazon ECR private
// registry.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation CreatePullThroughCacheRule for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ValidationException
//     There was an exception validating this request.
//
//   - PullThroughCacheRuleAlreadyExistsException
//     A pull through cache rule with these settings already exists for the private
//     registry.
//
//   - UnsupportedUpstreamRegistryException
//     The specified upstream registry isn't supported.
//
//   - LimitExceededException
//     The operation did not succeed because it would have exceeded a service limit
//     for your account. For more information, see Amazon ECR service quotas (https://docs.aws.amazon.com/AmazonECR/latest/userguide/service-quotas.html)
//     in the Amazon Elastic Container Registry User Guide.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/CreatePullThroughCacheRule
func (c *ECR) CreatePullThroughCacheRule(input *CreatePullThroughCacheRuleInput) (*CreatePullThroughCacheRuleOutput, error) {
	req, out := c.CreatePullThroughCacheRuleRequest(input)
	return out, req.Send()
}

// CreatePullThroughCacheRuleWithContext is the same as CreatePullThroughCacheRule with the addition of
// the ability to pass a context and additional request options.
//
// See CreatePullThroughCacheRule for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) CreatePullThroughCacheRuleWithContext(ctx aws.Context, input *CreatePullThroughCacheRuleInput, opts ...request.Option) (*CreatePullThroughCacheRuleOutput, error) {
	req, out := c.CreatePullThroughCacheRuleRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opCreateRepository = "CreateRepository"

// CreateRepositoryRequest generates a "aws/request.Request" representing the
// client's request for the CreateRepository operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See CreateRepository for more information on using the CreateRepository
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the CreateRepositoryRequest method.
//	req, resp := client.CreateRepositoryRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/CreateRepository
func (c *ECR) CreateRepositoryRequest(input *CreateRepositoryInput) (req *request.Request, output *CreateRepositoryOutput) {
	op := &request.Operation{
		Name:       opCreateRepository,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &CreateRepositoryInput{}
	}

	output = &CreateRepositoryOutput{}
	req = c.newRequest(op, input, output)
	return
}

// CreateRepository API operation for Amazon EC2 Container Registry.
//
// Creates a repository. For more information, see Amazon ECR repositories (https://docs.aws.amazon.com/AmazonECR/latest/userguide/Repositories.html)
// in the Amazon Elastic Container Registry User Guide.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation CreateRepository for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - InvalidTagParameterException
//     An invalid parameter has been specified. Tag keys can have a maximum character
//     length of 128 characters, and tag values can have a maximum length of 256
//     characters.
//
//   - TooManyTagsException
//     The list of tags on the repository is over the limit. The maximum number
//     of tags that can be applied to a repository is 50.
//
//   - RepositoryAlreadyExistsException
//     The specified repository already exists in the specified registry.
//
//   - LimitExceededException
//     The operation did not succeed because it would have exceeded a service limit
//     for your account. For more information, see Amazon ECR service quotas (https://docs.aws.amazon.com/AmazonECR/latest/userguide/service-quotas.html)
//     in the Amazon Elastic Container Registry User Guide.
//
//   - KmsException
//     The operation failed due to a KMS exception.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/CreateRepository
func (c *ECR) CreateRepository(input *CreateRepositoryInput) (*CreateRepositoryOutput, error) {
	req, out := c.CreateRepositoryRequest(input)
	return out, req.Send()
}

// CreateRepositoryWithContext is the same as CreateRepository with the addition of
// the ability to pass a context and additional request options.
//
// See CreateRepository for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) CreateRepositoryWithContext(ctx aws.Context, input *CreateRepositoryInput, opts ...request.Option) (*CreateRepositoryOutput, error) {
	req, out := c.CreateRepositoryRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDeleteLifecyclePolicy = "DeleteLifecyclePolicy"

// DeleteLifecyclePolicyRequest generates a "aws/request.Request" representing the
// client's request for the DeleteLifecyclePolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DeleteLifecyclePolicy for more information on using the DeleteLifecyclePolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DeleteLifecyclePolicyRequest method.
//	req, resp := client.DeleteLifecyclePolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeleteLifecyclePolicy
func (c *ECR) DeleteLifecyclePolicyRequest(input *DeleteLifecyclePolicyInput) (req *request.Request, output *DeleteLifecyclePolicyOutput) {
	op := &request.Operation{
		Name:       opDeleteLifecyclePolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DeleteLifecyclePolicyInput{}
	}

	output = &DeleteLifecyclePolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DeleteLifecyclePolicy API operation for Amazon EC2 Container Registry.
//
// Deletes the lifecycle policy associated with the specified repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DeleteLifecyclePolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - LifecyclePolicyNotFoundException
//     The lifecycle policy could not be found, and no policy is set to the repository.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeleteLifecyclePolicy
func (c *ECR) DeleteLifecyclePolicy(input *DeleteLifecyclePolicyInput) (*DeleteLifecyclePolicyOutput, error) {
	req, out := c.DeleteLifecyclePolicyRequest(input)
	return out, req.Send()
}

// DeleteLifecyclePolicyWithContext is the same as DeleteLifecyclePolicy with the addition of
// the ability to pass a context and additional request options.
//
// See DeleteLifecyclePolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DeleteLifecyclePolicyWithContext(ctx aws.Context, input *DeleteLifecyclePolicyInput, opts ...request.Option) (*DeleteLifecyclePolicyOutput, error) {
	req, out := c.DeleteLifecyclePolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDeletePullThroughCacheRule = "DeletePullThroughCacheRule"

// DeletePullThroughCacheRuleRequest generates a "aws/request.Request" representing the
// client's request for the DeletePullThroughCacheRule operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DeletePullThroughCacheRule for more information on using the DeletePullThroughCacheRule
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DeletePullThroughCacheRuleRequest method.
//	req, resp := client.DeletePullThroughCacheRuleRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeletePullThroughCacheRule
func (c *ECR) DeletePullThroughCacheRuleRequest(input *DeletePullThroughCacheRuleInput) (req *request.Request, output *DeletePullThroughCacheRuleOutput) {
	op := &request.Operation{
		Name:       opDeletePullThroughCacheRule,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DeletePullThroughCacheRuleInput{}
	}

	output = &DeletePullThroughCacheRuleOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DeletePullThroughCacheRule API operation for Amazon EC2 Container Registry.
//
// Deletes a pull through cache rule.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DeletePullThroughCacheRule for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ValidationException
//     There was an exception validating this request.
//
//   - PullThroughCacheRuleNotFoundException
//     The pull through cache rule was not found. Specify a valid pull through cache
//     rule and try again.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeletePullThroughCacheRule
func (c *ECR) DeletePullThroughCacheRule(input *DeletePullThroughCacheRuleInput) (*DeletePullThroughCacheRuleOutput, error) {
	req, out := c.DeletePullThroughCacheRuleRequest(input)
	return out, req.Send()
}

// DeletePullThroughCacheRuleWithContext is the same as DeletePullThroughCacheRule with the addition of
// the ability to pass a context and additional request options.
//
// See DeletePullThroughCacheRule for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DeletePullThroughCacheRuleWithContext(ctx aws.Context, input *DeletePullThroughCacheRuleInput, opts ...request.Option) (*DeletePullThroughCacheRuleOutput, error) {
	req, out := c.DeletePullThroughCacheRuleRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDeleteRegistryPolicy = "DeleteRegistryPolicy"

// DeleteRegistryPolicyRequest generates a "aws/request.Request" representing the
// client's request for the DeleteRegistryPolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DeleteRegistryPolicy for more information on using the DeleteRegistryPolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DeleteRegistryPolicyRequest method.
//	req, resp := client.DeleteRegistryPolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeleteRegistryPolicy
func (c *ECR) DeleteRegistryPolicyRequest(input *DeleteRegistryPolicyInput) (req *request.Request, output *DeleteRegistryPolicyOutput) {
	op := &request.Operation{
		Name:       opDeleteRegistryPolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DeleteRegistryPolicyInput{}
	}

	output = &DeleteRegistryPolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DeleteRegistryPolicy API operation for Amazon EC2 Container Registry.
//
// Deletes the registry permissions policy.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DeleteRegistryPolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RegistryPolicyNotFoundException
//     The registry doesn't have an associated registry policy.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeleteRegistryPolicy
func (c *ECR) DeleteRegistryPolicy(input *DeleteRegistryPolicyInput) (*DeleteRegistryPolicyOutput, error) {
	req, out := c.DeleteRegistryPolicyRequest(input)
	return out, req.Send()
}

// DeleteRegistryPolicyWithContext is the same as DeleteRegistryPolicy with the addition of
// the ability to pass a context and additional request options.
//
// See DeleteRegistryPolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DeleteRegistryPolicyWithContext(ctx aws.Context, input *DeleteRegistryPolicyInput, opts ...request.Option) (*DeleteRegistryPolicyOutput, error) {
	req, out := c.DeleteRegistryPolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDeleteRepository = "DeleteRepository"

// DeleteRepositoryRequest generates a "aws/request.Request" representing the
// client's request for the DeleteRepository operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DeleteRepository for more information on using the DeleteRepository
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DeleteRepositoryRequest method.
//	req, resp := client.DeleteRepositoryRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeleteRepository
func (c *ECR) DeleteRepositoryRequest(input *DeleteRepositoryInput) (req *request.Request, output *DeleteRepositoryOutput) {
	op := &request.Operation{
		Name:       opDeleteRepository,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DeleteRepositoryInput{}
	}

	output = &DeleteRepositoryOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DeleteRepository API operation for Amazon EC2 Container Registry.
//
// Deletes a repository. If the repository contains images, you must either
// delete all images in the repository or use the force option to delete the
// repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DeleteRepository for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - RepositoryNotEmptyException
//     The specified repository contains images. To delete a repository that contains
//     images, you must force the deletion with the force parameter.
//
//   - KmsException
//     The operation failed due to a KMS exception.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeleteRepository
func (c *ECR) DeleteRepository(input *DeleteRepositoryInput) (*DeleteRepositoryOutput, error) {
	req, out := c.DeleteRepositoryRequest(input)
	return out, req.Send()
}

// DeleteRepositoryWithContext is the same as DeleteRepository with the addition of
// the ability to pass a context and additional request options.
//
// See DeleteRepository for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DeleteRepositoryWithContext(ctx aws.Context, input *DeleteRepositoryInput, opts ...request.Option) (*DeleteRepositoryOutput, error) {
	req, out := c.DeleteRepositoryRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDeleteRepositoryPolicy = "DeleteRepositoryPolicy"

// DeleteRepositoryPolicyRequest generates a "aws/request.Request" representing the
// client's request for the DeleteRepositoryPolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DeleteRepositoryPolicy for more information on using the DeleteRepositoryPolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DeleteRepositoryPolicyRequest method.
//	req, resp := client.DeleteRepositoryPolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeleteRepositoryPolicy
func (c *ECR) DeleteRepositoryPolicyRequest(input *DeleteRepositoryPolicyInput) (req *request.Request, output *DeleteRepositoryPolicyOutput) {
	op := &request.Operation{
		Name:       opDeleteRepositoryPolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DeleteRepositoryPolicyInput{}
	}

	output = &DeleteRepositoryPolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DeleteRepositoryPolicy API operation for Amazon EC2 Container Registry.
//
// Deletes the repository policy associated with the specified repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DeleteRepositoryPolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - RepositoryPolicyNotFoundException
//     The specified repository and registry combination does not have an associated
//     repository policy.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DeleteRepositoryPolicy
func (c *ECR) DeleteRepositoryPolicy(input *DeleteRepositoryPolicyInput) (*DeleteRepositoryPolicyOutput, error) {
	req, out := c.DeleteRepositoryPolicyRequest(input)
	return out, req.Send()
}

// DeleteRepositoryPolicyWithContext is the same as DeleteRepositoryPolicy with the addition of
// the ability to pass a context and additional request options.
//
// See DeleteRepositoryPolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DeleteRepositoryPolicyWithContext(ctx aws.Context, input *DeleteRepositoryPolicyInput, opts ...request.Option) (*DeleteRepositoryPolicyOutput, error) {
	req, out := c.DeleteRepositoryPolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDescribeImageReplicationStatus = "DescribeImageReplicationStatus"

// DescribeImageReplicationStatusRequest generates a "aws/request.Request" representing the
// client's request for the DescribeImageReplicationStatus operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeImageReplicationStatus for more information on using the DescribeImageReplicationStatus
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DescribeImageReplicationStatusRequest method.
//	req, resp := client.DescribeImageReplicationStatusRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeImageReplicationStatus
func (c *ECR) DescribeImageReplicationStatusRequest(input *DescribeImageReplicationStatusInput) (req *request.Request, output *DescribeImageReplicationStatusOutput) {
	op := &request.Operation{
		Name:       opDescribeImageReplicationStatus,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DescribeImageReplicationStatusInput{}
	}

	output = &DescribeImageReplicationStatusOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeImageReplicationStatus API operation for Amazon EC2 Container Registry.
//
// Returns the replication status for a specified image.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DescribeImageReplicationStatus for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ImageNotFoundException
//     The image requested does not exist in the specified repository.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeImageReplicationStatus
func (c *ECR) DescribeImageReplicationStatus(input *DescribeImageReplicationStatusInput) (*DescribeImageReplicationStatusOutput, error) {
	req, out := c.DescribeImageReplicationStatusRequest(input)
	return out, req.Send()
}

// DescribeImageReplicationStatusWithContext is the same as DescribeImageReplicationStatus with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeImageReplicationStatus for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribeImageReplicationStatusWithContext(ctx aws.Context, input *DescribeImageReplicationStatusInput, opts ...request.Option) (*DescribeImageReplicationStatusOutput, error) {
	req, out := c.DescribeImageReplicationStatusRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDescribeImageScanFindings = "DescribeImageScanFindings"

// DescribeImageScanFindingsRequest generates a "aws/request.Request" representing the
// client's request for the DescribeImageScanFindings operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeImageScanFindings for more information on using the DescribeImageScanFindings
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DescribeImageScanFindingsRequest method.
//	req, resp := client.DescribeImageScanFindingsRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeImageScanFindings
func (c *ECR) DescribeImageScanFindingsRequest(input *DescribeImageScanFindingsInput) (req *request.Request, output *DescribeImageScanFindingsOutput) {
	op := &request.Operation{
		Name:       opDescribeImageScanFindings,
		HTTPMethod: "POST",
		HTTPPath:   "/",
		Paginator: &request.Paginator{
			InputTokens:     []string{"nextToken"},
			OutputTokens:    []string{"nextToken"},
			LimitToken:      "maxResults",
			TruncationToken: "",
		},
	}

	if input == nil {
		input = &DescribeImageScanFindingsInput{}
	}

	output = &DescribeImageScanFindingsOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeImageScanFindings API operation for Amazon EC2 Container Registry.
//
// Returns the scan findings for the specified image.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DescribeImageScanFindings for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ImageNotFoundException
//     The image requested does not exist in the specified repository.
//
//   - ScanNotFoundException
//     The specified image scan could not be found. Ensure that image scanning is
//     enabled on the repository and try again.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeImageScanFindings
func (c *ECR) DescribeImageScanFindings(input *DescribeImageScanFindingsInput) (*DescribeImageScanFindingsOutput, error) {
	req, out := c.DescribeImageScanFindingsRequest(input)
	return out, req.Send()
}

// DescribeImageScanFindingsWithContext is the same as DescribeImageScanFindings with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeImageScanFindings for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribeImageScanFindingsWithContext(ctx aws.Context, input *DescribeImageScanFindingsInput, opts ...request.Option) (*DescribeImageScanFindingsOutput, error) {
	req, out := c.DescribeImageScanFindingsRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// DescribeImageScanFindingsPages iterates over the pages of a DescribeImageScanFindings operation,
// calling the "fn" function with the response data for each page. To stop
// iterating, return false from the fn function.
//
// See DescribeImageScanFindings method for more information on how to use this operation.
//
// Note: This operation can generate multiple requests to a service.
//
//	// Example iterating over at most 3 pages of a DescribeImageScanFindings operation.
//	pageNum := 0
//	err := client.DescribeImageScanFindingsPages(params,
//	    func(page *ecr.DescribeImageScanFindingsOutput, lastPage bool) bool {
//	        pageNum++
//	        fmt.Println(page)
//	        return pageNum <= 3
//	    })
func (c *ECR) DescribeImageScanFindingsPages(input *DescribeImageScanFindingsInput, fn func(*DescribeImageScanFindingsOutput, bool) bool) error {
	return c.DescribeImageScanFindingsPagesWithContext(aws.BackgroundContext(), input, fn)
}

// DescribeImageScanFindingsPagesWithContext same as DescribeImageScanFindingsPages except
// it takes a Context and allows setting request options on the pages.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribeImageScanFindingsPagesWithContext(ctx aws.Context, input *DescribeImageScanFindingsInput, fn func(*DescribeImageScanFindingsOutput, bool) bool, opts ...request.Option) error {
	p := request.Pagination{
		NewRequest: func() (*request.Request, error) {
			var inCpy *DescribeImageScanFindingsInput
			if input != nil {
				tmp := *input
				inCpy = &tmp
			}
			req, _ := c.DescribeImageScanFindingsRequest(inCpy)
			req.SetContext(ctx)
			req.ApplyOptions(opts...)
			return req, nil
		},
	}

	for p.Next() {
		if !fn(p.Page().(*DescribeImageScanFindingsOutput), !p.HasNextPage()) {
			break
		}
	}

	return p.Err()
}

const opDescribeImages = "DescribeImages"

// DescribeImagesRequest generates a "aws/request.Request" representing the
// client's request for the DescribeImages operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeImages for more information on using the DescribeImages
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DescribeImagesRequest method.
//	req, resp := client.DescribeImagesRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeImages
func (c *ECR) DescribeImagesRequest(input *DescribeImagesInput) (req *request.Request, output *DescribeImagesOutput) {
	op := &request.Operation{
		Name:       opDescribeImages,
		HTTPMethod: "POST",
		HTTPPath:   "/",
		Paginator: &request.Paginator{
			InputTokens:     []string{"nextToken"},
			OutputTokens:    []string{"nextToken"},
			LimitToken:      "maxResults",
			TruncationToken: "",
		},
	}

	if input == nil {
		input = &DescribeImagesInput{}
	}

	output = &DescribeImagesOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeImages API operation for Amazon EC2 Container Registry.
//
// Returns metadata about the images in a repository.
//
// Beginning with Docker version 1.9, the Docker client compresses image layers
// before pushing them to a V2 Docker registry. The output of the docker images
// command shows the uncompressed image size, so it may return a larger image
// size than the image sizes returned by DescribeImages.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DescribeImages for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ImageNotFoundException
//     The image requested does not exist in the specified repository.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeImages
func (c *ECR) DescribeImages(input *DescribeImagesInput) (*DescribeImagesOutput, error) {
	req, out := c.DescribeImagesRequest(input)
	return out, req.Send()
}

// DescribeImagesWithContext is the same as DescribeImages with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeImages for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribeImagesWithContext(ctx aws.Context, input *DescribeImagesInput, opts ...request.Option) (*DescribeImagesOutput, error) {
	req, out := c.DescribeImagesRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// DescribeImagesPages iterates over the pages of a DescribeImages operation,
// calling the "fn" function with the response data for each page. To stop
// iterating, return false from the fn function.
//
// See DescribeImages method for more information on how to use this operation.
//
// Note: This operation can generate multiple requests to a service.
//
//	// Example iterating over at most 3 pages of a DescribeImages operation.
//	pageNum := 0
//	err := client.DescribeImagesPages(params,
//	    func(page *ecr.DescribeImagesOutput, lastPage bool) bool {
//	        pageNum++
//	        fmt.Println(page)
//	        return pageNum <= 3
//	    })
func (c *ECR) DescribeImagesPages(input *DescribeImagesInput, fn func(*DescribeImagesOutput, bool) bool) error {
	return c.DescribeImagesPagesWithContext(aws.BackgroundContext(), input, fn)
}

// DescribeImagesPagesWithContext same as DescribeImagesPages except
// it takes a Context and allows setting request options on the pages.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribeImagesPagesWithContext(ctx aws.Context, input *DescribeImagesInput, fn func(*DescribeImagesOutput, bool) bool, opts ...request.Option) error {
	p := request.Pagination{
		NewRequest: func() (*request.Request, error) {
			var inCpy *DescribeImagesInput
			if input != nil {
				tmp := *input
				inCpy = &tmp
			}
			req, _ := c.DescribeImagesRequest(inCpy)
			req.SetContext(ctx)
			req.ApplyOptions(opts...)
			return req, nil
		},
	}

	for p.Next() {
		if !fn(p.Page().(*DescribeImagesOutput), !p.HasNextPage()) {
			break
		}
	}

	return p.Err()
}

const opDescribePullThroughCacheRules = "DescribePullThroughCacheRules"

// DescribePullThroughCacheRulesRequest generates a "aws/request.Request" representing the
// client's request for the DescribePullThroughCacheRules operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribePullThroughCacheRules for more information on using the DescribePullThroughCacheRules
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DescribePullThroughCacheRulesRequest method.
//	req, resp := client.DescribePullThroughCacheRulesRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribePullThroughCacheRules
func (c *ECR) DescribePullThroughCacheRulesRequest(input *DescribePullThroughCacheRulesInput) (req *request.Request, output *DescribePullThroughCacheRulesOutput) {
	op := &request.Operation{
		Name:       opDescribePullThroughCacheRules,
		HTTPMethod: "POST",
		HTTPPath:   "/",
		Paginator: &request.Paginator{
			InputTokens:     []string{"nextToken"},
			OutputTokens:    []string{"nextToken"},
			LimitToken:      "maxResults",
			TruncationToken: "",
		},
	}

	if input == nil {
		input = &DescribePullThroughCacheRulesInput{}
	}

	output = &DescribePullThroughCacheRulesOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribePullThroughCacheRules API operation for Amazon EC2 Container Registry.
//
// Returns the pull through cache rules for a registry.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DescribePullThroughCacheRules for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ValidationException
//     There was an exception validating this request.
//
//   - PullThroughCacheRuleNotFoundException
//     The pull through cache rule was not found. Specify a valid pull through cache
//     rule and try again.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribePullThroughCacheRules
func (c *ECR) DescribePullThroughCacheRules(input *DescribePullThroughCacheRulesInput) (*DescribePullThroughCacheRulesOutput, error) {
	req, out := c.DescribePullThroughCacheRulesRequest(input)
	return out, req.Send()
}

// DescribePullThroughCacheRulesWithContext is the same as DescribePullThroughCacheRules with the addition of
// the ability to pass a context and additional request options.
//
// See DescribePullThroughCacheRules for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribePullThroughCacheRulesWithContext(ctx aws.Context, input *DescribePullThroughCacheRulesInput, opts ...request.Option) (*DescribePullThroughCacheRulesOutput, error) {
	req, out := c.DescribePullThroughCacheRulesRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// DescribePullThroughCacheRulesPages iterates over the pages of a DescribePullThroughCacheRules operation,
// calling the "fn" function with the response data for each page. To stop
// iterating, return false from the fn function.
//
// See DescribePullThroughCacheRules method for more information on how to use this operation.
//
// Note: This operation can generate multiple requests to a service.
//
//	// Example iterating over at most 3 pages of a DescribePullThroughCacheRules operation.
//	pageNum := 0
//	err := client.DescribePullThroughCacheRulesPages(params,
//	    func(page *ecr.DescribePullThroughCacheRulesOutput, lastPage bool) bool {
//	        pageNum++
//	        fmt.Println(page)
//	        return pageNum <= 3
//	    })
func (c *ECR) DescribePullThroughCacheRulesPages(input *DescribePullThroughCacheRulesInput, fn func(*DescribePullThroughCacheRulesOutput, bool) bool) error {
	return c.DescribePullThroughCacheRulesPagesWithContext(aws.BackgroundContext(), input, fn)
}

// DescribePullThroughCacheRulesPagesWithContext same as DescribePullThroughCacheRulesPages except
// it takes a Context and allows setting request options on the pages.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribePullThroughCacheRulesPagesWithContext(ctx aws.Context, input *DescribePullThroughCacheRulesInput, fn func(*DescribePullThroughCacheRulesOutput, bool) bool, opts ...request.Option) error {
	p := request.Pagination{
		NewRequest: func() (*request.Request, error) {
			var inCpy *DescribePullThroughCacheRulesInput
			if input != nil {
				tmp := *input
				inCpy = &tmp
			}
			req, _ := c.DescribePullThroughCacheRulesRequest(inCpy)
			req.SetContext(ctx)
			req.ApplyOptions(opts...)
			return req, nil
		},
	}

	for p.Next() {
		if !fn(p.Page().(*DescribePullThroughCacheRulesOutput), !p.HasNextPage()) {
			break
		}
	}

	return p.Err()
}

const opDescribeRegistry = "DescribeRegistry"

// DescribeRegistryRequest generates a "aws/request.Request" representing the
// client's request for the DescribeRegistry operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeRegistry for more information on using the DescribeRegistry
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DescribeRegistryRequest method.
//	req, resp := client.DescribeRegistryRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeRegistry
func (c *ECR) DescribeRegistryRequest(input *DescribeRegistryInput) (req *request.Request, output *DescribeRegistryOutput) {
	op := &request.Operation{
		Name:       opDescribeRegistry,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &DescribeRegistryInput{}
	}

	output = &DescribeRegistryOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeRegistry API operation for Amazon EC2 Container Registry.
//
// Describes the settings for a registry. The replication configuration for
// a repository can be created or updated with the PutReplicationConfiguration
// API action.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DescribeRegistry for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeRegistry
func (c *ECR) DescribeRegistry(input *DescribeRegistryInput) (*DescribeRegistryOutput, error) {
	req, out := c.DescribeRegistryRequest(input)
	return out, req.Send()
}

// DescribeRegistryWithContext is the same as DescribeRegistry with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeRegistry for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribeRegistryWithContext(ctx aws.Context, input *DescribeRegistryInput, opts ...request.Option) (*DescribeRegistryOutput, error) {
	req, out := c.DescribeRegistryRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opDescribeRepositories = "DescribeRepositories"

// DescribeRepositoriesRequest generates a "aws/request.Request" representing the
// client's request for the DescribeRepositories operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See DescribeRepositories for more information on using the DescribeRepositories
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the DescribeRepositoriesRequest method.
//	req, resp := client.DescribeRepositoriesRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeRepositories
func (c *ECR) DescribeRepositoriesRequest(input *DescribeRepositoriesInput) (req *request.Request, output *DescribeRepositoriesOutput) {
	op := &request.Operation{
		Name:       opDescribeRepositories,
		HTTPMethod: "POST",
		HTTPPath:   "/",
		Paginator: &request.Paginator{
			InputTokens:     []string{"nextToken"},
			OutputTokens:    []string{"nextToken"},
			LimitToken:      "maxResults",
			TruncationToken: "",
		},
	}

	if input == nil {
		input = &DescribeRepositoriesInput{}
	}

	output = &DescribeRepositoriesOutput{}
	req = c.newRequest(op, input, output)
	return
}

// DescribeRepositories API operation for Amazon EC2 Container Registry.
//
// Describes image repositories in a registry.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation DescribeRepositories for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/DescribeRepositories
func (c *ECR) DescribeRepositories(input *DescribeRepositoriesInput) (*DescribeRepositoriesOutput, error) {
	req, out := c.DescribeRepositoriesRequest(input)
	return out, req.Send()
}

// DescribeRepositoriesWithContext is the same as DescribeRepositories with the addition of
// the ability to pass a context and additional request options.
//
// See DescribeRepositories for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribeRepositoriesWithContext(ctx aws.Context, input *DescribeRepositoriesInput, opts ...request.Option) (*DescribeRepositoriesOutput, error) {
	req, out := c.DescribeRepositoriesRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// DescribeRepositoriesPages iterates over the pages of a DescribeRepositories operation,
// calling the "fn" function with the response data for each page. To stop
// iterating, return false from the fn function.
//
// See DescribeRepositories method for more information on how to use this operation.
//
// Note: This operation can generate multiple requests to a service.
//
//	// Example iterating over at most 3 pages of a DescribeRepositories operation.
//	pageNum := 0
//	err := client.DescribeRepositoriesPages(params,
//	    func(page *ecr.DescribeRepositoriesOutput, lastPage bool) bool {
//	        pageNum++
//	        fmt.Println(page)
//	        return pageNum <= 3
//	    })
func (c *ECR) DescribeRepositoriesPages(input *DescribeRepositoriesInput, fn func(*DescribeRepositoriesOutput, bool) bool) error {
	return c.DescribeRepositoriesPagesWithContext(aws.BackgroundContext(), input, fn)
}

// DescribeRepositoriesPagesWithContext same as DescribeRepositoriesPages except
// it takes a Context and allows setting request options on the pages.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) DescribeRepositoriesPagesWithContext(ctx aws.Context, input *DescribeRepositoriesInput, fn func(*DescribeRepositoriesOutput, bool) bool, opts ...request.Option) error {
	p := request.Pagination{
		NewRequest: func() (*request.Request, error) {
			var inCpy *DescribeRepositoriesInput
			if input != nil {
				tmp := *input
				inCpy = &tmp
			}
			req, _ := c.DescribeRepositoriesRequest(inCpy)
			req.SetContext(ctx)
			req.ApplyOptions(opts...)
			return req, nil
		},
	}

	for p.Next() {
		if !fn(p.Page().(*DescribeRepositoriesOutput), !p.HasNextPage()) {
			break
		}
	}

	return p.Err()
}

const opGetAuthorizationToken = "GetAuthorizationToken"

// GetAuthorizationTokenRequest generates a "aws/request.Request" representing the
// client's request for the GetAuthorizationToken operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See GetAuthorizationToken for more information on using the GetAuthorizationToken
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the GetAuthorizationTokenRequest method.
//	req, resp := client.GetAuthorizationTokenRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetAuthorizationToken
func (c *ECR) GetAuthorizationTokenRequest(input *GetAuthorizationTokenInput) (req *request.Request, output *GetAuthorizationTokenOutput) {
	op := &request.Operation{
		Name:       opGetAuthorizationToken,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &GetAuthorizationTokenInput{}
	}

	output = &GetAuthorizationTokenOutput{}
	req = c.newRequest(op, input, output)
	return
}

// GetAuthorizationToken API operation for Amazon EC2 Container Registry.
//
// Retrieves an authorization token. An authorization token represents your
// IAM authentication credentials and can be used to access any Amazon ECR registry
// that your IAM principal has access to. The authorization token is valid for
// 12 hours.
//
// The authorizationToken returned is a base64 encoded string that can be decoded
// and used in a docker login command to authenticate to a registry. The CLI
// offers an get-login-password command that simplifies the login process. For
// more information, see Registry authentication (https://docs.aws.amazon.com/AmazonECR/latest/userguide/Registries.html#registry_auth)
// in the Amazon Elastic Container Registry User Guide.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation GetAuthorizationToken for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetAuthorizationToken
func (c *ECR) GetAuthorizationToken(input *GetAuthorizationTokenInput) (*GetAuthorizationTokenOutput, error) {
	req, out := c.GetAuthorizationTokenRequest(input)
	return out, req.Send()
}

// GetAuthorizationTokenWithContext is the same as GetAuthorizationToken with the addition of
// the ability to pass a context and additional request options.
//
// See GetAuthorizationToken for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) GetAuthorizationTokenWithContext(ctx aws.Context, input *GetAuthorizationTokenInput, opts ...request.Option) (*GetAuthorizationTokenOutput, error) {
	req, out := c.GetAuthorizationTokenRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opGetDownloadUrlForLayer = "GetDownloadUrlForLayer"

// GetDownloadUrlForLayerRequest generates a "aws/request.Request" representing the
// client's request for the GetDownloadUrlForLayer operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See GetDownloadUrlForLayer for more information on using the GetDownloadUrlForLayer
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the GetDownloadUrlForLayerRequest method.
//	req, resp := client.GetDownloadUrlForLayerRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetDownloadUrlForLayer
func (c *ECR) GetDownloadUrlForLayerRequest(input *GetDownloadUrlForLayerInput) (req *request.Request, output *GetDownloadUrlForLayerOutput) {
	op := &request.Operation{
		Name:       opGetDownloadUrlForLayer,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &GetDownloadUrlForLayerInput{}
	}

	output = &GetDownloadUrlForLayerOutput{}
	req = c.newRequest(op, input, output)
	return
}

// GetDownloadUrlForLayer API operation for Amazon EC2 Container Registry.
//
// Retrieves the pre-signed Amazon S3 download URL corresponding to an image
// layer. You can only get URLs for image layers that are referenced in an image.
//
// When an image is pulled, the GetDownloadUrlForLayer API is called once per
// image layer that is not already cached.
//
// This operation is used by the Amazon ECR proxy and is not generally used
// by customers for pulling and pushing images. In most cases, you should use
// the docker CLI to pull, tag, and push images.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation GetDownloadUrlForLayer for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - LayersNotFoundException
//     The specified layers could not be found, or the specified layer is not valid
//     for this repository.
//
//   - LayerInaccessibleException
//     The specified layer is not available because it is not associated with an
//     image. Unassociated image layers may be cleaned up at any time.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetDownloadUrlForLayer
func (c *ECR) GetDownloadUrlForLayer(input *GetDownloadUrlForLayerInput) (*GetDownloadUrlForLayerOutput, error) {
	req, out := c.GetDownloadUrlForLayerRequest(input)
	return out, req.Send()
}

// GetDownloadUrlForLayerWithContext is the same as GetDownloadUrlForLayer with the addition of
// the ability to pass a context and additional request options.
//
// See GetDownloadUrlForLayer for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) GetDownloadUrlForLayerWithContext(ctx aws.Context, input *GetDownloadUrlForLayerInput, opts ...request.Option) (*GetDownloadUrlForLayerOutput, error) {
	req, out := c.GetDownloadUrlForLayerRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opGetLifecyclePolicy = "GetLifecyclePolicy"

// GetLifecyclePolicyRequest generates a "aws/request.Request" representing the
// client's request for the GetLifecyclePolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See GetLifecyclePolicy for more information on using the GetLifecyclePolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the GetLifecyclePolicyRequest method.
//	req, resp := client.GetLifecyclePolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetLifecyclePolicy
func (c *ECR) GetLifecyclePolicyRequest(input *GetLifecyclePolicyInput) (req *request.Request, output *GetLifecyclePolicyOutput) {
	op := &request.Operation{
		Name:       opGetLifecyclePolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &GetLifecyclePolicyInput{}
	}

	output = &GetLifecyclePolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// GetLifecyclePolicy API operation for Amazon EC2 Container Registry.
//
// Retrieves the lifecycle policy for the specified repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation GetLifecyclePolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - LifecyclePolicyNotFoundException
//     The lifecycle policy could not be found, and no policy is set to the repository.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetLifecyclePolicy
func (c *ECR) GetLifecyclePolicy(input *GetLifecyclePolicyInput) (*GetLifecyclePolicyOutput, error) {
	req, out := c.GetLifecyclePolicyRequest(input)
	return out, req.Send()
}

// GetLifecyclePolicyWithContext is the same as GetLifecyclePolicy with the addition of
// the ability to pass a context and additional request options.
//
// See GetLifecyclePolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) GetLifecyclePolicyWithContext(ctx aws.Context, input *GetLifecyclePolicyInput, opts ...request.Option) (*GetLifecyclePolicyOutput, error) {
	req, out := c.GetLifecyclePolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opGetLifecyclePolicyPreview = "GetLifecyclePolicyPreview"

// GetLifecyclePolicyPreviewRequest generates a "aws/request.Request" representing the
// client's request for the GetLifecyclePolicyPreview operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See GetLifecyclePolicyPreview for more information on using the GetLifecyclePolicyPreview
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the GetLifecyclePolicyPreviewRequest method.
//	req, resp := client.GetLifecyclePolicyPreviewRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetLifecyclePolicyPreview
func (c *ECR) GetLifecyclePolicyPreviewRequest(input *GetLifecyclePolicyPreviewInput) (req *request.Request, output *GetLifecyclePolicyPreviewOutput) {
	op := &request.Operation{
		Name:       opGetLifecyclePolicyPreview,
		HTTPMethod: "POST",
		HTTPPath:   "/",
		Paginator: &request.Paginator{
			InputTokens:     []string{"nextToken"},
			OutputTokens:    []string{"nextToken"},
			LimitToken:      "maxResults",
			TruncationToken: "",
		},
	}

	if input == nil {
		input = &GetLifecyclePolicyPreviewInput{}
	}

	output = &GetLifecyclePolicyPreviewOutput{}
	req = c.newRequest(op, input, output)
	return
}

// GetLifecyclePolicyPreview API operation for Amazon EC2 Container Registry.
//
// Retrieves the results of the lifecycle policy preview request for the specified
// repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation GetLifecyclePolicyPreview for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - LifecyclePolicyPreviewNotFoundException
//     There is no dry run for this repository.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetLifecyclePolicyPreview
func (c *ECR) GetLifecyclePolicyPreview(input *GetLifecyclePolicyPreviewInput) (*GetLifecyclePolicyPreviewOutput, error) {
	req, out := c.GetLifecyclePolicyPreviewRequest(input)
	return out, req.Send()
}

// GetLifecyclePolicyPreviewWithContext is the same as GetLifecyclePolicyPreview with the addition of
// the ability to pass a context and additional request options.
//
// See GetLifecyclePolicyPreview for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) GetLifecyclePolicyPreviewWithContext(ctx aws.Context, input *GetLifecyclePolicyPreviewInput, opts ...request.Option) (*GetLifecyclePolicyPreviewOutput, error) {
	req, out := c.GetLifecyclePolicyPreviewRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// GetLifecyclePolicyPreviewPages iterates over the pages of a GetLifecyclePolicyPreview operation,
// calling the "fn" function with the response data for each page. To stop
// iterating, return false from the fn function.
//
// See GetLifecyclePolicyPreview method for more information on how to use this operation.
//
// Note: This operation can generate multiple requests to a service.
//
//	// Example iterating over at most 3 pages of a GetLifecyclePolicyPreview operation.
//	pageNum := 0
//	err := client.GetLifecyclePolicyPreviewPages(params,
//	    func(page *ecr.GetLifecyclePolicyPreviewOutput, lastPage bool) bool {
//	        pageNum++
//	        fmt.Println(page)
//	        return pageNum <= 3
//	    })
func (c *ECR) GetLifecyclePolicyPreviewPages(input *GetLifecyclePolicyPreviewInput, fn func(*GetLifecyclePolicyPreviewOutput, bool) bool) error {
	return c.GetLifecyclePolicyPreviewPagesWithContext(aws.BackgroundContext(), input, fn)
}

// GetLifecyclePolicyPreviewPagesWithContext same as GetLifecyclePolicyPreviewPages except
// it takes a Context and allows setting request options on the pages.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) GetLifecyclePolicyPreviewPagesWithContext(ctx aws.Context, input *GetLifecyclePolicyPreviewInput, fn func(*GetLifecyclePolicyPreviewOutput, bool) bool, opts ...request.Option) error {
	p := request.Pagination{
		NewRequest: func() (*request.Request, error) {
			var inCpy *GetLifecyclePolicyPreviewInput
			if input != nil {
				tmp := *input
				inCpy = &tmp
			}
			req, _ := c.GetLifecyclePolicyPreviewRequest(inCpy)
			req.SetContext(ctx)
			req.ApplyOptions(opts...)
			return req, nil
		},
	}

	for p.Next() {
		if !fn(p.Page().(*GetLifecyclePolicyPreviewOutput), !p.HasNextPage()) {
			break
		}
	}

	return p.Err()
}

const opGetRegistryPolicy = "GetRegistryPolicy"

// GetRegistryPolicyRequest generates a "aws/request.Request" representing the
// client's request for the GetRegistryPolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See GetRegistryPolicy for more information on using the GetRegistryPolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the GetRegistryPolicyRequest method.
//	req, resp := client.GetRegistryPolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetRegistryPolicy
func (c *ECR) GetRegistryPolicyRequest(input *GetRegistryPolicyInput) (req *request.Request, output *GetRegistryPolicyOutput) {
	op := &request.Operation{
		Name:       opGetRegistryPolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &GetRegistryPolicyInput{}
	}

	output = &GetRegistryPolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// GetRegistryPolicy API operation for Amazon EC2 Container Registry.
//
// Retrieves the permissions policy for a registry.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation GetRegistryPolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RegistryPolicyNotFoundException
//     The registry doesn't have an associated registry policy.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetRegistryPolicy
func (c *ECR) GetRegistryPolicy(input *GetRegistryPolicyInput) (*GetRegistryPolicyOutput, error) {
	req, out := c.GetRegistryPolicyRequest(input)
	return out, req.Send()
}

// GetRegistryPolicyWithContext is the same as GetRegistryPolicy with the addition of
// the ability to pass a context and additional request options.
//
// See GetRegistryPolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) GetRegistryPolicyWithContext(ctx aws.Context, input *GetRegistryPolicyInput, opts ...request.Option) (*GetRegistryPolicyOutput, error) {
	req, out := c.GetRegistryPolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opGetRegistryScanningConfiguration = "GetRegistryScanningConfiguration"

// GetRegistryScanningConfigurationRequest generates a "aws/request.Request" representing the
// client's request for the GetRegistryScanningConfiguration operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See GetRegistryScanningConfiguration for more information on using the GetRegistryScanningConfiguration
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the GetRegistryScanningConfigurationRequest method.
//	req, resp := client.GetRegistryScanningConfigurationRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetRegistryScanningConfiguration
func (c *ECR) GetRegistryScanningConfigurationRequest(input *GetRegistryScanningConfigurationInput) (req *request.Request, output *GetRegistryScanningConfigurationOutput) {
	op := &request.Operation{
		Name:       opGetRegistryScanningConfiguration,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &GetRegistryScanningConfigurationInput{}
	}

	output = &GetRegistryScanningConfigurationOutput{}
	req = c.newRequest(op, input, output)
	return
}

// GetRegistryScanningConfiguration API operation for Amazon EC2 Container Registry.
//
// Retrieves the scanning configuration for a registry.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation GetRegistryScanningConfiguration for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetRegistryScanningConfiguration
func (c *ECR) GetRegistryScanningConfiguration(input *GetRegistryScanningConfigurationInput) (*GetRegistryScanningConfigurationOutput, error) {
	req, out := c.GetRegistryScanningConfigurationRequest(input)
	return out, req.Send()
}

// GetRegistryScanningConfigurationWithContext is the same as GetRegistryScanningConfiguration with the addition of
// the ability to pass a context and additional request options.
//
// See GetRegistryScanningConfiguration for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) GetRegistryScanningConfigurationWithContext(ctx aws.Context, input *GetRegistryScanningConfigurationInput, opts ...request.Option) (*GetRegistryScanningConfigurationOutput, error) {
	req, out := c.GetRegistryScanningConfigurationRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opGetRepositoryPolicy = "GetRepositoryPolicy"

// GetRepositoryPolicyRequest generates a "aws/request.Request" representing the
// client's request for the GetRepositoryPolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See GetRepositoryPolicy for more information on using the GetRepositoryPolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the GetRepositoryPolicyRequest method.
//	req, resp := client.GetRepositoryPolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetRepositoryPolicy
func (c *ECR) GetRepositoryPolicyRequest(input *GetRepositoryPolicyInput) (req *request.Request, output *GetRepositoryPolicyOutput) {
	op := &request.Operation{
		Name:       opGetRepositoryPolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &GetRepositoryPolicyInput{}
	}

	output = &GetRepositoryPolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// GetRepositoryPolicy API operation for Amazon EC2 Container Registry.
//
// Retrieves the repository policy for the specified repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation GetRepositoryPolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - RepositoryPolicyNotFoundException
//     The specified repository and registry combination does not have an associated
//     repository policy.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/GetRepositoryPolicy
func (c *ECR) GetRepositoryPolicy(input *GetRepositoryPolicyInput) (*GetRepositoryPolicyOutput, error) {
	req, out := c.GetRepositoryPolicyRequest(input)
	return out, req.Send()
}

// GetRepositoryPolicyWithContext is the same as GetRepositoryPolicy with the addition of
// the ability to pass a context and additional request options.
//
// See GetRepositoryPolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) GetRepositoryPolicyWithContext(ctx aws.Context, input *GetRepositoryPolicyInput, opts ...request.Option) (*GetRepositoryPolicyOutput, error) {
	req, out := c.GetRepositoryPolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opInitiateLayerUpload = "InitiateLayerUpload"

// InitiateLayerUploadRequest generates a "aws/request.Request" representing the
// client's request for the InitiateLayerUpload operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See InitiateLayerUpload for more information on using the InitiateLayerUpload
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the InitiateLayerUploadRequest method.
//	req, resp := client.InitiateLayerUploadRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/InitiateLayerUpload
func (c *ECR) InitiateLayerUploadRequest(input *InitiateLayerUploadInput) (req *request.Request, output *InitiateLayerUploadOutput) {
	op := &request.Operation{
		Name:       opInitiateLayerUpload,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &InitiateLayerUploadInput{}
	}

	output = &InitiateLayerUploadOutput{}
	req = c.newRequest(op, input, output)
	return
}

// InitiateLayerUpload API operation for Amazon EC2 Container Registry.
//
// Notifies Amazon ECR that you intend to upload an image layer.
//
// When an image is pushed, the InitiateLayerUpload API is called once per image
// layer that has not already been uploaded. Whether or not an image layer has
// been uploaded is determined by the BatchCheckLayerAvailability API action.
//
// This operation is used by the Amazon ECR proxy and is not generally used
// by customers for pulling and pushing images. In most cases, you should use
// the docker CLI to pull, tag, and push images.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation InitiateLayerUpload for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - KmsException
//     The operation failed due to a KMS exception.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/InitiateLayerUpload
func (c *ECR) InitiateLayerUpload(input *InitiateLayerUploadInput) (*InitiateLayerUploadOutput, error) {
	req, out := c.InitiateLayerUploadRequest(input)
	return out, req.Send()
}

// InitiateLayerUploadWithContext is the same as InitiateLayerUpload with the addition of
// the ability to pass a context and additional request options.
//
// See InitiateLayerUpload for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) InitiateLayerUploadWithContext(ctx aws.Context, input *InitiateLayerUploadInput, opts ...request.Option) (*InitiateLayerUploadOutput, error) {
	req, out := c.InitiateLayerUploadRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opListImages = "ListImages"

// ListImagesRequest generates a "aws/request.Request" representing the
// client's request for the ListImages operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See ListImages for more information on using the ListImages
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the ListImagesRequest method.
//	req, resp := client.ListImagesRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/ListImages
func (c *ECR) ListImagesRequest(input *ListImagesInput) (req *request.Request, output *ListImagesOutput) {
	op := &request.Operation{
		Name:       opListImages,
		HTTPMethod: "POST",
		HTTPPath:   "/",
		Paginator: &request.Paginator{
			InputTokens:     []string{"nextToken"},
			OutputTokens:    []string{"nextToken"},
			LimitToken:      "maxResults",
			TruncationToken: "",
		},
	}

	if input == nil {
		input = &ListImagesInput{}
	}

	output = &ListImagesOutput{}
	req = c.newRequest(op, input, output)
	return
}

// ListImages API operation for Amazon EC2 Container Registry.
//
// Lists all the image IDs for the specified repository.
//
// You can filter images based on whether or not they are tagged by using the
// tagStatus filter and specifying either TAGGED, UNTAGGED or ANY. For example,
// you can filter your results to return only UNTAGGED images and then pipe
// that result to a BatchDeleteImage operation to delete them. Or, you can filter
// your results to return only TAGGED images to list all of the tags in your
// repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation ListImages for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/ListImages
func (c *ECR) ListImages(input *ListImagesInput) (*ListImagesOutput, error) {
	req, out := c.ListImagesRequest(input)
	return out, req.Send()
}

// ListImagesWithContext is the same as ListImages with the addition of
// the ability to pass a context and additional request options.
//
// See ListImages for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) ListImagesWithContext(ctx aws.Context, input *ListImagesInput, opts ...request.Option) (*ListImagesOutput, error) {
	req, out := c.ListImagesRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// ListImagesPages iterates over the pages of a ListImages operation,
// calling the "fn" function with the response data for each page. To stop
// iterating, return false from the fn function.
//
// See ListImages method for more information on how to use this operation.
//
// Note: This operation can generate multiple requests to a service.
//
//	// Example iterating over at most 3 pages of a ListImages operation.
//	pageNum := 0
//	err := client.ListImagesPages(params,
//	    func(page *ecr.ListImagesOutput, lastPage bool) bool {
//	        pageNum++
//	        fmt.Println(page)
//	        return pageNum <= 3
//	    })
func (c *ECR) ListImagesPages(input *ListImagesInput, fn func(*ListImagesOutput, bool) bool) error {
	return c.ListImagesPagesWithContext(aws.BackgroundContext(), input, fn)
}

// ListImagesPagesWithContext same as ListImagesPages except
// it takes a Context and allows setting request options on the pages.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) ListImagesPagesWithContext(ctx aws.Context, input *ListImagesInput, fn func(*ListImagesOutput, bool) bool, opts ...request.Option) error {
	p := request.Pagination{
		NewRequest: func() (*request.Request, error) {
			var inCpy *ListImagesInput
			if input != nil {
				tmp := *input
				inCpy = &tmp
			}
			req, _ := c.ListImagesRequest(inCpy)
			req.SetContext(ctx)
			req.ApplyOptions(opts...)
			return req, nil
		},
	}

	for p.Next() {
		if !fn(p.Page().(*ListImagesOutput), !p.HasNextPage()) {
			break
		}
	}

	return p.Err()
}

const opListTagsForResource = "ListTagsForResource"

// ListTagsForResourceRequest generates a "aws/request.Request" representing the
// client's request for the ListTagsForResource operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See ListTagsForResource for more information on using the ListTagsForResource
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the ListTagsForResourceRequest method.
//	req, resp := client.ListTagsForResourceRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/ListTagsForResource
func (c *ECR) ListTagsForResourceRequest(input *ListTagsForResourceInput) (req *request.Request, output *ListTagsForResourceOutput) {
	op := &request.Operation{
		Name:       opListTagsForResource,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &ListTagsForResourceInput{}
	}

	output = &ListTagsForResourceOutput{}
	req = c.newRequest(op, input, output)
	return
}

// ListTagsForResource API operation for Amazon EC2 Container Registry.
//
// List the tags for an Amazon ECR resource.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation ListTagsForResource for usage and error information.
//
// Returned Error Types:
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/ListTagsForResource
func (c *ECR) ListTagsForResource(input *ListTagsForResourceInput) (*ListTagsForResourceOutput, error) {
	req, out := c.ListTagsForResourceRequest(input)
	return out, req.Send()
}

// ListTagsForResourceWithContext is the same as ListTagsForResource with the addition of
// the ability to pass a context and additional request options.
//
// See ListTagsForResource for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) ListTagsForResourceWithContext(ctx aws.Context, input *ListTagsForResourceInput, opts ...request.Option) (*ListTagsForResourceOutput, error) {
	req, out := c.ListTagsForResourceRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opPutImage = "PutImage"

// PutImageRequest generates a "aws/request.Request" representing the
// client's request for the PutImage operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See PutImage for more information on using the PutImage
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the PutImageRequest method.
//	req, resp := client.PutImageRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutImage
func (c *ECR) PutImageRequest(input *PutImageInput) (req *request.Request, output *PutImageOutput) {
	op := &request.Operation{
		Name:       opPutImage,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &PutImageInput{}
	}

	output = &PutImageOutput{}
	req = c.newRequest(op, input, output)
	return
}

// PutImage API operation for Amazon EC2 Container Registry.
//
// Creates or updates the image manifest and tags associated with an image.
//
// When an image is pushed and all new image layers have been uploaded, the
// PutImage API is called once to create or update the image manifest and the
// tags associated with the image.
//
// This operation is used by the Amazon ECR proxy and is not generally used
// by customers for pulling and pushing images. In most cases, you should use
// the docker CLI to pull, tag, and push images.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation PutImage for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ImageAlreadyExistsException
//     The specified image has already been pushed, and there were no changes to
//     the manifest or image tag after the last push.
//
//   - LayersNotFoundException
//     The specified layers could not be found, or the specified layer is not valid
//     for this repository.
//
//   - ReferencedImagesNotFoundException
//     The manifest list is referencing an image that does not exist.
//
//   - LimitExceededException
//     The operation did not succeed because it would have exceeded a service limit
//     for your account. For more information, see Amazon ECR service quotas (https://docs.aws.amazon.com/AmazonECR/latest/userguide/service-quotas.html)
//     in the Amazon Elastic Container Registry User Guide.
//
//   - ImageTagAlreadyExistsException
//     The specified image is tagged with a tag that already exists. The repository
//     is configured for tag immutability.
//
//   - ImageDigestDoesNotMatchException
//     The specified image digest does not match the digest that Amazon ECR calculated
//     for the image.
//
//   - KmsException
//     The operation failed due to a KMS exception.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutImage
func (c *ECR) PutImage(input *PutImageInput) (*PutImageOutput, error) {
	req, out := c.PutImageRequest(input)
	return out, req.Send()
}

// PutImageWithContext is the same as PutImage with the addition of
// the ability to pass a context and additional request options.
//
// See PutImage for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) PutImageWithContext(ctx aws.Context, input *PutImageInput, opts ...request.Option) (*PutImageOutput, error) {
	req, out := c.PutImageRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opPutImageScanningConfiguration = "PutImageScanningConfiguration"

// PutImageScanningConfigurationRequest generates a "aws/request.Request" representing the
// client's request for the PutImageScanningConfiguration operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See PutImageScanningConfiguration for more information on using the PutImageScanningConfiguration
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the PutImageScanningConfigurationRequest method.
//	req, resp := client.PutImageScanningConfigurationRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutImageScanningConfiguration
func (c *ECR) PutImageScanningConfigurationRequest(input *PutImageScanningConfigurationInput) (req *request.Request, output *PutImageScanningConfigurationOutput) {
	op := &request.Operation{
		Name:       opPutImageScanningConfiguration,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &PutImageScanningConfigurationInput{}
	}

	output = &PutImageScanningConfigurationOutput{}
	req = c.newRequest(op, input, output)
	return
}

// PutImageScanningConfiguration API operation for Amazon EC2 Container Registry.
//
// The PutImageScanningConfiguration API is being deprecated, in favor of specifying
// the image scanning configuration at the registry level. For more information,
// see PutRegistryScanningConfiguration.
//
// Updates the image scanning configuration for the specified repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation PutImageScanningConfiguration for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutImageScanningConfiguration
func (c *ECR) PutImageScanningConfiguration(input *PutImageScanningConfigurationInput) (*PutImageScanningConfigurationOutput, error) {
	req, out := c.PutImageScanningConfigurationRequest(input)
	return out, req.Send()
}

// PutImageScanningConfigurationWithContext is the same as PutImageScanningConfiguration with the addition of
// the ability to pass a context and additional request options.
//
// See PutImageScanningConfiguration for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) PutImageScanningConfigurationWithContext(ctx aws.Context, input *PutImageScanningConfigurationInput, opts ...request.Option) (*PutImageScanningConfigurationOutput, error) {
	req, out := c.PutImageScanningConfigurationRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opPutImageTagMutability = "PutImageTagMutability"

// PutImageTagMutabilityRequest generates a "aws/request.Request" representing the
// client's request for the PutImageTagMutability operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See PutImageTagMutability for more information on using the PutImageTagMutability
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the PutImageTagMutabilityRequest method.
//	req, resp := client.PutImageTagMutabilityRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutImageTagMutability
func (c *ECR) PutImageTagMutabilityRequest(input *PutImageTagMutabilityInput) (req *request.Request, output *PutImageTagMutabilityOutput) {
	op := &request.Operation{
		Name:       opPutImageTagMutability,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &PutImageTagMutabilityInput{}
	}

	output = &PutImageTagMutabilityOutput{}
	req = c.newRequest(op, input, output)
	return
}

// PutImageTagMutability API operation for Amazon EC2 Container Registry.
//
// Updates the image tag mutability settings for the specified repository. For
// more information, see Image tag mutability (https://docs.aws.amazon.com/AmazonECR/latest/userguide/image-tag-mutability.html)
// in the Amazon Elastic Container Registry User Guide.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation PutImageTagMutability for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutImageTagMutability
func (c *ECR) PutImageTagMutability(input *PutImageTagMutabilityInput) (*PutImageTagMutabilityOutput, error) {
	req, out := c.PutImageTagMutabilityRequest(input)
	return out, req.Send()
}

// PutImageTagMutabilityWithContext is the same as PutImageTagMutability with the addition of
// the ability to pass a context and additional request options.
//
// See PutImageTagMutability for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) PutImageTagMutabilityWithContext(ctx aws.Context, input *PutImageTagMutabilityInput, opts ...request.Option) (*PutImageTagMutabilityOutput, error) {
	req, out := c.PutImageTagMutabilityRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opPutLifecyclePolicy = "PutLifecyclePolicy"

// PutLifecyclePolicyRequest generates a "aws/request.Request" representing the
// client's request for the PutLifecyclePolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See PutLifecyclePolicy for more information on using the PutLifecyclePolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the PutLifecyclePolicyRequest method.
//	req, resp := client.PutLifecyclePolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutLifecyclePolicy
func (c *ECR) PutLifecyclePolicyRequest(input *PutLifecyclePolicyInput) (req *request.Request, output *PutLifecyclePolicyOutput) {
	op := &request.Operation{
		Name:       opPutLifecyclePolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &PutLifecyclePolicyInput{}
	}

	output = &PutLifecyclePolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// PutLifecyclePolicy API operation for Amazon EC2 Container Registry.
//
// Creates or updates the lifecycle policy for the specified repository. For
// more information, see Lifecycle policy template (https://docs.aws.amazon.com/AmazonECR/latest/userguide/LifecyclePolicies.html).
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation PutLifecyclePolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutLifecyclePolicy
func (c *ECR) PutLifecyclePolicy(input *PutLifecyclePolicyInput) (*PutLifecyclePolicyOutput, error) {
	req, out := c.PutLifecyclePolicyRequest(input)
	return out, req.Send()
}

// PutLifecyclePolicyWithContext is the same as PutLifecyclePolicy with the addition of
// the ability to pass a context and additional request options.
//
// See PutLifecyclePolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) PutLifecyclePolicyWithContext(ctx aws.Context, input *PutLifecyclePolicyInput, opts ...request.Option) (*PutLifecyclePolicyOutput, error) {
	req, out := c.PutLifecyclePolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opPutRegistryPolicy = "PutRegistryPolicy"

// PutRegistryPolicyRequest generates a "aws/request.Request" representing the
// client's request for the PutRegistryPolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See PutRegistryPolicy for more information on using the PutRegistryPolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the PutRegistryPolicyRequest method.
//	req, resp := client.PutRegistryPolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutRegistryPolicy
func (c *ECR) PutRegistryPolicyRequest(input *PutRegistryPolicyInput) (req *request.Request, output *PutRegistryPolicyOutput) {
	op := &request.Operation{
		Name:       opPutRegistryPolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &PutRegistryPolicyInput{}
	}

	output = &PutRegistryPolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// PutRegistryPolicy API operation for Amazon EC2 Container Registry.
//
// Creates or updates the permissions policy for your registry.
//
// A registry policy is used to specify permissions for another Amazon Web Services
// account and is used when configuring cross-account replication. For more
// information, see Registry permissions (https://docs.aws.amazon.com/AmazonECR/latest/userguide/registry-permissions.html)
// in the Amazon Elastic Container Registry User Guide.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation PutRegistryPolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutRegistryPolicy
func (c *ECR) PutRegistryPolicy(input *PutRegistryPolicyInput) (*PutRegistryPolicyOutput, error) {
	req, out := c.PutRegistryPolicyRequest(input)
	return out, req.Send()
}

// PutRegistryPolicyWithContext is the same as PutRegistryPolicy with the addition of
// the ability to pass a context and additional request options.
//
// See PutRegistryPolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) PutRegistryPolicyWithContext(ctx aws.Context, input *PutRegistryPolicyInput, opts ...request.Option) (*PutRegistryPolicyOutput, error) {
	req, out := c.PutRegistryPolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opPutRegistryScanningConfiguration = "PutRegistryScanningConfiguration"

// PutRegistryScanningConfigurationRequest generates a "aws/request.Request" representing the
// client's request for the PutRegistryScanningConfiguration operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See PutRegistryScanningConfiguration for more information on using the PutRegistryScanningConfiguration
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the PutRegistryScanningConfigurationRequest method.
//	req, resp := client.PutRegistryScanningConfigurationRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutRegistryScanningConfiguration
func (c *ECR) PutRegistryScanningConfigurationRequest(input *PutRegistryScanningConfigurationInput) (req *request.Request, output *PutRegistryScanningConfigurationOutput) {
	op := &request.Operation{
		Name:       opPutRegistryScanningConfiguration,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &PutRegistryScanningConfigurationInput{}
	}

	output = &PutRegistryScanningConfigurationOutput{}
	req = c.newRequest(op, input, output)
	return
}

// PutRegistryScanningConfiguration API operation for Amazon EC2 Container Registry.
//
// Creates or updates the scanning configuration for your private registry.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation PutRegistryScanningConfiguration for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutRegistryScanningConfiguration
func (c *ECR) PutRegistryScanningConfiguration(input *PutRegistryScanningConfigurationInput) (*PutRegistryScanningConfigurationOutput, error) {
	req, out := c.PutRegistryScanningConfigurationRequest(input)
	return out, req.Send()
}

// PutRegistryScanningConfigurationWithContext is the same as PutRegistryScanningConfiguration with the addition of
// the ability to pass a context and additional request options.
//
// See PutRegistryScanningConfiguration for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) PutRegistryScanningConfigurationWithContext(ctx aws.Context, input *PutRegistryScanningConfigurationInput, opts ...request.Option) (*PutRegistryScanningConfigurationOutput, error) {
	req, out := c.PutRegistryScanningConfigurationRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opPutReplicationConfiguration = "PutReplicationConfiguration"

// PutReplicationConfigurationRequest generates a "aws/request.Request" representing the
// client's request for the PutReplicationConfiguration operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See PutReplicationConfiguration for more information on using the PutReplicationConfiguration
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the PutReplicationConfigurationRequest method.
//	req, resp := client.PutReplicationConfigurationRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutReplicationConfiguration
func (c *ECR) PutReplicationConfigurationRequest(input *PutReplicationConfigurationInput) (req *request.Request, output *PutReplicationConfigurationOutput) {
	op := &request.Operation{
		Name:       opPutReplicationConfiguration,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &PutReplicationConfigurationInput{}
	}

	output = &PutReplicationConfigurationOutput{}
	req = c.newRequest(op, input, output)
	return
}

// PutReplicationConfiguration API operation for Amazon EC2 Container Registry.
//
// Creates or updates the replication configuration for a registry. The existing
// replication configuration for a repository can be retrieved with the DescribeRegistry
// API action. The first time the PutReplicationConfiguration API is called,
// a service-linked IAM role is created in your account for the replication
// process. For more information, see Using service-linked roles for Amazon
// ECR (https://docs.aws.amazon.com/AmazonECR/latest/userguide/using-service-linked-roles.html)
// in the Amazon Elastic Container Registry User Guide.
//
// When configuring cross-account replication, the destination account must
// grant the source account permission to replicate. This permission is controlled
// using a registry permissions policy. For more information, see PutRegistryPolicy.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation PutReplicationConfiguration for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/PutReplicationConfiguration
func (c *ECR) PutReplicationConfiguration(input *PutReplicationConfigurationInput) (*PutReplicationConfigurationOutput, error) {
	req, out := c.PutReplicationConfigurationRequest(input)
	return out, req.Send()
}

// PutReplicationConfigurationWithContext is the same as PutReplicationConfiguration with the addition of
// the ability to pass a context and additional request options.
//
// See PutReplicationConfiguration for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) PutReplicationConfigurationWithContext(ctx aws.Context, input *PutReplicationConfigurationInput, opts ...request.Option) (*PutReplicationConfigurationOutput, error) {
	req, out := c.PutReplicationConfigurationRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opSetRepositoryPolicy = "SetRepositoryPolicy"

// SetRepositoryPolicyRequest generates a "aws/request.Request" representing the
// client's request for the SetRepositoryPolicy operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See SetRepositoryPolicy for more information on using the SetRepositoryPolicy
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the SetRepositoryPolicyRequest method.
//	req, resp := client.SetRepositoryPolicyRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/SetRepositoryPolicy
func (c *ECR) SetRepositoryPolicyRequest(input *SetRepositoryPolicyInput) (req *request.Request, output *SetRepositoryPolicyOutput) {
	op := &request.Operation{
		Name:       opSetRepositoryPolicy,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &SetRepositoryPolicyInput{}
	}

	output = &SetRepositoryPolicyOutput{}
	req = c.newRequest(op, input, output)
	return
}

// SetRepositoryPolicy API operation for Amazon EC2 Container Registry.
//
// Applies a repository policy to the specified repository to control access
// permissions. For more information, see Amazon ECR Repository policies (https://docs.aws.amazon.com/AmazonECR/latest/userguide/repository-policies.html)
// in the Amazon Elastic Container Registry User Guide.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation SetRepositoryPolicy for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/SetRepositoryPolicy
func (c *ECR) SetRepositoryPolicy(input *SetRepositoryPolicyInput) (*SetRepositoryPolicyOutput, error) {
	req, out := c.SetRepositoryPolicyRequest(input)
	return out, req.Send()
}

// SetRepositoryPolicyWithContext is the same as SetRepositoryPolicy with the addition of
// the ability to pass a context and additional request options.
//
// See SetRepositoryPolicy for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) SetRepositoryPolicyWithContext(ctx aws.Context, input *SetRepositoryPolicyInput, opts ...request.Option) (*SetRepositoryPolicyOutput, error) {
	req, out := c.SetRepositoryPolicyRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opStartImageScan = "StartImageScan"

// StartImageScanRequest generates a "aws/request.Request" representing the
// client's request for the StartImageScan operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See StartImageScan for more information on using the StartImageScan
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the StartImageScanRequest method.
//	req, resp := client.StartImageScanRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/StartImageScan
func (c *ECR) StartImageScanRequest(input *StartImageScanInput) (req *request.Request, output *StartImageScanOutput) {
	op := &request.Operation{
		Name:       opStartImageScan,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &StartImageScanInput{}
	}

	output = &StartImageScanOutput{}
	req = c.newRequest(op, input, output)
	return
}

// StartImageScan API operation for Amazon EC2 Container Registry.
//
// Starts an image vulnerability scan. An image scan can only be started once
// per 24 hours on an individual image. This limit includes if an image was
// scanned on initial push. For more information, see Image scanning (https://docs.aws.amazon.com/AmazonECR/latest/userguide/image-scanning.html)
// in the Amazon Elastic Container Registry User Guide.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation StartImageScan for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - UnsupportedImageTypeException
//     The image is of a type that cannot be scanned.
//
//   - LimitExceededException
//     The operation did not succeed because it would have exceeded a service limit
//     for your account. For more information, see Amazon ECR service quotas (https://docs.aws.amazon.com/AmazonECR/latest/userguide/service-quotas.html)
//     in the Amazon Elastic Container Registry User Guide.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ImageNotFoundException
//     The image requested does not exist in the specified repository.
//
//   - ValidationException
//     There was an exception validating this request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/StartImageScan
func (c *ECR) StartImageScan(input *StartImageScanInput) (*StartImageScanOutput, error) {
	req, out := c.StartImageScanRequest(input)
	return out, req.Send()
}

// StartImageScanWithContext is the same as StartImageScan with the addition of
// the ability to pass a context and additional request options.
//
// See StartImageScan for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) StartImageScanWithContext(ctx aws.Context, input *StartImageScanInput, opts ...request.Option) (*StartImageScanOutput, error) {
	req, out := c.StartImageScanRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opStartLifecyclePolicyPreview = "StartLifecyclePolicyPreview"

// StartLifecyclePolicyPreviewRequest generates a "aws/request.Request" representing the
// client's request for the StartLifecyclePolicyPreview operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See StartLifecyclePolicyPreview for more information on using the StartLifecyclePolicyPreview
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the StartLifecyclePolicyPreviewRequest method.
//	req, resp := client.StartLifecyclePolicyPreviewRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/StartLifecyclePolicyPreview
func (c *ECR) StartLifecyclePolicyPreviewRequest(input *StartLifecyclePolicyPreviewInput) (req *request.Request, output *StartLifecyclePolicyPreviewOutput) {
	op := &request.Operation{
		Name:       opStartLifecyclePolicyPreview,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &StartLifecyclePolicyPreviewInput{}
	}

	output = &StartLifecyclePolicyPreviewOutput{}
	req = c.newRequest(op, input, output)
	return
}

// StartLifecyclePolicyPreview API operation for Amazon EC2 Container Registry.
//
// Starts a preview of a lifecycle policy for the specified repository. This
// allows you to see the results before associating the lifecycle policy with
// the repository.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation StartLifecyclePolicyPreview for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - LifecyclePolicyNotFoundException
//     The lifecycle policy could not be found, and no policy is set to the repository.
//
//   - LifecyclePolicyPreviewInProgressException
//     The previous lifecycle policy preview request has not completed. Wait and
//     try again.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/StartLifecyclePolicyPreview
func (c *ECR) StartLifecyclePolicyPreview(input *StartLifecyclePolicyPreviewInput) (*StartLifecyclePolicyPreviewOutput, error) {
	req, out := c.StartLifecyclePolicyPreviewRequest(input)
	return out, req.Send()
}

// StartLifecyclePolicyPreviewWithContext is the same as StartLifecyclePolicyPreview with the addition of
// the ability to pass a context and additional request options.
//
// See StartLifecyclePolicyPreview for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) StartLifecyclePolicyPreviewWithContext(ctx aws.Context, input *StartLifecyclePolicyPreviewInput, opts ...request.Option) (*StartLifecyclePolicyPreviewOutput, error) {
	req, out := c.StartLifecyclePolicyPreviewRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opTagResource = "TagResource"

// TagResourceRequest generates a "aws/request.Request" representing the
// client's request for the TagResource operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See TagResource for more information on using the TagResource
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the TagResourceRequest method.
//	req, resp := client.TagResourceRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/TagResource
func (c *ECR) TagResourceRequest(input *TagResourceInput) (req *request.Request, output *TagResourceOutput) {
	op := &request.Operation{
		Name:       opTagResource,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &TagResourceInput{}
	}

	output = &TagResourceOutput{}
	req = c.newRequest(op, input, output)
	req.Handlers.Unmarshal.Swap(jsonrpc.UnmarshalHandler.Name, protocol.UnmarshalDiscardBodyHandler)
	return
}

// TagResource API operation for Amazon EC2 Container Registry.
//
// Adds specified tags to a resource with the specified ARN. Existing tags on
// a resource are not changed if they are not specified in the request parameters.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation TagResource for usage and error information.
//
// Returned Error Types:
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - InvalidTagParameterException
//     An invalid parameter has been specified. Tag keys can have a maximum character
//     length of 128 characters, and tag values can have a maximum length of 256
//     characters.
//
//   - TooManyTagsException
//     The list of tags on the repository is over the limit. The maximum number
//     of tags that can be applied to a repository is 50.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/TagResource
func (c *ECR) TagResource(input *TagResourceInput) (*TagResourceOutput, error) {
	req, out := c.TagResourceRequest(input)
	return out, req.Send()
}

// TagResourceWithContext is the same as TagResource with the addition of
// the ability to pass a context and additional request options.
//
// See TagResource for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) TagResourceWithContext(ctx aws.Context, input *TagResourceInput, opts ...request.Option) (*TagResourceOutput, error) {
	req, out := c.TagResourceRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opUntagResource = "UntagResource"

// UntagResourceRequest generates a "aws/request.Request" representing the
// client's request for the UntagResource operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See UntagResource for more information on using the UntagResource
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the UntagResourceRequest method.
//	req, resp := client.UntagResourceRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/UntagResource
func (c *ECR) UntagResourceRequest(input *UntagResourceInput) (req *request.Request, output *UntagResourceOutput) {
	op := &request.Operation{
		Name:       opUntagResource,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &UntagResourceInput{}
	}

	output = &UntagResourceOutput{}
	req = c.newRequest(op, input, output)
	req.Handlers.Unmarshal.Swap(jsonrpc.UnmarshalHandler.Name, protocol.UnmarshalDiscardBodyHandler)
	return
}

// UntagResource API operation for Amazon EC2 Container Registry.
//
// Deletes specified tags from a resource.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation UntagResource for usage and error information.
//
// Returned Error Types:
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - InvalidTagParameterException
//     An invalid parameter has been specified. Tag keys can have a maximum character
//     length of 128 characters, and tag values can have a maximum length of 256
//     characters.
//
//   - TooManyTagsException
//     The list of tags on the repository is over the limit. The maximum number
//     of tags that can be applied to a repository is 50.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/UntagResource
func (c *ECR) UntagResource(input *UntagResourceInput) (*UntagResourceOutput, error) {
	req, out := c.UntagResourceRequest(input)
	return out, req.Send()
}

// UntagResourceWithContext is the same as UntagResource with the addition of
// the ability to pass a context and additional request options.
//
// See UntagResource for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) UntagResourceWithContext(ctx aws.Context, input *UntagResourceInput, opts ...request.Option) (*UntagResourceOutput, error) {
	req, out := c.UntagResourceRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opUploadLayerPart = "UploadLayerPart"

// UploadLayerPartRequest generates a "aws/request.Request" representing the
// client's request for the UploadLayerPart operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See UploadLayerPart for more information on using the UploadLayerPart
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the UploadLayerPartRequest method.
//	req, resp := client.UploadLayerPartRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/UploadLayerPart
func (c *ECR) UploadLayerPartRequest(input *UploadLayerPartInput) (req *request.Request, output *UploadLayerPartOutput) {
	op := &request.Operation{
		Name:       opUploadLayerPart,
		HTTPMethod: "POST",
		HTTPPath:   "/",
	}

	if input == nil {
		input = &UploadLayerPartInput{}
	}

	output = &UploadLayerPartOutput{}
	req = c.newRequest(op, input, output)
	return
}

// UploadLayerPart API operation for Amazon EC2 Container Registry.
//
// Uploads an image layer part to Amazon ECR.
//
// When an image is pushed, each new image layer is uploaded in parts. The maximum
// size of each image layer part can be 20971520 bytes (or about 20MB). The
// UploadLayerPart API is called once per each new image layer part.
//
// This operation is used by the Amazon ECR proxy and is not generally used
// by customers for pulling and pushing images. In most cases, you should use
// the docker CLI to pull, tag, and push images.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for Amazon EC2 Container Registry's
// API operation UploadLayerPart for usage and error information.
//
// Returned Error Types:
//
//   - ServerException
//     These errors are usually caused by a server-side issue.
//
//   - InvalidParameterException
//     The specified parameter is invalid. Review the available parameters for the
//     API request.
//
//   - InvalidLayerPartException
//     The layer part size is not valid, or the first byte specified is not consecutive
//     to the last byte of a previous layer part upload.
//
//   - RepositoryNotFoundException
//     The specified repository could not be found. Check the spelling of the specified
//     repository and ensure that you are performing operations on the correct registry.
//
//   - UploadNotFoundException
//     The upload could not be found, or the specified upload ID is not valid for
//     this repository.
//
//   - LimitExceededException
//     The operation did not succeed because it would have exceeded a service limit
//     for your account. For more information, see Amazon ECR service quotas (https://docs.aws.amazon.com/AmazonECR/latest/userguide/service-quotas.html)
//     in the Amazon Elastic Container Registry User Guide.
//
//   - KmsException
//     The operation failed due to a KMS exception.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/ecr-2015-09-21/UploadLayerPart
func (c *ECR) UploadLayerPart(input *UploadLayerPartInput) (*UploadLayerPartOutput, error) {
	req, out := c.UploadLayerPartRequest(input)
	return out, req.Send()
}

// UploadLayerPartWithContext is the same as UploadLayerPart with the addition of
// the ability to pass a context and additional request options.
//
// See UploadLayerPart for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *ECR) UploadLayerPartWithContext(ctx aws.Context, input *UploadLayerPartInput, opts ...request.Option) (*UploadLayerPartOutput, error) {
	req, out := c.UploadLayerPartRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// This data type is used in the ImageScanFinding data type.
type Attribute struct {
	_ struct{} `type:"structure"`

	// The attribute key.
	//
	// Key is a required field
	Key *string `locationName:"key" min:"1" type:"string" required:"true"`

	// The value assigned to the attribute key.
	Value *string `locationName:"value" min:"1" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Attribute) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Attribute) GoString() string {
	return s.String()
}

// SetKey sets the Key field's value.
func (s *Attribute) SetKey(v string) *Attribute {
	s.Key = &v
	return s
}

// SetValue sets the Value field's value.
func (s *Attribute) SetValue(v string) *Attribute {
	s.Value = &v
	return s
}

// An object representing authorization data for an Amazon ECR registry.
type AuthorizationData struct {
	_ struct{} `type:"structure"`

	// A base64-encoded string that contains authorization data for the specified
	// Amazon ECR registry. When the string is decoded, it is presented in the format
	// user:password for private registry authentication using docker login.
	AuthorizationToken *string `locationName:"authorizationToken" type:"string"`

	// The Unix time in seconds and milliseconds when the authorization token expires.
	// Authorization tokens are valid for 12 hours.
	ExpiresAt *time.Time `locationName:"expiresAt" type:"timestamp"`

	// The registry URL to use for this authorization token in a docker login command.
	// The Amazon ECR registry URL format is https://aws_account_id.dkr.ecr.region.amazonaws.com.
	// For example, https://************.dkr.ecr.us-east-1.amazonaws.com..
	ProxyEndpoint *string `locationName:"proxyEndpoint" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s AuthorizationData) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s AuthorizationData) GoString() string {
	return s.String()
}

// SetAuthorizationToken sets the AuthorizationToken field's value.
func (s *AuthorizationData) SetAuthorizationToken(v string) *AuthorizationData {
	s.AuthorizationToken = &v
	return s
}

// SetExpiresAt sets the ExpiresAt field's value.
func (s *AuthorizationData) SetExpiresAt(v time.Time) *AuthorizationData {
	s.ExpiresAt = &v
	return s
}

// SetProxyEndpoint sets the ProxyEndpoint field's value.
func (s *AuthorizationData) SetProxyEndpoint(v string) *AuthorizationData {
	s.ProxyEndpoint = &v
	return s
}

// The image details of the Amazon ECR container image.
type AwsEcrContainerImageDetails struct {
	_ struct{} `type:"structure"`

	// The architecture of the Amazon ECR container image.
	Architecture *string `locationName:"architecture" type:"string"`

	// The image author of the Amazon ECR container image.
	Author *string `locationName:"author" type:"string"`

	// The image hash of the Amazon ECR container image.
	ImageHash *string `locationName:"imageHash" type:"string"`

	// The image tags attached to the Amazon ECR container image.
	ImageTags []*string `locationName:"imageTags" type:"list"`

	// The platform of the Amazon ECR container image.
	Platform *string `locationName:"platform" type:"string"`

	// The date and time the Amazon ECR container image was pushed.
	PushedAt *time.Time `locationName:"pushedAt" type:"timestamp"`

	// The registry the Amazon ECR container image belongs to.
	Registry *string `locationName:"registry" type:"string"`

	// The name of the repository the Amazon ECR container image resides in.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s AwsEcrContainerImageDetails) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s AwsEcrContainerImageDetails) GoString() string {
	return s.String()
}

// SetArchitecture sets the Architecture field's value.
func (s *AwsEcrContainerImageDetails) SetArchitecture(v string) *AwsEcrContainerImageDetails {
	s.Architecture = &v
	return s
}

// SetAuthor sets the Author field's value.
func (s *AwsEcrContainerImageDetails) SetAuthor(v string) *AwsEcrContainerImageDetails {
	s.Author = &v
	return s
}

// SetImageHash sets the ImageHash field's value.
func (s *AwsEcrContainerImageDetails) SetImageHash(v string) *AwsEcrContainerImageDetails {
	s.ImageHash = &v
	return s
}

// SetImageTags sets the ImageTags field's value.
func (s *AwsEcrContainerImageDetails) SetImageTags(v []*string) *AwsEcrContainerImageDetails {
	s.ImageTags = v
	return s
}

// SetPlatform sets the Platform field's value.
func (s *AwsEcrContainerImageDetails) SetPlatform(v string) *AwsEcrContainerImageDetails {
	s.Platform = &v
	return s
}

// SetPushedAt sets the PushedAt field's value.
func (s *AwsEcrContainerImageDetails) SetPushedAt(v time.Time) *AwsEcrContainerImageDetails {
	s.PushedAt = &v
	return s
}

// SetRegistry sets the Registry field's value.
func (s *AwsEcrContainerImageDetails) SetRegistry(v string) *AwsEcrContainerImageDetails {
	s.Registry = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *AwsEcrContainerImageDetails) SetRepositoryName(v string) *AwsEcrContainerImageDetails {
	s.RepositoryName = &v
	return s
}

type BatchCheckLayerAvailabilityInput struct {
	_ struct{} `type:"structure"`

	// The digests of the image layers to check.
	//
	// LayerDigests is a required field
	LayerDigests []*string `locationName:"layerDigests" min:"1" type:"list" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the image layers to check. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository that is associated with the image layers to check.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchCheckLayerAvailabilityInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchCheckLayerAvailabilityInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *BatchCheckLayerAvailabilityInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "BatchCheckLayerAvailabilityInput"}
	if s.LayerDigests == nil {
		invalidParams.Add(request.NewErrParamRequired("LayerDigests"))
	}
	if s.LayerDigests != nil && len(s.LayerDigests) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("LayerDigests", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetLayerDigests sets the LayerDigests field's value.
func (s *BatchCheckLayerAvailabilityInput) SetLayerDigests(v []*string) *BatchCheckLayerAvailabilityInput {
	s.LayerDigests = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *BatchCheckLayerAvailabilityInput) SetRegistryId(v string) *BatchCheckLayerAvailabilityInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *BatchCheckLayerAvailabilityInput) SetRepositoryName(v string) *BatchCheckLayerAvailabilityInput {
	s.RepositoryName = &v
	return s
}

type BatchCheckLayerAvailabilityOutput struct {
	_ struct{} `type:"structure"`

	// Any failures associated with the call.
	Failures []*LayerFailure `locationName:"failures" type:"list"`

	// A list of image layer objects corresponding to the image layer references
	// in the request.
	Layers []*Layer `locationName:"layers" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchCheckLayerAvailabilityOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchCheckLayerAvailabilityOutput) GoString() string {
	return s.String()
}

// SetFailures sets the Failures field's value.
func (s *BatchCheckLayerAvailabilityOutput) SetFailures(v []*LayerFailure) *BatchCheckLayerAvailabilityOutput {
	s.Failures = v
	return s
}

// SetLayers sets the Layers field's value.
func (s *BatchCheckLayerAvailabilityOutput) SetLayers(v []*Layer) *BatchCheckLayerAvailabilityOutput {
	s.Layers = v
	return s
}

// Deletes specified images within a specified repository. Images are specified
// with either the imageTag or imageDigest.
type BatchDeleteImageInput struct {
	_ struct{} `type:"structure"`

	// A list of image ID references that correspond to images to delete. The format
	// of the imageIds reference is imageTag=tag or imageDigest=digest.
	//
	// ImageIds is a required field
	ImageIds []*ImageIdentifier `locationName:"imageIds" min:"1" type:"list" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the image to delete. If you do not specify a registry, the default registry
	// is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository that contains the image to delete.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchDeleteImageInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchDeleteImageInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *BatchDeleteImageInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "BatchDeleteImageInput"}
	if s.ImageIds == nil {
		invalidParams.Add(request.NewErrParamRequired("ImageIds"))
	}
	if s.ImageIds != nil && len(s.ImageIds) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ImageIds", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.ImageIds != nil {
		for i, v := range s.ImageIds {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "ImageIds", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetImageIds sets the ImageIds field's value.
func (s *BatchDeleteImageInput) SetImageIds(v []*ImageIdentifier) *BatchDeleteImageInput {
	s.ImageIds = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *BatchDeleteImageInput) SetRegistryId(v string) *BatchDeleteImageInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *BatchDeleteImageInput) SetRepositoryName(v string) *BatchDeleteImageInput {
	s.RepositoryName = &v
	return s
}

type BatchDeleteImageOutput struct {
	_ struct{} `type:"structure"`

	// Any failures associated with the call.
	Failures []*ImageFailure `locationName:"failures" type:"list"`

	// The image IDs of the deleted images.
	ImageIds []*ImageIdentifier `locationName:"imageIds" min:"1" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchDeleteImageOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchDeleteImageOutput) GoString() string {
	return s.String()
}

// SetFailures sets the Failures field's value.
func (s *BatchDeleteImageOutput) SetFailures(v []*ImageFailure) *BatchDeleteImageOutput {
	s.Failures = v
	return s
}

// SetImageIds sets the ImageIds field's value.
func (s *BatchDeleteImageOutput) SetImageIds(v []*ImageIdentifier) *BatchDeleteImageOutput {
	s.ImageIds = v
	return s
}

type BatchGetImageInput struct {
	_ struct{} `type:"structure"`

	// The accepted media types for the request.
	//
	// Valid values: application/vnd.docker.distribution.manifest.v1+json | application/vnd.docker.distribution.manifest.v2+json
	// | application/vnd.oci.image.manifest.v1+json
	AcceptedMediaTypes []*string `locationName:"acceptedMediaTypes" min:"1" type:"list"`

	// A list of image ID references that correspond to images to describe. The
	// format of the imageIds reference is imageTag=tag or imageDigest=digest.
	//
	// ImageIds is a required field
	ImageIds []*ImageIdentifier `locationName:"imageIds" min:"1" type:"list" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the images to describe. If you do not specify a registry, the default registry
	// is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository that contains the images to describe.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchGetImageInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchGetImageInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *BatchGetImageInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "BatchGetImageInput"}
	if s.AcceptedMediaTypes != nil && len(s.AcceptedMediaTypes) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("AcceptedMediaTypes", 1))
	}
	if s.ImageIds == nil {
		invalidParams.Add(request.NewErrParamRequired("ImageIds"))
	}
	if s.ImageIds != nil && len(s.ImageIds) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ImageIds", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.ImageIds != nil {
		for i, v := range s.ImageIds {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "ImageIds", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetAcceptedMediaTypes sets the AcceptedMediaTypes field's value.
func (s *BatchGetImageInput) SetAcceptedMediaTypes(v []*string) *BatchGetImageInput {
	s.AcceptedMediaTypes = v
	return s
}

// SetImageIds sets the ImageIds field's value.
func (s *BatchGetImageInput) SetImageIds(v []*ImageIdentifier) *BatchGetImageInput {
	s.ImageIds = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *BatchGetImageInput) SetRegistryId(v string) *BatchGetImageInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *BatchGetImageInput) SetRepositoryName(v string) *BatchGetImageInput {
	s.RepositoryName = &v
	return s
}

type BatchGetImageOutput struct {
	_ struct{} `type:"structure"`

	// Any failures associated with the call.
	Failures []*ImageFailure `locationName:"failures" type:"list"`

	// A list of image objects corresponding to the image references in the request.
	Images []*Image `locationName:"images" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchGetImageOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchGetImageOutput) GoString() string {
	return s.String()
}

// SetFailures sets the Failures field's value.
func (s *BatchGetImageOutput) SetFailures(v []*ImageFailure) *BatchGetImageOutput {
	s.Failures = v
	return s
}

// SetImages sets the Images field's value.
func (s *BatchGetImageOutput) SetImages(v []*Image) *BatchGetImageOutput {
	s.Images = v
	return s
}

type BatchGetRepositoryScanningConfigurationInput struct {
	_ struct{} `type:"structure"`

	// One or more repository names to get the scanning configuration for.
	//
	// RepositoryNames is a required field
	RepositoryNames []*string `locationName:"repositoryNames" min:"1" type:"list" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchGetRepositoryScanningConfigurationInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchGetRepositoryScanningConfigurationInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *BatchGetRepositoryScanningConfigurationInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "BatchGetRepositoryScanningConfigurationInput"}
	if s.RepositoryNames == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryNames"))
	}
	if s.RepositoryNames != nil && len(s.RepositoryNames) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryNames", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRepositoryNames sets the RepositoryNames field's value.
func (s *BatchGetRepositoryScanningConfigurationInput) SetRepositoryNames(v []*string) *BatchGetRepositoryScanningConfigurationInput {
	s.RepositoryNames = v
	return s
}

type BatchGetRepositoryScanningConfigurationOutput struct {
	_ struct{} `type:"structure"`

	// Any failures associated with the call.
	Failures []*RepositoryScanningConfigurationFailure `locationName:"failures" type:"list"`

	// The scanning configuration for the requested repositories.
	ScanningConfigurations []*RepositoryScanningConfiguration `locationName:"scanningConfigurations" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchGetRepositoryScanningConfigurationOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s BatchGetRepositoryScanningConfigurationOutput) GoString() string {
	return s.String()
}

// SetFailures sets the Failures field's value.
func (s *BatchGetRepositoryScanningConfigurationOutput) SetFailures(v []*RepositoryScanningConfigurationFailure) *BatchGetRepositoryScanningConfigurationOutput {
	s.Failures = v
	return s
}

// SetScanningConfigurations sets the ScanningConfigurations field's value.
func (s *BatchGetRepositoryScanningConfigurationOutput) SetScanningConfigurations(v []*RepositoryScanningConfiguration) *BatchGetRepositoryScanningConfigurationOutput {
	s.ScanningConfigurations = v
	return s
}

type CompleteLayerUploadInput struct {
	_ struct{} `type:"structure"`

	// The sha256 digest of the image layer.
	//
	// LayerDigests is a required field
	LayerDigests []*string `locationName:"layerDigests" min:"1" type:"list" required:"true"`

	// The Amazon Web Services account ID associated with the registry to which
	// to upload layers. If you do not specify a registry, the default registry
	// is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository to associate with the image layer.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`

	// The upload ID from a previous InitiateLayerUpload operation to associate
	// with the image layer.
	//
	// UploadId is a required field
	UploadId *string `locationName:"uploadId" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CompleteLayerUploadInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CompleteLayerUploadInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *CompleteLayerUploadInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "CompleteLayerUploadInput"}
	if s.LayerDigests == nil {
		invalidParams.Add(request.NewErrParamRequired("LayerDigests"))
	}
	if s.LayerDigests != nil && len(s.LayerDigests) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("LayerDigests", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.UploadId == nil {
		invalidParams.Add(request.NewErrParamRequired("UploadId"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetLayerDigests sets the LayerDigests field's value.
func (s *CompleteLayerUploadInput) SetLayerDigests(v []*string) *CompleteLayerUploadInput {
	s.LayerDigests = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *CompleteLayerUploadInput) SetRegistryId(v string) *CompleteLayerUploadInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *CompleteLayerUploadInput) SetRepositoryName(v string) *CompleteLayerUploadInput {
	s.RepositoryName = &v
	return s
}

// SetUploadId sets the UploadId field's value.
func (s *CompleteLayerUploadInput) SetUploadId(v string) *CompleteLayerUploadInput {
	s.UploadId = &v
	return s
}

type CompleteLayerUploadOutput struct {
	_ struct{} `type:"structure"`

	// The sha256 digest of the image layer.
	LayerDigest *string `locationName:"layerDigest" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`

	// The upload ID associated with the layer.
	UploadId *string `locationName:"uploadId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CompleteLayerUploadOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CompleteLayerUploadOutput) GoString() string {
	return s.String()
}

// SetLayerDigest sets the LayerDigest field's value.
func (s *CompleteLayerUploadOutput) SetLayerDigest(v string) *CompleteLayerUploadOutput {
	s.LayerDigest = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *CompleteLayerUploadOutput) SetRegistryId(v string) *CompleteLayerUploadOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *CompleteLayerUploadOutput) SetRepositoryName(v string) *CompleteLayerUploadOutput {
	s.RepositoryName = &v
	return s
}

// SetUploadId sets the UploadId field's value.
func (s *CompleteLayerUploadOutput) SetUploadId(v string) *CompleteLayerUploadOutput {
	s.UploadId = &v
	return s
}

type CreatePullThroughCacheRuleInput struct {
	_ struct{} `type:"structure"`

	// The repository name prefix to use when caching images from the source registry.
	//
	// EcrRepositoryPrefix is a required field
	EcrRepositoryPrefix *string `locationName:"ecrRepositoryPrefix" min:"2" type:"string" required:"true"`

	// The Amazon Web Services account ID associated with the registry to create
	// the pull through cache rule for. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The registry URL of the upstream public registry to use as the source for
	// the pull through cache rule.
	//
	// UpstreamRegistryUrl is a required field
	UpstreamRegistryUrl *string `locationName:"upstreamRegistryUrl" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreatePullThroughCacheRuleInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreatePullThroughCacheRuleInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *CreatePullThroughCacheRuleInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "CreatePullThroughCacheRuleInput"}
	if s.EcrRepositoryPrefix == nil {
		invalidParams.Add(request.NewErrParamRequired("EcrRepositoryPrefix"))
	}
	if s.EcrRepositoryPrefix != nil && len(*s.EcrRepositoryPrefix) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("EcrRepositoryPrefix", 2))
	}
	if s.UpstreamRegistryUrl == nil {
		invalidParams.Add(request.NewErrParamRequired("UpstreamRegistryUrl"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetEcrRepositoryPrefix sets the EcrRepositoryPrefix field's value.
func (s *CreatePullThroughCacheRuleInput) SetEcrRepositoryPrefix(v string) *CreatePullThroughCacheRuleInput {
	s.EcrRepositoryPrefix = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *CreatePullThroughCacheRuleInput) SetRegistryId(v string) *CreatePullThroughCacheRuleInput {
	s.RegistryId = &v
	return s
}

// SetUpstreamRegistryUrl sets the UpstreamRegistryUrl field's value.
func (s *CreatePullThroughCacheRuleInput) SetUpstreamRegistryUrl(v string) *CreatePullThroughCacheRuleInput {
	s.UpstreamRegistryUrl = &v
	return s
}

type CreatePullThroughCacheRuleOutput struct {
	_ struct{} `type:"structure"`

	// The date and time, in JavaScript date format, when the pull through cache
	// rule was created.
	CreatedAt *time.Time `locationName:"createdAt" type:"timestamp"`

	// The Amazon ECR repository prefix associated with the pull through cache rule.
	EcrRepositoryPrefix *string `locationName:"ecrRepositoryPrefix" min:"2" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The upstream registry URL associated with the pull through cache rule.
	UpstreamRegistryUrl *string `locationName:"upstreamRegistryUrl" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreatePullThroughCacheRuleOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreatePullThroughCacheRuleOutput) GoString() string {
	return s.String()
}

// SetCreatedAt sets the CreatedAt field's value.
func (s *CreatePullThroughCacheRuleOutput) SetCreatedAt(v time.Time) *CreatePullThroughCacheRuleOutput {
	s.CreatedAt = &v
	return s
}

// SetEcrRepositoryPrefix sets the EcrRepositoryPrefix field's value.
func (s *CreatePullThroughCacheRuleOutput) SetEcrRepositoryPrefix(v string) *CreatePullThroughCacheRuleOutput {
	s.EcrRepositoryPrefix = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *CreatePullThroughCacheRuleOutput) SetRegistryId(v string) *CreatePullThroughCacheRuleOutput {
	s.RegistryId = &v
	return s
}

// SetUpstreamRegistryUrl sets the UpstreamRegistryUrl field's value.
func (s *CreatePullThroughCacheRuleOutput) SetUpstreamRegistryUrl(v string) *CreatePullThroughCacheRuleOutput {
	s.UpstreamRegistryUrl = &v
	return s
}

type CreateRepositoryInput struct {
	_ struct{} `type:"structure"`

	// The encryption configuration for the repository. This determines how the
	// contents of your repository are encrypted at rest.
	EncryptionConfiguration *EncryptionConfiguration `locationName:"encryptionConfiguration" type:"structure"`

	// The image scanning configuration for the repository. This determines whether
	// images are scanned for known vulnerabilities after being pushed to the repository.
	ImageScanningConfiguration *ImageScanningConfiguration `locationName:"imageScanningConfiguration" type:"structure"`

	// The tag mutability setting for the repository. If this parameter is omitted,
	// the default setting of MUTABLE will be used which will allow image tags to
	// be overwritten. If IMMUTABLE is specified, all image tags within the repository
	// will be immutable which will prevent them from being overwritten.
	ImageTagMutability *string `locationName:"imageTagMutability" type:"string" enum:"ImageTagMutability"`

	// The Amazon Web Services account ID associated with the registry to create
	// the repository. If you do not specify a registry, the default registry is
	// assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name to use for the repository. The repository name may be specified
	// on its own (such as nginx-web-app) or it can be prepended with a namespace
	// to group the repository into a category (such as project-a/nginx-web-app).
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`

	// The metadata that you apply to the repository to help you categorize and
	// organize them. Each tag consists of a key and an optional value, both of
	// which you define. Tag keys can have a maximum character length of 128 characters,
	// and tag values can have a maximum length of 256 characters.
	Tags []*Tag `locationName:"tags" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateRepositoryInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateRepositoryInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *CreateRepositoryInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "CreateRepositoryInput"}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.EncryptionConfiguration != nil {
		if err := s.EncryptionConfiguration.Validate(); err != nil {
			invalidParams.AddNested("EncryptionConfiguration", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetEncryptionConfiguration sets the EncryptionConfiguration field's value.
func (s *CreateRepositoryInput) SetEncryptionConfiguration(v *EncryptionConfiguration) *CreateRepositoryInput {
	s.EncryptionConfiguration = v
	return s
}

// SetImageScanningConfiguration sets the ImageScanningConfiguration field's value.
func (s *CreateRepositoryInput) SetImageScanningConfiguration(v *ImageScanningConfiguration) *CreateRepositoryInput {
	s.ImageScanningConfiguration = v
	return s
}

// SetImageTagMutability sets the ImageTagMutability field's value.
func (s *CreateRepositoryInput) SetImageTagMutability(v string) *CreateRepositoryInput {
	s.ImageTagMutability = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *CreateRepositoryInput) SetRegistryId(v string) *CreateRepositoryInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *CreateRepositoryInput) SetRepositoryName(v string) *CreateRepositoryInput {
	s.RepositoryName = &v
	return s
}

// SetTags sets the Tags field's value.
func (s *CreateRepositoryInput) SetTags(v []*Tag) *CreateRepositoryInput {
	s.Tags = v
	return s
}

type CreateRepositoryOutput struct {
	_ struct{} `type:"structure"`

	// The repository that was created.
	Repository *Repository `locationName:"repository" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateRepositoryOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateRepositoryOutput) GoString() string {
	return s.String()
}

// SetRepository sets the Repository field's value.
func (s *CreateRepositoryOutput) SetRepository(v *Repository) *CreateRepositoryOutput {
	s.Repository = v
	return s
}

// The CVSS score for a finding.
type CvssScore struct {
	_ struct{} `type:"structure"`

	// The base CVSS score used for the finding.
	BaseScore *float64 `locationName:"baseScore" type:"double"`

	// The vector string of the CVSS score.
	ScoringVector *string `locationName:"scoringVector" type:"string"`

	// The source of the CVSS score.
	Source *string `locationName:"source" type:"string"`

	// The version of CVSS used for the score.
	Version *string `locationName:"version" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CvssScore) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CvssScore) GoString() string {
	return s.String()
}

// SetBaseScore sets the BaseScore field's value.
func (s *CvssScore) SetBaseScore(v float64) *CvssScore {
	s.BaseScore = &v
	return s
}

// SetScoringVector sets the ScoringVector field's value.
func (s *CvssScore) SetScoringVector(v string) *CvssScore {
	s.ScoringVector = &v
	return s
}

// SetSource sets the Source field's value.
func (s *CvssScore) SetSource(v string) *CvssScore {
	s.Source = &v
	return s
}

// SetVersion sets the Version field's value.
func (s *CvssScore) SetVersion(v string) *CvssScore {
	s.Version = &v
	return s
}

// Details on adjustments Amazon Inspector made to the CVSS score for a finding.
type CvssScoreAdjustment struct {
	_ struct{} `type:"structure"`

	// The metric used to adjust the CVSS score.
	Metric *string `locationName:"metric" type:"string"`

	// The reason the CVSS score has been adjustment.
	Reason *string `locationName:"reason" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CvssScoreAdjustment) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CvssScoreAdjustment) GoString() string {
	return s.String()
}

// SetMetric sets the Metric field's value.
func (s *CvssScoreAdjustment) SetMetric(v string) *CvssScoreAdjustment {
	s.Metric = &v
	return s
}

// SetReason sets the Reason field's value.
func (s *CvssScoreAdjustment) SetReason(v string) *CvssScoreAdjustment {
	s.Reason = &v
	return s
}

// Information about the CVSS score.
type CvssScoreDetails struct {
	_ struct{} `type:"structure"`

	// An object that contains details about adjustment Amazon Inspector made to
	// the CVSS score.
	Adjustments []*CvssScoreAdjustment `locationName:"adjustments" type:"list"`

	// The CVSS score.
	Score *float64 `locationName:"score" type:"double"`

	// The source for the CVSS score.
	ScoreSource *string `locationName:"scoreSource" type:"string"`

	// The vector for the CVSS score.
	ScoringVector *string `locationName:"scoringVector" type:"string"`

	// The CVSS version used in scoring.
	Version *string `locationName:"version" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CvssScoreDetails) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CvssScoreDetails) GoString() string {
	return s.String()
}

// SetAdjustments sets the Adjustments field's value.
func (s *CvssScoreDetails) SetAdjustments(v []*CvssScoreAdjustment) *CvssScoreDetails {
	s.Adjustments = v
	return s
}

// SetScore sets the Score field's value.
func (s *CvssScoreDetails) SetScore(v float64) *CvssScoreDetails {
	s.Score = &v
	return s
}

// SetScoreSource sets the ScoreSource field's value.
func (s *CvssScoreDetails) SetScoreSource(v string) *CvssScoreDetails {
	s.ScoreSource = &v
	return s
}

// SetScoringVector sets the ScoringVector field's value.
func (s *CvssScoreDetails) SetScoringVector(v string) *CvssScoreDetails {
	s.ScoringVector = &v
	return s
}

// SetVersion sets the Version field's value.
func (s *CvssScoreDetails) SetVersion(v string) *CvssScoreDetails {
	s.Version = &v
	return s
}

type DeleteLifecyclePolicyInput struct {
	_ struct{} `type:"structure"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository. If you do not specify a registry, the default registry is
	// assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteLifecyclePolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteLifecyclePolicyInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DeleteLifecyclePolicyInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DeleteLifecyclePolicyInput"}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRegistryId sets the RegistryId field's value.
func (s *DeleteLifecyclePolicyInput) SetRegistryId(v string) *DeleteLifecyclePolicyInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DeleteLifecyclePolicyInput) SetRepositoryName(v string) *DeleteLifecyclePolicyInput {
	s.RepositoryName = &v
	return s
}

type DeleteLifecyclePolicyOutput struct {
	_ struct{} `type:"structure"`

	// The time stamp of the last time that the lifecycle policy was run.
	LastEvaluatedAt *time.Time `locationName:"lastEvaluatedAt" type:"timestamp"`

	// The JSON lifecycle policy text.
	LifecyclePolicyText *string `locationName:"lifecyclePolicyText" min:"100" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteLifecyclePolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteLifecyclePolicyOutput) GoString() string {
	return s.String()
}

// SetLastEvaluatedAt sets the LastEvaluatedAt field's value.
func (s *DeleteLifecyclePolicyOutput) SetLastEvaluatedAt(v time.Time) *DeleteLifecyclePolicyOutput {
	s.LastEvaluatedAt = &v
	return s
}

// SetLifecyclePolicyText sets the LifecyclePolicyText field's value.
func (s *DeleteLifecyclePolicyOutput) SetLifecyclePolicyText(v string) *DeleteLifecyclePolicyOutput {
	s.LifecyclePolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DeleteLifecyclePolicyOutput) SetRegistryId(v string) *DeleteLifecyclePolicyOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DeleteLifecyclePolicyOutput) SetRepositoryName(v string) *DeleteLifecyclePolicyOutput {
	s.RepositoryName = &v
	return s
}

type DeletePullThroughCacheRuleInput struct {
	_ struct{} `type:"structure"`

	// The Amazon ECR repository prefix associated with the pull through cache rule
	// to delete.
	//
	// EcrRepositoryPrefix is a required field
	EcrRepositoryPrefix *string `locationName:"ecrRepositoryPrefix" min:"2" type:"string" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the pull through cache rule. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeletePullThroughCacheRuleInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeletePullThroughCacheRuleInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DeletePullThroughCacheRuleInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DeletePullThroughCacheRuleInput"}
	if s.EcrRepositoryPrefix == nil {
		invalidParams.Add(request.NewErrParamRequired("EcrRepositoryPrefix"))
	}
	if s.EcrRepositoryPrefix != nil && len(*s.EcrRepositoryPrefix) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("EcrRepositoryPrefix", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetEcrRepositoryPrefix sets the EcrRepositoryPrefix field's value.
func (s *DeletePullThroughCacheRuleInput) SetEcrRepositoryPrefix(v string) *DeletePullThroughCacheRuleInput {
	s.EcrRepositoryPrefix = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DeletePullThroughCacheRuleInput) SetRegistryId(v string) *DeletePullThroughCacheRuleInput {
	s.RegistryId = &v
	return s
}

type DeletePullThroughCacheRuleOutput struct {
	_ struct{} `type:"structure"`

	// The timestamp associated with the pull through cache rule.
	CreatedAt *time.Time `locationName:"createdAt" type:"timestamp"`

	// The Amazon ECR repository prefix associated with the request.
	EcrRepositoryPrefix *string `locationName:"ecrRepositoryPrefix" min:"2" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The upstream registry URL associated with the pull through cache rule.
	UpstreamRegistryUrl *string `locationName:"upstreamRegistryUrl" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeletePullThroughCacheRuleOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeletePullThroughCacheRuleOutput) GoString() string {
	return s.String()
}

// SetCreatedAt sets the CreatedAt field's value.
func (s *DeletePullThroughCacheRuleOutput) SetCreatedAt(v time.Time) *DeletePullThroughCacheRuleOutput {
	s.CreatedAt = &v
	return s
}

// SetEcrRepositoryPrefix sets the EcrRepositoryPrefix field's value.
func (s *DeletePullThroughCacheRuleOutput) SetEcrRepositoryPrefix(v string) *DeletePullThroughCacheRuleOutput {
	s.EcrRepositoryPrefix = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DeletePullThroughCacheRuleOutput) SetRegistryId(v string) *DeletePullThroughCacheRuleOutput {
	s.RegistryId = &v
	return s
}

// SetUpstreamRegistryUrl sets the UpstreamRegistryUrl field's value.
func (s *DeletePullThroughCacheRuleOutput) SetUpstreamRegistryUrl(v string) *DeletePullThroughCacheRuleOutput {
	s.UpstreamRegistryUrl = &v
	return s
}

type DeleteRegistryPolicyInput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRegistryPolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRegistryPolicyInput) GoString() string {
	return s.String()
}

type DeleteRegistryPolicyOutput struct {
	_ struct{} `type:"structure"`

	// The contents of the registry permissions policy that was deleted.
	PolicyText *string `locationName:"policyText" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRegistryPolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRegistryPolicyOutput) GoString() string {
	return s.String()
}

// SetPolicyText sets the PolicyText field's value.
func (s *DeleteRegistryPolicyOutput) SetPolicyText(v string) *DeleteRegistryPolicyOutput {
	s.PolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DeleteRegistryPolicyOutput) SetRegistryId(v string) *DeleteRegistryPolicyOutput {
	s.RegistryId = &v
	return s
}

type DeleteRepositoryInput struct {
	_ struct{} `type:"structure"`

	// If a repository contains images, forces the deletion.
	Force *bool `locationName:"force" type:"boolean"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository to delete. If you do not specify a registry, the default registry
	// is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository to delete.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRepositoryInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRepositoryInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DeleteRepositoryInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DeleteRepositoryInput"}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetForce sets the Force field's value.
func (s *DeleteRepositoryInput) SetForce(v bool) *DeleteRepositoryInput {
	s.Force = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DeleteRepositoryInput) SetRegistryId(v string) *DeleteRepositoryInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DeleteRepositoryInput) SetRepositoryName(v string) *DeleteRepositoryInput {
	s.RepositoryName = &v
	return s
}

type DeleteRepositoryOutput struct {
	_ struct{} `type:"structure"`

	// The repository that was deleted.
	Repository *Repository `locationName:"repository" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRepositoryOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRepositoryOutput) GoString() string {
	return s.String()
}

// SetRepository sets the Repository field's value.
func (s *DeleteRepositoryOutput) SetRepository(v *Repository) *DeleteRepositoryOutput {
	s.Repository = v
	return s
}

type DeleteRepositoryPolicyInput struct {
	_ struct{} `type:"structure"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository policy to delete. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository that is associated with the repository policy
	// to delete.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRepositoryPolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRepositoryPolicyInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DeleteRepositoryPolicyInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DeleteRepositoryPolicyInput"}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRegistryId sets the RegistryId field's value.
func (s *DeleteRepositoryPolicyInput) SetRegistryId(v string) *DeleteRepositoryPolicyInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DeleteRepositoryPolicyInput) SetRepositoryName(v string) *DeleteRepositoryPolicyInput {
	s.RepositoryName = &v
	return s
}

type DeleteRepositoryPolicyOutput struct {
	_ struct{} `type:"structure"`

	// The JSON repository policy that was deleted from the repository.
	PolicyText *string `locationName:"policyText" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRepositoryPolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DeleteRepositoryPolicyOutput) GoString() string {
	return s.String()
}

// SetPolicyText sets the PolicyText field's value.
func (s *DeleteRepositoryPolicyOutput) SetPolicyText(v string) *DeleteRepositoryPolicyOutput {
	s.PolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DeleteRepositoryPolicyOutput) SetRegistryId(v string) *DeleteRepositoryPolicyOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DeleteRepositoryPolicyOutput) SetRepositoryName(v string) *DeleteRepositoryPolicyOutput {
	s.RepositoryName = &v
	return s
}

type DescribeImageReplicationStatusInput struct {
	_ struct{} `type:"structure"`

	// An object with identifying information for an image in an Amazon ECR repository.
	//
	// ImageId is a required field
	ImageId *ImageIdentifier `locationName:"imageId" type:"structure" required:"true"`

	// The Amazon Web Services account ID associated with the registry. If you do
	// not specify a registry, the default registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository that the image is in.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImageReplicationStatusInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImageReplicationStatusInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribeImageReplicationStatusInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribeImageReplicationStatusInput"}
	if s.ImageId == nil {
		invalidParams.Add(request.NewErrParamRequired("ImageId"))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.ImageId != nil {
		if err := s.ImageId.Validate(); err != nil {
			invalidParams.AddNested("ImageId", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetImageId sets the ImageId field's value.
func (s *DescribeImageReplicationStatusInput) SetImageId(v *ImageIdentifier) *DescribeImageReplicationStatusInput {
	s.ImageId = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DescribeImageReplicationStatusInput) SetRegistryId(v string) *DescribeImageReplicationStatusInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DescribeImageReplicationStatusInput) SetRepositoryName(v string) *DescribeImageReplicationStatusInput {
	s.RepositoryName = &v
	return s
}

type DescribeImageReplicationStatusOutput struct {
	_ struct{} `type:"structure"`

	// An object with identifying information for an image in an Amazon ECR repository.
	ImageId *ImageIdentifier `locationName:"imageId" type:"structure"`

	// The replication status details for the images in the specified repository.
	ReplicationStatuses []*ImageReplicationStatus `locationName:"replicationStatuses" type:"list"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImageReplicationStatusOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImageReplicationStatusOutput) GoString() string {
	return s.String()
}

// SetImageId sets the ImageId field's value.
func (s *DescribeImageReplicationStatusOutput) SetImageId(v *ImageIdentifier) *DescribeImageReplicationStatusOutput {
	s.ImageId = v
	return s
}

// SetReplicationStatuses sets the ReplicationStatuses field's value.
func (s *DescribeImageReplicationStatusOutput) SetReplicationStatuses(v []*ImageReplicationStatus) *DescribeImageReplicationStatusOutput {
	s.ReplicationStatuses = v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DescribeImageReplicationStatusOutput) SetRepositoryName(v string) *DescribeImageReplicationStatusOutput {
	s.RepositoryName = &v
	return s
}

type DescribeImageScanFindingsInput struct {
	_ struct{} `type:"structure"`

	// An object with identifying information for an image in an Amazon ECR repository.
	//
	// ImageId is a required field
	ImageId *ImageIdentifier `locationName:"imageId" type:"structure" required:"true"`

	// The maximum number of image scan results returned by DescribeImageScanFindings
	// in paginated output. When this parameter is used, DescribeImageScanFindings
	// only returns maxResults results in a single page along with a nextToken response
	// element. The remaining results of the initial request can be seen by sending
	// another DescribeImageScanFindings request with the returned nextToken value.
	// This value can be between 1 and 1000. If this parameter is not used, then
	// DescribeImageScanFindings returns up to 100 results and a nextToken value,
	// if applicable.
	MaxResults *int64 `locationName:"maxResults" min:"1" type:"integer"`

	// The nextToken value returned from a previous paginated DescribeImageScanFindings
	// request where maxResults was used and the results exceeded the value of that
	// parameter. Pagination continues from the end of the previous results that
	// returned the nextToken value. This value is null when there are no more results
	// to return.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository in which to describe the image scan findings for. If you do
	// not specify a registry, the default registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository for the image for which to describe the scan findings.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImageScanFindingsInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImageScanFindingsInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribeImageScanFindingsInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribeImageScanFindingsInput"}
	if s.ImageId == nil {
		invalidParams.Add(request.NewErrParamRequired("ImageId"))
	}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.ImageId != nil {
		if err := s.ImageId.Validate(); err != nil {
			invalidParams.AddNested("ImageId", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetImageId sets the ImageId field's value.
func (s *DescribeImageScanFindingsInput) SetImageId(v *ImageIdentifier) *DescribeImageScanFindingsInput {
	s.ImageId = v
	return s
}

// SetMaxResults sets the MaxResults field's value.
func (s *DescribeImageScanFindingsInput) SetMaxResults(v int64) *DescribeImageScanFindingsInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *DescribeImageScanFindingsInput) SetNextToken(v string) *DescribeImageScanFindingsInput {
	s.NextToken = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DescribeImageScanFindingsInput) SetRegistryId(v string) *DescribeImageScanFindingsInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DescribeImageScanFindingsInput) SetRepositoryName(v string) *DescribeImageScanFindingsInput {
	s.RepositoryName = &v
	return s
}

type DescribeImageScanFindingsOutput struct {
	_ struct{} `type:"structure"`

	// An object with identifying information for an image in an Amazon ECR repository.
	ImageId *ImageIdentifier `locationName:"imageId" type:"structure"`

	// The information contained in the image scan findings.
	ImageScanFindings *ImageScanFindings `locationName:"imageScanFindings" type:"structure"`

	// The current state of the scan.
	ImageScanStatus *ImageScanStatus `locationName:"imageScanStatus" type:"structure"`

	// The nextToken value to include in a future DescribeImageScanFindings request.
	// When the results of a DescribeImageScanFindings request exceed maxResults,
	// this value can be used to retrieve the next page of results. This value is
	// null when there are no more results to return.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImageScanFindingsOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImageScanFindingsOutput) GoString() string {
	return s.String()
}

// SetImageId sets the ImageId field's value.
func (s *DescribeImageScanFindingsOutput) SetImageId(v *ImageIdentifier) *DescribeImageScanFindingsOutput {
	s.ImageId = v
	return s
}

// SetImageScanFindings sets the ImageScanFindings field's value.
func (s *DescribeImageScanFindingsOutput) SetImageScanFindings(v *ImageScanFindings) *DescribeImageScanFindingsOutput {
	s.ImageScanFindings = v
	return s
}

// SetImageScanStatus sets the ImageScanStatus field's value.
func (s *DescribeImageScanFindingsOutput) SetImageScanStatus(v *ImageScanStatus) *DescribeImageScanFindingsOutput {
	s.ImageScanStatus = v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *DescribeImageScanFindingsOutput) SetNextToken(v string) *DescribeImageScanFindingsOutput {
	s.NextToken = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DescribeImageScanFindingsOutput) SetRegistryId(v string) *DescribeImageScanFindingsOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DescribeImageScanFindingsOutput) SetRepositoryName(v string) *DescribeImageScanFindingsOutput {
	s.RepositoryName = &v
	return s
}

// An object representing a filter on a DescribeImages operation.
type DescribeImagesFilter struct {
	_ struct{} `type:"structure"`

	// The tag status with which to filter your DescribeImages results. You can
	// filter results based on whether they are TAGGED or UNTAGGED.
	TagStatus *string `locationName:"tagStatus" type:"string" enum:"TagStatus"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImagesFilter) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImagesFilter) GoString() string {
	return s.String()
}

// SetTagStatus sets the TagStatus field's value.
func (s *DescribeImagesFilter) SetTagStatus(v string) *DescribeImagesFilter {
	s.TagStatus = &v
	return s
}

type DescribeImagesInput struct {
	_ struct{} `type:"structure"`

	// The filter key and value with which to filter your DescribeImages results.
	Filter *DescribeImagesFilter `locationName:"filter" type:"structure"`

	// The list of image IDs for the requested repository.
	ImageIds []*ImageIdentifier `locationName:"imageIds" min:"1" type:"list"`

	// The maximum number of repository results returned by DescribeImages in paginated
	// output. When this parameter is used, DescribeImages only returns maxResults
	// results in a single page along with a nextToken response element. The remaining
	// results of the initial request can be seen by sending another DescribeImages
	// request with the returned nextToken value. This value can be between 1 and
	// 1000. If this parameter is not used, then DescribeImages returns up to 100
	// results and a nextToken value, if applicable. This option cannot be used
	// when you specify images with imageIds.
	MaxResults *int64 `locationName:"maxResults" min:"1" type:"integer"`

	// The nextToken value returned from a previous paginated DescribeImages request
	// where maxResults was used and the results exceeded the value of that parameter.
	// Pagination continues from the end of the previous results that returned the
	// nextToken value. This value is null when there are no more results to return.
	// This option cannot be used when you specify images with imageIds.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository in which to describe images. If you do not specify a registry,
	// the default registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository that contains the images to describe.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImagesInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImagesInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribeImagesInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribeImagesInput"}
	if s.ImageIds != nil && len(s.ImageIds) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ImageIds", 1))
	}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.ImageIds != nil {
		for i, v := range s.ImageIds {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "ImageIds", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetFilter sets the Filter field's value.
func (s *DescribeImagesInput) SetFilter(v *DescribeImagesFilter) *DescribeImagesInput {
	s.Filter = v
	return s
}

// SetImageIds sets the ImageIds field's value.
func (s *DescribeImagesInput) SetImageIds(v []*ImageIdentifier) *DescribeImagesInput {
	s.ImageIds = v
	return s
}

// SetMaxResults sets the MaxResults field's value.
func (s *DescribeImagesInput) SetMaxResults(v int64) *DescribeImagesInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *DescribeImagesInput) SetNextToken(v string) *DescribeImagesInput {
	s.NextToken = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DescribeImagesInput) SetRegistryId(v string) *DescribeImagesInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *DescribeImagesInput) SetRepositoryName(v string) *DescribeImagesInput {
	s.RepositoryName = &v
	return s
}

type DescribeImagesOutput struct {
	_ struct{} `type:"structure"`

	// A list of ImageDetail objects that contain data about the image.
	ImageDetails []*ImageDetail `locationName:"imageDetails" type:"list"`

	// The nextToken value to include in a future DescribeImages request. When the
	// results of a DescribeImages request exceed maxResults, this value can be
	// used to retrieve the next page of results. This value is null when there
	// are no more results to return.
	NextToken *string `locationName:"nextToken" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImagesOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeImagesOutput) GoString() string {
	return s.String()
}

// SetImageDetails sets the ImageDetails field's value.
func (s *DescribeImagesOutput) SetImageDetails(v []*ImageDetail) *DescribeImagesOutput {
	s.ImageDetails = v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *DescribeImagesOutput) SetNextToken(v string) *DescribeImagesOutput {
	s.NextToken = &v
	return s
}

type DescribePullThroughCacheRulesInput struct {
	_ struct{} `type:"structure"`

	// The Amazon ECR repository prefixes associated with the pull through cache
	// rules to return. If no repository prefix value is specified, all pull through
	// cache rules are returned.
	EcrRepositoryPrefixes []*string `locationName:"ecrRepositoryPrefixes" min:"1" type:"list"`

	// The maximum number of pull through cache rules returned by DescribePullThroughCacheRulesRequest
	// in paginated output. When this parameter is used, DescribePullThroughCacheRulesRequest
	// only returns maxResults results in a single page along with a nextToken response
	// element. The remaining results of the initial request can be seen by sending
	// another DescribePullThroughCacheRulesRequest request with the returned nextToken
	// value. This value can be between 1 and 1000. If this parameter is not used,
	// then DescribePullThroughCacheRulesRequest returns up to 100 results and a
	// nextToken value, if applicable.
	MaxResults *int64 `locationName:"maxResults" min:"1" type:"integer"`

	// The nextToken value returned from a previous paginated DescribePullThroughCacheRulesRequest
	// request where maxResults was used and the results exceeded the value of that
	// parameter. Pagination continues from the end of the previous results that
	// returned the nextToken value. This value is null when there are no more results
	// to return.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The Amazon Web Services account ID associated with the registry to return
	// the pull through cache rules for. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribePullThroughCacheRulesInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribePullThroughCacheRulesInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribePullThroughCacheRulesInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribePullThroughCacheRulesInput"}
	if s.EcrRepositoryPrefixes != nil && len(s.EcrRepositoryPrefixes) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("EcrRepositoryPrefixes", 1))
	}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetEcrRepositoryPrefixes sets the EcrRepositoryPrefixes field's value.
func (s *DescribePullThroughCacheRulesInput) SetEcrRepositoryPrefixes(v []*string) *DescribePullThroughCacheRulesInput {
	s.EcrRepositoryPrefixes = v
	return s
}

// SetMaxResults sets the MaxResults field's value.
func (s *DescribePullThroughCacheRulesInput) SetMaxResults(v int64) *DescribePullThroughCacheRulesInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *DescribePullThroughCacheRulesInput) SetNextToken(v string) *DescribePullThroughCacheRulesInput {
	s.NextToken = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DescribePullThroughCacheRulesInput) SetRegistryId(v string) *DescribePullThroughCacheRulesInput {
	s.RegistryId = &v
	return s
}

type DescribePullThroughCacheRulesOutput struct {
	_ struct{} `type:"structure"`

	// The nextToken value to include in a future DescribePullThroughCacheRulesRequest
	// request. When the results of a DescribePullThroughCacheRulesRequest request
	// exceed maxResults, this value can be used to retrieve the next page of results.
	// This value is null when there are no more results to return.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The details of the pull through cache rules.
	PullThroughCacheRules []*PullThroughCacheRule `locationName:"pullThroughCacheRules" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribePullThroughCacheRulesOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribePullThroughCacheRulesOutput) GoString() string {
	return s.String()
}

// SetNextToken sets the NextToken field's value.
func (s *DescribePullThroughCacheRulesOutput) SetNextToken(v string) *DescribePullThroughCacheRulesOutput {
	s.NextToken = &v
	return s
}

// SetPullThroughCacheRules sets the PullThroughCacheRules field's value.
func (s *DescribePullThroughCacheRulesOutput) SetPullThroughCacheRules(v []*PullThroughCacheRule) *DescribePullThroughCacheRulesOutput {
	s.PullThroughCacheRules = v
	return s
}

type DescribeRegistryInput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeRegistryInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeRegistryInput) GoString() string {
	return s.String()
}

type DescribeRegistryOutput struct {
	_ struct{} `type:"structure"`

	// The ID of the registry.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The replication configuration for the registry.
	ReplicationConfiguration *ReplicationConfiguration `locationName:"replicationConfiguration" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeRegistryOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeRegistryOutput) GoString() string {
	return s.String()
}

// SetRegistryId sets the RegistryId field's value.
func (s *DescribeRegistryOutput) SetRegistryId(v string) *DescribeRegistryOutput {
	s.RegistryId = &v
	return s
}

// SetReplicationConfiguration sets the ReplicationConfiguration field's value.
func (s *DescribeRegistryOutput) SetReplicationConfiguration(v *ReplicationConfiguration) *DescribeRegistryOutput {
	s.ReplicationConfiguration = v
	return s
}

type DescribeRepositoriesInput struct {
	_ struct{} `type:"structure"`

	// The maximum number of repository results returned by DescribeRepositories
	// in paginated output. When this parameter is used, DescribeRepositories only
	// returns maxResults results in a single page along with a nextToken response
	// element. The remaining results of the initial request can be seen by sending
	// another DescribeRepositories request with the returned nextToken value. This
	// value can be between 1 and 1000. If this parameter is not used, then DescribeRepositories
	// returns up to 100 results and a nextToken value, if applicable. This option
	// cannot be used when you specify repositories with repositoryNames.
	MaxResults *int64 `locationName:"maxResults" min:"1" type:"integer"`

	// The nextToken value returned from a previous paginated DescribeRepositories
	// request where maxResults was used and the results exceeded the value of that
	// parameter. Pagination continues from the end of the previous results that
	// returned the nextToken value. This value is null when there are no more results
	// to return. This option cannot be used when you specify repositories with
	// repositoryNames.
	//
	// This token should be treated as an opaque identifier that is only used to
	// retrieve the next items in a list and not for other programmatic purposes.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repositories to be described. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// A list of repositories to describe. If this parameter is omitted, then all
	// repositories in a registry are described.
	RepositoryNames []*string `locationName:"repositoryNames" min:"1" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeRepositoriesInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeRepositoriesInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *DescribeRepositoriesInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "DescribeRepositoriesInput"}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}
	if s.RepositoryNames != nil && len(s.RepositoryNames) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryNames", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetMaxResults sets the MaxResults field's value.
func (s *DescribeRepositoriesInput) SetMaxResults(v int64) *DescribeRepositoriesInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *DescribeRepositoriesInput) SetNextToken(v string) *DescribeRepositoriesInput {
	s.NextToken = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *DescribeRepositoriesInput) SetRegistryId(v string) *DescribeRepositoriesInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryNames sets the RepositoryNames field's value.
func (s *DescribeRepositoriesInput) SetRepositoryNames(v []*string) *DescribeRepositoriesInput {
	s.RepositoryNames = v
	return s
}

type DescribeRepositoriesOutput struct {
	_ struct{} `type:"structure"`

	// The nextToken value to include in a future DescribeRepositories request.
	// When the results of a DescribeRepositories request exceed maxResults, this
	// value can be used to retrieve the next page of results. This value is null
	// when there are no more results to return.
	NextToken *string `locationName:"nextToken" type:"string"`

	// A list of repository objects corresponding to valid repositories.
	Repositories []*Repository `locationName:"repositories" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeRepositoriesOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s DescribeRepositoriesOutput) GoString() string {
	return s.String()
}

// SetNextToken sets the NextToken field's value.
func (s *DescribeRepositoriesOutput) SetNextToken(v string) *DescribeRepositoriesOutput {
	s.NextToken = &v
	return s
}

// SetRepositories sets the Repositories field's value.
func (s *DescribeRepositoriesOutput) SetRepositories(v []*Repository) *DescribeRepositoriesOutput {
	s.Repositories = v
	return s
}

// The specified layer upload does not contain any layer parts.
type EmptyUploadException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s EmptyUploadException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s EmptyUploadException) GoString() string {
	return s.String()
}

func newErrorEmptyUploadException(v protocol.ResponseMetadata) error {
	return &EmptyUploadException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *EmptyUploadException) Code() string {
	return "EmptyUploadException"
}

// Message returns the exception's message.
func (s *EmptyUploadException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *EmptyUploadException) OrigErr() error {
	return nil
}

func (s *EmptyUploadException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *EmptyUploadException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *EmptyUploadException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The encryption configuration for the repository. This determines how the
// contents of your repository are encrypted at rest.
//
// By default, when no encryption configuration is set or the AES256 encryption
// type is used, Amazon ECR uses server-side encryption with Amazon S3-managed
// encryption keys which encrypts your data at rest using an AES-256 encryption
// algorithm. This does not require any action on your part.
//
// For more control over the encryption of the contents of your repository,
// you can use server-side encryption with Key Management Service key stored
// in Key Management Service (KMS) to encrypt your images. For more information,
// see Amazon ECR encryption at rest (https://docs.aws.amazon.com/AmazonECR/latest/userguide/encryption-at-rest.html)
// in the Amazon Elastic Container Registry User Guide.
type EncryptionConfiguration struct {
	_ struct{} `type:"structure"`

	// The encryption type to use.
	//
	// If you use the KMS encryption type, the contents of the repository will be
	// encrypted using server-side encryption with Key Management Service key stored
	// in KMS. When you use KMS to encrypt your data, you can either use the default
	// Amazon Web Services managed KMS key for Amazon ECR, or specify your own KMS
	// key, which you already created. For more information, see Protecting data
	// using server-side encryption with an KMS key stored in Key Management Service
	// (SSE-KMS) (https://docs.aws.amazon.com/AmazonS3/latest/dev/UsingKMSEncryption.html)
	// in the Amazon Simple Storage Service Console Developer Guide.
	//
	// If you use the AES256 encryption type, Amazon ECR uses server-side encryption
	// with Amazon S3-managed encryption keys which encrypts the images in the repository
	// using an AES-256 encryption algorithm. For more information, see Protecting
	// data using server-side encryption with Amazon S3-managed encryption keys
	// (SSE-S3) (https://docs.aws.amazon.com/AmazonS3/latest/dev/UsingServerSideEncryption.html)
	// in the Amazon Simple Storage Service Console Developer Guide.
	//
	// EncryptionType is a required field
	EncryptionType *string `locationName:"encryptionType" type:"string" required:"true" enum:"EncryptionType"`

	// If you use the KMS encryption type, specify the KMS key to use for encryption.
	// The alias, key ID, or full ARN of the KMS key can be specified. The key must
	// exist in the same Region as the repository. If no key is specified, the default
	// Amazon Web Services managed KMS key for Amazon ECR will be used.
	KmsKey *string `locationName:"kmsKey" min:"1" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s EncryptionConfiguration) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s EncryptionConfiguration) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *EncryptionConfiguration) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "EncryptionConfiguration"}
	if s.EncryptionType == nil {
		invalidParams.Add(request.NewErrParamRequired("EncryptionType"))
	}
	if s.KmsKey != nil && len(*s.KmsKey) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("KmsKey", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetEncryptionType sets the EncryptionType field's value.
func (s *EncryptionConfiguration) SetEncryptionType(v string) *EncryptionConfiguration {
	s.EncryptionType = &v
	return s
}

// SetKmsKey sets the KmsKey field's value.
func (s *EncryptionConfiguration) SetKmsKey(v string) *EncryptionConfiguration {
	s.KmsKey = &v
	return s
}

// The details of an enhanced image scan. This is returned when enhanced scanning
// is enabled for your private registry.
type EnhancedImageScanFinding struct {
	_ struct{} `type:"structure"`

	// The Amazon Web Services account ID associated with the image.
	AwsAccountId *string `locationName:"awsAccountId" type:"string"`

	// The description of the finding.
	Description *string `locationName:"description" type:"string"`

	// The Amazon Resource Number (ARN) of the finding.
	FindingArn *string `locationName:"findingArn" type:"string"`

	// The date and time that the finding was first observed.
	FirstObservedAt *time.Time `locationName:"firstObservedAt" type:"timestamp"`

	// The date and time that the finding was last observed.
	LastObservedAt *time.Time `locationName:"lastObservedAt" type:"timestamp"`

	// An object that contains the details of a package vulnerability finding.
	PackageVulnerabilityDetails *PackageVulnerabilityDetails `locationName:"packageVulnerabilityDetails" type:"structure"`

	// An object that contains the details about how to remediate a finding.
	Remediation *Remediation `locationName:"remediation" type:"structure"`

	// Contains information on the resources involved in a finding.
	Resources []*Resource `locationName:"resources" type:"list"`

	// The Amazon Inspector score given to the finding.
	Score *float64 `locationName:"score" type:"double"`

	// An object that contains details of the Amazon Inspector score.
	ScoreDetails *ScoreDetails `locationName:"scoreDetails" type:"structure"`

	// The severity of the finding.
	Severity *string `locationName:"severity" type:"string"`

	// The status of the finding.
	Status *string `locationName:"status" type:"string"`

	// The title of the finding.
	Title *string `locationName:"title" type:"string"`

	// The type of the finding.
	Type *string `locationName:"type" type:"string"`

	// The date and time the finding was last updated at.
	UpdatedAt *time.Time `locationName:"updatedAt" type:"timestamp"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s EnhancedImageScanFinding) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s EnhancedImageScanFinding) GoString() string {
	return s.String()
}

// SetAwsAccountId sets the AwsAccountId field's value.
func (s *EnhancedImageScanFinding) SetAwsAccountId(v string) *EnhancedImageScanFinding {
	s.AwsAccountId = &v
	return s
}

// SetDescription sets the Description field's value.
func (s *EnhancedImageScanFinding) SetDescription(v string) *EnhancedImageScanFinding {
	s.Description = &v
	return s
}

// SetFindingArn sets the FindingArn field's value.
func (s *EnhancedImageScanFinding) SetFindingArn(v string) *EnhancedImageScanFinding {
	s.FindingArn = &v
	return s
}

// SetFirstObservedAt sets the FirstObservedAt field's value.
func (s *EnhancedImageScanFinding) SetFirstObservedAt(v time.Time) *EnhancedImageScanFinding {
	s.FirstObservedAt = &v
	return s
}

// SetLastObservedAt sets the LastObservedAt field's value.
func (s *EnhancedImageScanFinding) SetLastObservedAt(v time.Time) *EnhancedImageScanFinding {
	s.LastObservedAt = &v
	return s
}

// SetPackageVulnerabilityDetails sets the PackageVulnerabilityDetails field's value.
func (s *EnhancedImageScanFinding) SetPackageVulnerabilityDetails(v *PackageVulnerabilityDetails) *EnhancedImageScanFinding {
	s.PackageVulnerabilityDetails = v
	return s
}

// SetRemediation sets the Remediation field's value.
func (s *EnhancedImageScanFinding) SetRemediation(v *Remediation) *EnhancedImageScanFinding {
	s.Remediation = v
	return s
}

// SetResources sets the Resources field's value.
func (s *EnhancedImageScanFinding) SetResources(v []*Resource) *EnhancedImageScanFinding {
	s.Resources = v
	return s
}

// SetScore sets the Score field's value.
func (s *EnhancedImageScanFinding) SetScore(v float64) *EnhancedImageScanFinding {
	s.Score = &v
	return s
}

// SetScoreDetails sets the ScoreDetails field's value.
func (s *EnhancedImageScanFinding) SetScoreDetails(v *ScoreDetails) *EnhancedImageScanFinding {
	s.ScoreDetails = v
	return s
}

// SetSeverity sets the Severity field's value.
func (s *EnhancedImageScanFinding) SetSeverity(v string) *EnhancedImageScanFinding {
	s.Severity = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *EnhancedImageScanFinding) SetStatus(v string) *EnhancedImageScanFinding {
	s.Status = &v
	return s
}

// SetTitle sets the Title field's value.
func (s *EnhancedImageScanFinding) SetTitle(v string) *EnhancedImageScanFinding {
	s.Title = &v
	return s
}

// SetType sets the Type field's value.
func (s *EnhancedImageScanFinding) SetType(v string) *EnhancedImageScanFinding {
	s.Type = &v
	return s
}

// SetUpdatedAt sets the UpdatedAt field's value.
func (s *EnhancedImageScanFinding) SetUpdatedAt(v time.Time) *EnhancedImageScanFinding {
	s.UpdatedAt = &v
	return s
}

type GetAuthorizationTokenInput struct {
	_ struct{} `type:"structure"`

	// A list of Amazon Web Services account IDs that are associated with the registries
	// for which to get AuthorizationData objects. If you do not specify a registry,
	// the default registry is assumed.
	//
	// Deprecated: This field is deprecated. The returned authorization token can be used to access any Amazon ECR registry that the IAM principal has access to, specifying a registry ID doesn't change the permissions scope of the authorization token.
	RegistryIds []*string `locationName:"registryIds" min:"1" deprecated:"true" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetAuthorizationTokenInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetAuthorizationTokenInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *GetAuthorizationTokenInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "GetAuthorizationTokenInput"}
	if s.RegistryIds != nil && len(s.RegistryIds) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("RegistryIds", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRegistryIds sets the RegistryIds field's value.
func (s *GetAuthorizationTokenInput) SetRegistryIds(v []*string) *GetAuthorizationTokenInput {
	s.RegistryIds = v
	return s
}

type GetAuthorizationTokenOutput struct {
	_ struct{} `type:"structure"`

	// A list of authorization token data objects that correspond to the registryIds
	// values in the request.
	AuthorizationData []*AuthorizationData `locationName:"authorizationData" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetAuthorizationTokenOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetAuthorizationTokenOutput) GoString() string {
	return s.String()
}

// SetAuthorizationData sets the AuthorizationData field's value.
func (s *GetAuthorizationTokenOutput) SetAuthorizationData(v []*AuthorizationData) *GetAuthorizationTokenOutput {
	s.AuthorizationData = v
	return s
}

type GetDownloadUrlForLayerInput struct {
	_ struct{} `type:"structure"`

	// The digest of the image layer to download.
	//
	// LayerDigest is a required field
	LayerDigest *string `locationName:"layerDigest" type:"string" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the image layer to download. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository that is associated with the image layer to download.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetDownloadUrlForLayerInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetDownloadUrlForLayerInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *GetDownloadUrlForLayerInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "GetDownloadUrlForLayerInput"}
	if s.LayerDigest == nil {
		invalidParams.Add(request.NewErrParamRequired("LayerDigest"))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetLayerDigest sets the LayerDigest field's value.
func (s *GetDownloadUrlForLayerInput) SetLayerDigest(v string) *GetDownloadUrlForLayerInput {
	s.LayerDigest = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetDownloadUrlForLayerInput) SetRegistryId(v string) *GetDownloadUrlForLayerInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *GetDownloadUrlForLayerInput) SetRepositoryName(v string) *GetDownloadUrlForLayerInput {
	s.RepositoryName = &v
	return s
}

type GetDownloadUrlForLayerOutput struct {
	_ struct{} `type:"structure"`

	// The pre-signed Amazon S3 download URL for the requested layer.
	DownloadUrl *string `locationName:"downloadUrl" type:"string"`

	// The digest of the image layer to download.
	LayerDigest *string `locationName:"layerDigest" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetDownloadUrlForLayerOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetDownloadUrlForLayerOutput) GoString() string {
	return s.String()
}

// SetDownloadUrl sets the DownloadUrl field's value.
func (s *GetDownloadUrlForLayerOutput) SetDownloadUrl(v string) *GetDownloadUrlForLayerOutput {
	s.DownloadUrl = &v
	return s
}

// SetLayerDigest sets the LayerDigest field's value.
func (s *GetDownloadUrlForLayerOutput) SetLayerDigest(v string) *GetDownloadUrlForLayerOutput {
	s.LayerDigest = &v
	return s
}

type GetLifecyclePolicyInput struct {
	_ struct{} `type:"structure"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository. If you do not specify a registry, the default registry is
	// assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetLifecyclePolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetLifecyclePolicyInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *GetLifecyclePolicyInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "GetLifecyclePolicyInput"}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetLifecyclePolicyInput) SetRegistryId(v string) *GetLifecyclePolicyInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *GetLifecyclePolicyInput) SetRepositoryName(v string) *GetLifecyclePolicyInput {
	s.RepositoryName = &v
	return s
}

type GetLifecyclePolicyOutput struct {
	_ struct{} `type:"structure"`

	// The time stamp of the last time that the lifecycle policy was run.
	LastEvaluatedAt *time.Time `locationName:"lastEvaluatedAt" type:"timestamp"`

	// The JSON lifecycle policy text.
	LifecyclePolicyText *string `locationName:"lifecyclePolicyText" min:"100" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetLifecyclePolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetLifecyclePolicyOutput) GoString() string {
	return s.String()
}

// SetLastEvaluatedAt sets the LastEvaluatedAt field's value.
func (s *GetLifecyclePolicyOutput) SetLastEvaluatedAt(v time.Time) *GetLifecyclePolicyOutput {
	s.LastEvaluatedAt = &v
	return s
}

// SetLifecyclePolicyText sets the LifecyclePolicyText field's value.
func (s *GetLifecyclePolicyOutput) SetLifecyclePolicyText(v string) *GetLifecyclePolicyOutput {
	s.LifecyclePolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetLifecyclePolicyOutput) SetRegistryId(v string) *GetLifecyclePolicyOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *GetLifecyclePolicyOutput) SetRepositoryName(v string) *GetLifecyclePolicyOutput {
	s.RepositoryName = &v
	return s
}

type GetLifecyclePolicyPreviewInput struct {
	_ struct{} `type:"structure"`

	// An optional parameter that filters results based on image tag status and
	// all tags, if tagged.
	Filter *LifecyclePolicyPreviewFilter `locationName:"filter" type:"structure"`

	// The list of imageIDs to be included.
	ImageIds []*ImageIdentifier `locationName:"imageIds" min:"1" type:"list"`

	// The maximum number of repository results returned by GetLifecyclePolicyPreviewRequest
	// in paginated output. When this parameter is used, GetLifecyclePolicyPreviewRequest
	// only returns maxResults results in a single page along with a nextToken response
	// element. The remaining results of the initial request can be seen by sending
	// another GetLifecyclePolicyPreviewRequest request with the returned nextToken
	// value. This value can be between 1 and 1000. If this parameter is not used,
	// then GetLifecyclePolicyPreviewRequest returns up to 100 results and a nextToken
	// value, if applicable. This option cannot be used when you specify images
	// with imageIds.
	MaxResults *int64 `locationName:"maxResults" min:"1" type:"integer"`

	// The nextToken value returned from a previous paginated GetLifecyclePolicyPreviewRequest
	// request where maxResults was used and the results exceeded the value of that
	// parameter. Pagination continues from the end of the previous results that
	// returned the nextToken value. This value is null when there are no more results
	// to return. This option cannot be used when you specify images with imageIds.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository. If you do not specify a registry, the default registry is
	// assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetLifecyclePolicyPreviewInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetLifecyclePolicyPreviewInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *GetLifecyclePolicyPreviewInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "GetLifecyclePolicyPreviewInput"}
	if s.ImageIds != nil && len(s.ImageIds) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ImageIds", 1))
	}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.ImageIds != nil {
		for i, v := range s.ImageIds {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "ImageIds", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetFilter sets the Filter field's value.
func (s *GetLifecyclePolicyPreviewInput) SetFilter(v *LifecyclePolicyPreviewFilter) *GetLifecyclePolicyPreviewInput {
	s.Filter = v
	return s
}

// SetImageIds sets the ImageIds field's value.
func (s *GetLifecyclePolicyPreviewInput) SetImageIds(v []*ImageIdentifier) *GetLifecyclePolicyPreviewInput {
	s.ImageIds = v
	return s
}

// SetMaxResults sets the MaxResults field's value.
func (s *GetLifecyclePolicyPreviewInput) SetMaxResults(v int64) *GetLifecyclePolicyPreviewInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *GetLifecyclePolicyPreviewInput) SetNextToken(v string) *GetLifecyclePolicyPreviewInput {
	s.NextToken = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetLifecyclePolicyPreviewInput) SetRegistryId(v string) *GetLifecyclePolicyPreviewInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *GetLifecyclePolicyPreviewInput) SetRepositoryName(v string) *GetLifecyclePolicyPreviewInput {
	s.RepositoryName = &v
	return s
}

type GetLifecyclePolicyPreviewOutput struct {
	_ struct{} `type:"structure"`

	// The JSON lifecycle policy text.
	LifecyclePolicyText *string `locationName:"lifecyclePolicyText" min:"100" type:"string"`

	// The nextToken value to include in a future GetLifecyclePolicyPreview request.
	// When the results of a GetLifecyclePolicyPreview request exceed maxResults,
	// this value can be used to retrieve the next page of results. This value is
	// null when there are no more results to return.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The results of the lifecycle policy preview request.
	PreviewResults []*LifecyclePolicyPreviewResult `locationName:"previewResults" type:"list"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`

	// The status of the lifecycle policy preview request.
	Status *string `locationName:"status" type:"string" enum:"LifecyclePolicyPreviewStatus"`

	// The list of images that is returned as a result of the action.
	Summary *LifecyclePolicyPreviewSummary `locationName:"summary" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetLifecyclePolicyPreviewOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetLifecyclePolicyPreviewOutput) GoString() string {
	return s.String()
}

// SetLifecyclePolicyText sets the LifecyclePolicyText field's value.
func (s *GetLifecyclePolicyPreviewOutput) SetLifecyclePolicyText(v string) *GetLifecyclePolicyPreviewOutput {
	s.LifecyclePolicyText = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *GetLifecyclePolicyPreviewOutput) SetNextToken(v string) *GetLifecyclePolicyPreviewOutput {
	s.NextToken = &v
	return s
}

// SetPreviewResults sets the PreviewResults field's value.
func (s *GetLifecyclePolicyPreviewOutput) SetPreviewResults(v []*LifecyclePolicyPreviewResult) *GetLifecyclePolicyPreviewOutput {
	s.PreviewResults = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetLifecyclePolicyPreviewOutput) SetRegistryId(v string) *GetLifecyclePolicyPreviewOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *GetLifecyclePolicyPreviewOutput) SetRepositoryName(v string) *GetLifecyclePolicyPreviewOutput {
	s.RepositoryName = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *GetLifecyclePolicyPreviewOutput) SetStatus(v string) *GetLifecyclePolicyPreviewOutput {
	s.Status = &v
	return s
}

// SetSummary sets the Summary field's value.
func (s *GetLifecyclePolicyPreviewOutput) SetSummary(v *LifecyclePolicyPreviewSummary) *GetLifecyclePolicyPreviewOutput {
	s.Summary = v
	return s
}

type GetRegistryPolicyInput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRegistryPolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRegistryPolicyInput) GoString() string {
	return s.String()
}

type GetRegistryPolicyOutput struct {
	_ struct{} `type:"structure"`

	// The JSON text of the permissions policy for a registry.
	PolicyText *string `locationName:"policyText" type:"string"`

	// The ID of the registry.
	RegistryId *string `locationName:"registryId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRegistryPolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRegistryPolicyOutput) GoString() string {
	return s.String()
}

// SetPolicyText sets the PolicyText field's value.
func (s *GetRegistryPolicyOutput) SetPolicyText(v string) *GetRegistryPolicyOutput {
	s.PolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetRegistryPolicyOutput) SetRegistryId(v string) *GetRegistryPolicyOutput {
	s.RegistryId = &v
	return s
}

type GetRegistryScanningConfigurationInput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRegistryScanningConfigurationInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRegistryScanningConfigurationInput) GoString() string {
	return s.String()
}

type GetRegistryScanningConfigurationOutput struct {
	_ struct{} `type:"structure"`

	// The ID of the registry.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The scanning configuration for the registry.
	ScanningConfiguration *RegistryScanningConfiguration `locationName:"scanningConfiguration" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRegistryScanningConfigurationOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRegistryScanningConfigurationOutput) GoString() string {
	return s.String()
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetRegistryScanningConfigurationOutput) SetRegistryId(v string) *GetRegistryScanningConfigurationOutput {
	s.RegistryId = &v
	return s
}

// SetScanningConfiguration sets the ScanningConfiguration field's value.
func (s *GetRegistryScanningConfigurationOutput) SetScanningConfiguration(v *RegistryScanningConfiguration) *GetRegistryScanningConfigurationOutput {
	s.ScanningConfiguration = v
	return s
}

type GetRepositoryPolicyInput struct {
	_ struct{} `type:"structure"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository. If you do not specify a registry, the default registry is
	// assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository with the policy to retrieve.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRepositoryPolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRepositoryPolicyInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *GetRepositoryPolicyInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "GetRepositoryPolicyInput"}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetRepositoryPolicyInput) SetRegistryId(v string) *GetRepositoryPolicyInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *GetRepositoryPolicyInput) SetRepositoryName(v string) *GetRepositoryPolicyInput {
	s.RepositoryName = &v
	return s
}

type GetRepositoryPolicyOutput struct {
	_ struct{} `type:"structure"`

	// The JSON repository policy text associated with the repository.
	PolicyText *string `locationName:"policyText" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRepositoryPolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s GetRepositoryPolicyOutput) GoString() string {
	return s.String()
}

// SetPolicyText sets the PolicyText field's value.
func (s *GetRepositoryPolicyOutput) SetPolicyText(v string) *GetRepositoryPolicyOutput {
	s.PolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *GetRepositoryPolicyOutput) SetRegistryId(v string) *GetRepositoryPolicyOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *GetRepositoryPolicyOutput) SetRepositoryName(v string) *GetRepositoryPolicyOutput {
	s.RepositoryName = &v
	return s
}

// An object representing an Amazon ECR image.
type Image struct {
	_ struct{} `type:"structure"`

	// An object containing the image tag and image digest associated with an image.
	ImageId *ImageIdentifier `locationName:"imageId" type:"structure"`

	// The image manifest associated with the image.
	ImageManifest *string `locationName:"imageManifest" min:"1" type:"string"`

	// The manifest media type of the image.
	ImageManifestMediaType *string `locationName:"imageManifestMediaType" type:"string"`

	// The Amazon Web Services account ID associated with the registry containing
	// the image.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository associated with the image.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Image) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Image) GoString() string {
	return s.String()
}

// SetImageId sets the ImageId field's value.
func (s *Image) SetImageId(v *ImageIdentifier) *Image {
	s.ImageId = v
	return s
}

// SetImageManifest sets the ImageManifest field's value.
func (s *Image) SetImageManifest(v string) *Image {
	s.ImageManifest = &v
	return s
}

// SetImageManifestMediaType sets the ImageManifestMediaType field's value.
func (s *Image) SetImageManifestMediaType(v string) *Image {
	s.ImageManifestMediaType = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *Image) SetRegistryId(v string) *Image {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *Image) SetRepositoryName(v string) *Image {
	s.RepositoryName = &v
	return s
}

// The specified image has already been pushed, and there were no changes to
// the manifest or image tag after the last push.
type ImageAlreadyExistsException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageAlreadyExistsException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageAlreadyExistsException) GoString() string {
	return s.String()
}

func newErrorImageAlreadyExistsException(v protocol.ResponseMetadata) error {
	return &ImageAlreadyExistsException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ImageAlreadyExistsException) Code() string {
	return "ImageAlreadyExistsException"
}

// Message returns the exception's message.
func (s *ImageAlreadyExistsException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ImageAlreadyExistsException) OrigErr() error {
	return nil
}

func (s *ImageAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ImageAlreadyExistsException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ImageAlreadyExistsException) RequestID() string {
	return s.RespMetadata.RequestID
}

// An object that describes an image returned by a DescribeImages operation.
type ImageDetail struct {
	_ struct{} `type:"structure"`

	// The artifact media type of the image.
	ArtifactMediaType *string `locationName:"artifactMediaType" type:"string"`

	// The sha256 digest of the image manifest.
	ImageDigest *string `locationName:"imageDigest" type:"string"`

	// The media type of the image manifest.
	ImageManifestMediaType *string `locationName:"imageManifestMediaType" type:"string"`

	// The date and time, expressed in standard JavaScript date format, at which
	// the current image was pushed to the repository.
	ImagePushedAt *time.Time `locationName:"imagePushedAt" type:"timestamp"`

	// A summary of the last completed image scan.
	ImageScanFindingsSummary *ImageScanFindingsSummary `locationName:"imageScanFindingsSummary" type:"structure"`

	// The current state of the scan.
	ImageScanStatus *ImageScanStatus `locationName:"imageScanStatus" type:"structure"`

	// The size, in bytes, of the image in the repository.
	//
	// If the image is a manifest list, this will be the max size of all manifests
	// in the list.
	//
	// Beginning with Docker version 1.9, the Docker client compresses image layers
	// before pushing them to a V2 Docker registry. The output of the docker images
	// command shows the uncompressed image size, so it may return a larger image
	// size than the image sizes returned by DescribeImages.
	ImageSizeInBytes *int64 `locationName:"imageSizeInBytes" type:"long"`

	// The list of tags associated with this image.
	ImageTags []*string `locationName:"imageTags" type:"list"`

	// The date and time, expressed in standard JavaScript date format, when Amazon
	// ECR recorded the last image pull.
	//
	// Amazon ECR refreshes the last image pull timestamp at least once every 24
	// hours. For example, if you pull an image once a day then the lastRecordedPullTime
	// timestamp will indicate the exact time that the image was last pulled. However,
	// if you pull an image once an hour, because Amazon ECR refreshes the lastRecordedPullTime
	// timestamp at least once every 24 hours, the result may not be the exact time
	// that the image was last pulled.
	LastRecordedPullTime *time.Time `locationName:"lastRecordedPullTime" type:"timestamp"`

	// The Amazon Web Services account ID associated with the registry to which
	// this image belongs.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository to which this image belongs.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageDetail) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageDetail) GoString() string {
	return s.String()
}

// SetArtifactMediaType sets the ArtifactMediaType field's value.
func (s *ImageDetail) SetArtifactMediaType(v string) *ImageDetail {
	s.ArtifactMediaType = &v
	return s
}

// SetImageDigest sets the ImageDigest field's value.
func (s *ImageDetail) SetImageDigest(v string) *ImageDetail {
	s.ImageDigest = &v
	return s
}

// SetImageManifestMediaType sets the ImageManifestMediaType field's value.
func (s *ImageDetail) SetImageManifestMediaType(v string) *ImageDetail {
	s.ImageManifestMediaType = &v
	return s
}

// SetImagePushedAt sets the ImagePushedAt field's value.
func (s *ImageDetail) SetImagePushedAt(v time.Time) *ImageDetail {
	s.ImagePushedAt = &v
	return s
}

// SetImageScanFindingsSummary sets the ImageScanFindingsSummary field's value.
func (s *ImageDetail) SetImageScanFindingsSummary(v *ImageScanFindingsSummary) *ImageDetail {
	s.ImageScanFindingsSummary = v
	return s
}

// SetImageScanStatus sets the ImageScanStatus field's value.
func (s *ImageDetail) SetImageScanStatus(v *ImageScanStatus) *ImageDetail {
	s.ImageScanStatus = v
	return s
}

// SetImageSizeInBytes sets the ImageSizeInBytes field's value.
func (s *ImageDetail) SetImageSizeInBytes(v int64) *ImageDetail {
	s.ImageSizeInBytes = &v
	return s
}

// SetImageTags sets the ImageTags field's value.
func (s *ImageDetail) SetImageTags(v []*string) *ImageDetail {
	s.ImageTags = v
	return s
}

// SetLastRecordedPullTime sets the LastRecordedPullTime field's value.
func (s *ImageDetail) SetLastRecordedPullTime(v time.Time) *ImageDetail {
	s.LastRecordedPullTime = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *ImageDetail) SetRegistryId(v string) *ImageDetail {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *ImageDetail) SetRepositoryName(v string) *ImageDetail {
	s.RepositoryName = &v
	return s
}

// The specified image digest does not match the digest that Amazon ECR calculated
// for the image.
type ImageDigestDoesNotMatchException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageDigestDoesNotMatchException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageDigestDoesNotMatchException) GoString() string {
	return s.String()
}

func newErrorImageDigestDoesNotMatchException(v protocol.ResponseMetadata) error {
	return &ImageDigestDoesNotMatchException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ImageDigestDoesNotMatchException) Code() string {
	return "ImageDigestDoesNotMatchException"
}

// Message returns the exception's message.
func (s *ImageDigestDoesNotMatchException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ImageDigestDoesNotMatchException) OrigErr() error {
	return nil
}

func (s *ImageDigestDoesNotMatchException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ImageDigestDoesNotMatchException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ImageDigestDoesNotMatchException) RequestID() string {
	return s.RespMetadata.RequestID
}

// An object representing an Amazon ECR image failure.
type ImageFailure struct {
	_ struct{} `type:"structure"`

	// The code associated with the failure.
	FailureCode *string `locationName:"failureCode" type:"string" enum:"ImageFailureCode"`

	// The reason for the failure.
	FailureReason *string `locationName:"failureReason" type:"string"`

	// The image ID associated with the failure.
	ImageId *ImageIdentifier `locationName:"imageId" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageFailure) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageFailure) GoString() string {
	return s.String()
}

// SetFailureCode sets the FailureCode field's value.
func (s *ImageFailure) SetFailureCode(v string) *ImageFailure {
	s.FailureCode = &v
	return s
}

// SetFailureReason sets the FailureReason field's value.
func (s *ImageFailure) SetFailureReason(v string) *ImageFailure {
	s.FailureReason = &v
	return s
}

// SetImageId sets the ImageId field's value.
func (s *ImageFailure) SetImageId(v *ImageIdentifier) *ImageFailure {
	s.ImageId = v
	return s
}

// An object with identifying information for an image in an Amazon ECR repository.
type ImageIdentifier struct {
	_ struct{} `type:"structure"`

	// The sha256 digest of the image manifest.
	ImageDigest *string `locationName:"imageDigest" type:"string"`

	// The tag used for the image.
	ImageTag *string `locationName:"imageTag" min:"1" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageIdentifier) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageIdentifier) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ImageIdentifier) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ImageIdentifier"}
	if s.ImageTag != nil && len(*s.ImageTag) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ImageTag", 1))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetImageDigest sets the ImageDigest field's value.
func (s *ImageIdentifier) SetImageDigest(v string) *ImageIdentifier {
	s.ImageDigest = &v
	return s
}

// SetImageTag sets the ImageTag field's value.
func (s *ImageIdentifier) SetImageTag(v string) *ImageIdentifier {
	s.ImageTag = &v
	return s
}

// The image requested does not exist in the specified repository.
type ImageNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageNotFoundException) GoString() string {
	return s.String()
}

func newErrorImageNotFoundException(v protocol.ResponseMetadata) error {
	return &ImageNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ImageNotFoundException) Code() string {
	return "ImageNotFoundException"
}

// Message returns the exception's message.
func (s *ImageNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ImageNotFoundException) OrigErr() error {
	return nil
}

func (s *ImageNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ImageNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ImageNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The status of the replication process for an image.
type ImageReplicationStatus struct {
	_ struct{} `type:"structure"`

	// The failure code for a replication that has failed.
	FailureCode *string `locationName:"failureCode" type:"string"`

	// The destination Region for the image replication.
	Region *string `locationName:"region" min:"2" type:"string"`

	// The Amazon Web Services account ID associated with the registry to which
	// the image belongs.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The image replication status.
	Status *string `locationName:"status" type:"string" enum:"ReplicationStatus"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageReplicationStatus) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageReplicationStatus) GoString() string {
	return s.String()
}

// SetFailureCode sets the FailureCode field's value.
func (s *ImageReplicationStatus) SetFailureCode(v string) *ImageReplicationStatus {
	s.FailureCode = &v
	return s
}

// SetRegion sets the Region field's value.
func (s *ImageReplicationStatus) SetRegion(v string) *ImageReplicationStatus {
	s.Region = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *ImageReplicationStatus) SetRegistryId(v string) *ImageReplicationStatus {
	s.RegistryId = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *ImageReplicationStatus) SetStatus(v string) *ImageReplicationStatus {
	s.Status = &v
	return s
}

// Contains information about an image scan finding.
type ImageScanFinding struct {
	_ struct{} `type:"structure"`

	// A collection of attributes of the host from which the finding is generated.
	Attributes []*Attribute `locationName:"attributes" type:"list"`

	// The description of the finding.
	Description *string `locationName:"description" type:"string"`

	// The name associated with the finding, usually a CVE number.
	Name *string `locationName:"name" type:"string"`

	// The finding severity.
	Severity *string `locationName:"severity" type:"string" enum:"FindingSeverity"`

	// A link containing additional details about the security vulnerability.
	Uri *string `locationName:"uri" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanFinding) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanFinding) GoString() string {
	return s.String()
}

// SetAttributes sets the Attributes field's value.
func (s *ImageScanFinding) SetAttributes(v []*Attribute) *ImageScanFinding {
	s.Attributes = v
	return s
}

// SetDescription sets the Description field's value.
func (s *ImageScanFinding) SetDescription(v string) *ImageScanFinding {
	s.Description = &v
	return s
}

// SetName sets the Name field's value.
func (s *ImageScanFinding) SetName(v string) *ImageScanFinding {
	s.Name = &v
	return s
}

// SetSeverity sets the Severity field's value.
func (s *ImageScanFinding) SetSeverity(v string) *ImageScanFinding {
	s.Severity = &v
	return s
}

// SetUri sets the Uri field's value.
func (s *ImageScanFinding) SetUri(v string) *ImageScanFinding {
	s.Uri = &v
	return s
}

// The details of an image scan.
type ImageScanFindings struct {
	_ struct{} `type:"structure"`

	// Details about the enhanced scan findings from Amazon Inspector.
	EnhancedFindings []*EnhancedImageScanFinding `locationName:"enhancedFindings" type:"list"`

	// The image vulnerability counts, sorted by severity.
	FindingSeverityCounts map[string]*int64 `locationName:"findingSeverityCounts" type:"map"`

	// The findings from the image scan.
	Findings []*ImageScanFinding `locationName:"findings" type:"list"`

	// The time of the last completed image scan.
	ImageScanCompletedAt *time.Time `locationName:"imageScanCompletedAt" type:"timestamp"`

	// The time when the vulnerability data was last scanned.
	VulnerabilitySourceUpdatedAt *time.Time `locationName:"vulnerabilitySourceUpdatedAt" type:"timestamp"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanFindings) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanFindings) GoString() string {
	return s.String()
}

// SetEnhancedFindings sets the EnhancedFindings field's value.
func (s *ImageScanFindings) SetEnhancedFindings(v []*EnhancedImageScanFinding) *ImageScanFindings {
	s.EnhancedFindings = v
	return s
}

// SetFindingSeverityCounts sets the FindingSeverityCounts field's value.
func (s *ImageScanFindings) SetFindingSeverityCounts(v map[string]*int64) *ImageScanFindings {
	s.FindingSeverityCounts = v
	return s
}

// SetFindings sets the Findings field's value.
func (s *ImageScanFindings) SetFindings(v []*ImageScanFinding) *ImageScanFindings {
	s.Findings = v
	return s
}

// SetImageScanCompletedAt sets the ImageScanCompletedAt field's value.
func (s *ImageScanFindings) SetImageScanCompletedAt(v time.Time) *ImageScanFindings {
	s.ImageScanCompletedAt = &v
	return s
}

// SetVulnerabilitySourceUpdatedAt sets the VulnerabilitySourceUpdatedAt field's value.
func (s *ImageScanFindings) SetVulnerabilitySourceUpdatedAt(v time.Time) *ImageScanFindings {
	s.VulnerabilitySourceUpdatedAt = &v
	return s
}

// A summary of the last completed image scan.
type ImageScanFindingsSummary struct {
	_ struct{} `type:"structure"`

	// The image vulnerability counts, sorted by severity.
	FindingSeverityCounts map[string]*int64 `locationName:"findingSeverityCounts" type:"map"`

	// The time of the last completed image scan.
	ImageScanCompletedAt *time.Time `locationName:"imageScanCompletedAt" type:"timestamp"`

	// The time when the vulnerability data was last scanned.
	VulnerabilitySourceUpdatedAt *time.Time `locationName:"vulnerabilitySourceUpdatedAt" type:"timestamp"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanFindingsSummary) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanFindingsSummary) GoString() string {
	return s.String()
}

// SetFindingSeverityCounts sets the FindingSeverityCounts field's value.
func (s *ImageScanFindingsSummary) SetFindingSeverityCounts(v map[string]*int64) *ImageScanFindingsSummary {
	s.FindingSeverityCounts = v
	return s
}

// SetImageScanCompletedAt sets the ImageScanCompletedAt field's value.
func (s *ImageScanFindingsSummary) SetImageScanCompletedAt(v time.Time) *ImageScanFindingsSummary {
	s.ImageScanCompletedAt = &v
	return s
}

// SetVulnerabilitySourceUpdatedAt sets the VulnerabilitySourceUpdatedAt field's value.
func (s *ImageScanFindingsSummary) SetVulnerabilitySourceUpdatedAt(v time.Time) *ImageScanFindingsSummary {
	s.VulnerabilitySourceUpdatedAt = &v
	return s
}

// The current status of an image scan.
type ImageScanStatus struct {
	_ struct{} `type:"structure"`

	// The description of the image scan status.
	Description *string `locationName:"description" type:"string"`

	// The current state of an image scan.
	Status *string `locationName:"status" type:"string" enum:"ScanStatus"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanStatus) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanStatus) GoString() string {
	return s.String()
}

// SetDescription sets the Description field's value.
func (s *ImageScanStatus) SetDescription(v string) *ImageScanStatus {
	s.Description = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *ImageScanStatus) SetStatus(v string) *ImageScanStatus {
	s.Status = &v
	return s
}

// The image scanning configuration for a repository.
type ImageScanningConfiguration struct {
	_ struct{} `type:"structure"`

	// The setting that determines whether images are scanned after being pushed
	// to a repository. If set to true, images will be scanned after being pushed.
	// If this parameter is not specified, it will default to false and images will
	// not be scanned unless a scan is manually started with the API_StartImageScan
	// (https://docs.aws.amazon.com/AmazonECR/latest/APIReference/API_StartImageScan.html)
	// API.
	ScanOnPush *bool `locationName:"scanOnPush" type:"boolean"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanningConfiguration) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageScanningConfiguration) GoString() string {
	return s.String()
}

// SetScanOnPush sets the ScanOnPush field's value.
func (s *ImageScanningConfiguration) SetScanOnPush(v bool) *ImageScanningConfiguration {
	s.ScanOnPush = &v
	return s
}

// The specified image is tagged with a tag that already exists. The repository
// is configured for tag immutability.
type ImageTagAlreadyExistsException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageTagAlreadyExistsException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ImageTagAlreadyExistsException) GoString() string {
	return s.String()
}

func newErrorImageTagAlreadyExistsException(v protocol.ResponseMetadata) error {
	return &ImageTagAlreadyExistsException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ImageTagAlreadyExistsException) Code() string {
	return "ImageTagAlreadyExistsException"
}

// Message returns the exception's message.
func (s *ImageTagAlreadyExistsException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ImageTagAlreadyExistsException) OrigErr() error {
	return nil
}

func (s *ImageTagAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ImageTagAlreadyExistsException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ImageTagAlreadyExistsException) RequestID() string {
	return s.RespMetadata.RequestID
}

type InitiateLayerUploadInput struct {
	_ struct{} `type:"structure"`

	// The Amazon Web Services account ID associated with the registry to which
	// you intend to upload layers. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository to which you intend to upload layers.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InitiateLayerUploadInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InitiateLayerUploadInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *InitiateLayerUploadInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "InitiateLayerUploadInput"}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRegistryId sets the RegistryId field's value.
func (s *InitiateLayerUploadInput) SetRegistryId(v string) *InitiateLayerUploadInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *InitiateLayerUploadInput) SetRepositoryName(v string) *InitiateLayerUploadInput {
	s.RepositoryName = &v
	return s
}

type InitiateLayerUploadOutput struct {
	_ struct{} `type:"structure"`

	// The size, in bytes, that Amazon ECR expects future layer part uploads to
	// be.
	PartSize *int64 `locationName:"partSize" type:"long"`

	// The upload ID for the layer upload. This parameter is passed to further UploadLayerPart
	// and CompleteLayerUpload operations.
	UploadId *string `locationName:"uploadId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InitiateLayerUploadOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InitiateLayerUploadOutput) GoString() string {
	return s.String()
}

// SetPartSize sets the PartSize field's value.
func (s *InitiateLayerUploadOutput) SetPartSize(v int64) *InitiateLayerUploadOutput {
	s.PartSize = &v
	return s
}

// SetUploadId sets the UploadId field's value.
func (s *InitiateLayerUploadOutput) SetUploadId(v string) *InitiateLayerUploadOutput {
	s.UploadId = &v
	return s
}

// The layer digest calculation performed by Amazon ECR upon receipt of the
// image layer does not match the digest specified.
type InvalidLayerException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidLayerException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidLayerException) GoString() string {
	return s.String()
}

func newErrorInvalidLayerException(v protocol.ResponseMetadata) error {
	return &InvalidLayerException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidLayerException) Code() string {
	return "InvalidLayerException"
}

// Message returns the exception's message.
func (s *InvalidLayerException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidLayerException) OrigErr() error {
	return nil
}

func (s *InvalidLayerException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidLayerException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidLayerException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The layer part size is not valid, or the first byte specified is not consecutive
// to the last byte of a previous layer part upload.
type InvalidLayerPartException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The last valid byte received from the layer part upload that is associated
	// with the exception.
	LastValidByteReceived *int64 `locationName:"lastValidByteReceived" type:"long"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`

	// The registry ID associated with the exception.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the exception.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`

	// The upload ID associated with the exception.
	UploadId *string `locationName:"uploadId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidLayerPartException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidLayerPartException) GoString() string {
	return s.String()
}

func newErrorInvalidLayerPartException(v protocol.ResponseMetadata) error {
	return &InvalidLayerPartException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidLayerPartException) Code() string {
	return "InvalidLayerPartException"
}

// Message returns the exception's message.
func (s *InvalidLayerPartException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidLayerPartException) OrigErr() error {
	return nil
}

func (s *InvalidLayerPartException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidLayerPartException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidLayerPartException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The specified parameter is invalid. Review the available parameters for the
// API request.
type InvalidParameterException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidParameterException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidParameterException) GoString() string {
	return s.String()
}

func newErrorInvalidParameterException(v protocol.ResponseMetadata) error {
	return &InvalidParameterException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidParameterException) Code() string {
	return "InvalidParameterException"
}

// Message returns the exception's message.
func (s *InvalidParameterException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidParameterException) OrigErr() error {
	return nil
}

func (s *InvalidParameterException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidParameterException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidParameterException) RequestID() string {
	return s.RespMetadata.RequestID
}

// An invalid parameter has been specified. Tag keys can have a maximum character
// length of 128 characters, and tag values can have a maximum length of 256
// characters.
type InvalidTagParameterException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidTagParameterException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidTagParameterException) GoString() string {
	return s.String()
}

func newErrorInvalidTagParameterException(v protocol.ResponseMetadata) error {
	return &InvalidTagParameterException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidTagParameterException) Code() string {
	return "InvalidTagParameterException"
}

// Message returns the exception's message.
func (s *InvalidTagParameterException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidTagParameterException) OrigErr() error {
	return nil
}

func (s *InvalidTagParameterException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidTagParameterException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidTagParameterException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The operation failed due to a KMS exception.
type KmsException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error code returned by KMS.
	KmsError *string `locationName:"kmsError" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s KmsException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s KmsException) GoString() string {
	return s.String()
}

func newErrorKmsException(v protocol.ResponseMetadata) error {
	return &KmsException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *KmsException) Code() string {
	return "KmsException"
}

// Message returns the exception's message.
func (s *KmsException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *KmsException) OrigErr() error {
	return nil
}

func (s *KmsException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *KmsException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *KmsException) RequestID() string {
	return s.RespMetadata.RequestID
}

// An object representing an Amazon ECR image layer.
type Layer struct {
	_ struct{} `type:"structure"`

	// The availability status of the image layer.
	LayerAvailability *string `locationName:"layerAvailability" type:"string" enum:"LayerAvailability"`

	// The sha256 digest of the image layer.
	LayerDigest *string `locationName:"layerDigest" type:"string"`

	// The size, in bytes, of the image layer.
	LayerSize *int64 `locationName:"layerSize" type:"long"`

	// The media type of the layer, such as application/vnd.docker.image.rootfs.diff.tar.gzip
	// or application/vnd.oci.image.layer.v1.tar+gzip.
	MediaType *string `locationName:"mediaType" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Layer) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Layer) GoString() string {
	return s.String()
}

// SetLayerAvailability sets the LayerAvailability field's value.
func (s *Layer) SetLayerAvailability(v string) *Layer {
	s.LayerAvailability = &v
	return s
}

// SetLayerDigest sets the LayerDigest field's value.
func (s *Layer) SetLayerDigest(v string) *Layer {
	s.LayerDigest = &v
	return s
}

// SetLayerSize sets the LayerSize field's value.
func (s *Layer) SetLayerSize(v int64) *Layer {
	s.LayerSize = &v
	return s
}

// SetMediaType sets the MediaType field's value.
func (s *Layer) SetMediaType(v string) *Layer {
	s.MediaType = &v
	return s
}

// The image layer already exists in the associated repository.
type LayerAlreadyExistsException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayerAlreadyExistsException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayerAlreadyExistsException) GoString() string {
	return s.String()
}

func newErrorLayerAlreadyExistsException(v protocol.ResponseMetadata) error {
	return &LayerAlreadyExistsException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LayerAlreadyExistsException) Code() string {
	return "LayerAlreadyExistsException"
}

// Message returns the exception's message.
func (s *LayerAlreadyExistsException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LayerAlreadyExistsException) OrigErr() error {
	return nil
}

func (s *LayerAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LayerAlreadyExistsException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LayerAlreadyExistsException) RequestID() string {
	return s.RespMetadata.RequestID
}

// An object representing an Amazon ECR image layer failure.
type LayerFailure struct {
	_ struct{} `type:"structure"`

	// The failure code associated with the failure.
	FailureCode *string `locationName:"failureCode" type:"string" enum:"LayerFailureCode"`

	// The reason for the failure.
	FailureReason *string `locationName:"failureReason" type:"string"`

	// The layer digest associated with the failure.
	LayerDigest *string `locationName:"layerDigest" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayerFailure) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayerFailure) GoString() string {
	return s.String()
}

// SetFailureCode sets the FailureCode field's value.
func (s *LayerFailure) SetFailureCode(v string) *LayerFailure {
	s.FailureCode = &v
	return s
}

// SetFailureReason sets the FailureReason field's value.
func (s *LayerFailure) SetFailureReason(v string) *LayerFailure {
	s.FailureReason = &v
	return s
}

// SetLayerDigest sets the LayerDigest field's value.
func (s *LayerFailure) SetLayerDigest(v string) *LayerFailure {
	s.LayerDigest = &v
	return s
}

// The specified layer is not available because it is not associated with an
// image. Unassociated image layers may be cleaned up at any time.
type LayerInaccessibleException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayerInaccessibleException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayerInaccessibleException) GoString() string {
	return s.String()
}

func newErrorLayerInaccessibleException(v protocol.ResponseMetadata) error {
	return &LayerInaccessibleException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LayerInaccessibleException) Code() string {
	return "LayerInaccessibleException"
}

// Message returns the exception's message.
func (s *LayerInaccessibleException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LayerInaccessibleException) OrigErr() error {
	return nil
}

func (s *LayerInaccessibleException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LayerInaccessibleException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LayerInaccessibleException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Layer parts must be at least 5 MiB in size.
type LayerPartTooSmallException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayerPartTooSmallException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayerPartTooSmallException) GoString() string {
	return s.String()
}

func newErrorLayerPartTooSmallException(v protocol.ResponseMetadata) error {
	return &LayerPartTooSmallException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LayerPartTooSmallException) Code() string {
	return "LayerPartTooSmallException"
}

// Message returns the exception's message.
func (s *LayerPartTooSmallException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LayerPartTooSmallException) OrigErr() error {
	return nil
}

func (s *LayerPartTooSmallException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LayerPartTooSmallException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LayerPartTooSmallException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The specified layers could not be found, or the specified layer is not valid
// for this repository.
type LayersNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayersNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LayersNotFoundException) GoString() string {
	return s.String()
}

func newErrorLayersNotFoundException(v protocol.ResponseMetadata) error {
	return &LayersNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LayersNotFoundException) Code() string {
	return "LayersNotFoundException"
}

// Message returns the exception's message.
func (s *LayersNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LayersNotFoundException) OrigErr() error {
	return nil
}

func (s *LayersNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LayersNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LayersNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The lifecycle policy could not be found, and no policy is set to the repository.
type LifecyclePolicyNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyNotFoundException) GoString() string {
	return s.String()
}

func newErrorLifecyclePolicyNotFoundException(v protocol.ResponseMetadata) error {
	return &LifecyclePolicyNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LifecyclePolicyNotFoundException) Code() string {
	return "LifecyclePolicyNotFoundException"
}

// Message returns the exception's message.
func (s *LifecyclePolicyNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LifecyclePolicyNotFoundException) OrigErr() error {
	return nil
}

func (s *LifecyclePolicyNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LifecyclePolicyNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LifecyclePolicyNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The filter for the lifecycle policy preview.
type LifecyclePolicyPreviewFilter struct {
	_ struct{} `type:"structure"`

	// The tag status of the image.
	TagStatus *string `locationName:"tagStatus" type:"string" enum:"TagStatus"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewFilter) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewFilter) GoString() string {
	return s.String()
}

// SetTagStatus sets the TagStatus field's value.
func (s *LifecyclePolicyPreviewFilter) SetTagStatus(v string) *LifecyclePolicyPreviewFilter {
	s.TagStatus = &v
	return s
}

// The previous lifecycle policy preview request has not completed. Wait and
// try again.
type LifecyclePolicyPreviewInProgressException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewInProgressException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewInProgressException) GoString() string {
	return s.String()
}

func newErrorLifecyclePolicyPreviewInProgressException(v protocol.ResponseMetadata) error {
	return &LifecyclePolicyPreviewInProgressException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LifecyclePolicyPreviewInProgressException) Code() string {
	return "LifecyclePolicyPreviewInProgressException"
}

// Message returns the exception's message.
func (s *LifecyclePolicyPreviewInProgressException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LifecyclePolicyPreviewInProgressException) OrigErr() error {
	return nil
}

func (s *LifecyclePolicyPreviewInProgressException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LifecyclePolicyPreviewInProgressException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LifecyclePolicyPreviewInProgressException) RequestID() string {
	return s.RespMetadata.RequestID
}

// There is no dry run for this repository.
type LifecyclePolicyPreviewNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewNotFoundException) GoString() string {
	return s.String()
}

func newErrorLifecyclePolicyPreviewNotFoundException(v protocol.ResponseMetadata) error {
	return &LifecyclePolicyPreviewNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LifecyclePolicyPreviewNotFoundException) Code() string {
	return "LifecyclePolicyPreviewNotFoundException"
}

// Message returns the exception's message.
func (s *LifecyclePolicyPreviewNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LifecyclePolicyPreviewNotFoundException) OrigErr() error {
	return nil
}

func (s *LifecyclePolicyPreviewNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LifecyclePolicyPreviewNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LifecyclePolicyPreviewNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The result of the lifecycle policy preview.
type LifecyclePolicyPreviewResult struct {
	_ struct{} `type:"structure"`

	// The type of action to be taken.
	Action *LifecyclePolicyRuleAction `locationName:"action" type:"structure"`

	// The priority of the applied rule.
	AppliedRulePriority *int64 `locationName:"appliedRulePriority" min:"1" type:"integer"`

	// The sha256 digest of the image manifest.
	ImageDigest *string `locationName:"imageDigest" type:"string"`

	// The date and time, expressed in standard JavaScript date format, at which
	// the current image was pushed to the repository.
	ImagePushedAt *time.Time `locationName:"imagePushedAt" type:"timestamp"`

	// The list of tags associated with this image.
	ImageTags []*string `locationName:"imageTags" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewResult) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewResult) GoString() string {
	return s.String()
}

// SetAction sets the Action field's value.
func (s *LifecyclePolicyPreviewResult) SetAction(v *LifecyclePolicyRuleAction) *LifecyclePolicyPreviewResult {
	s.Action = v
	return s
}

// SetAppliedRulePriority sets the AppliedRulePriority field's value.
func (s *LifecyclePolicyPreviewResult) SetAppliedRulePriority(v int64) *LifecyclePolicyPreviewResult {
	s.AppliedRulePriority = &v
	return s
}

// SetImageDigest sets the ImageDigest field's value.
func (s *LifecyclePolicyPreviewResult) SetImageDigest(v string) *LifecyclePolicyPreviewResult {
	s.ImageDigest = &v
	return s
}

// SetImagePushedAt sets the ImagePushedAt field's value.
func (s *LifecyclePolicyPreviewResult) SetImagePushedAt(v time.Time) *LifecyclePolicyPreviewResult {
	s.ImagePushedAt = &v
	return s
}

// SetImageTags sets the ImageTags field's value.
func (s *LifecyclePolicyPreviewResult) SetImageTags(v []*string) *LifecyclePolicyPreviewResult {
	s.ImageTags = v
	return s
}

// The summary of the lifecycle policy preview request.
type LifecyclePolicyPreviewSummary struct {
	_ struct{} `type:"structure"`

	// The number of expiring images.
	ExpiringImageTotalCount *int64 `locationName:"expiringImageTotalCount" type:"integer"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewSummary) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyPreviewSummary) GoString() string {
	return s.String()
}

// SetExpiringImageTotalCount sets the ExpiringImageTotalCount field's value.
func (s *LifecyclePolicyPreviewSummary) SetExpiringImageTotalCount(v int64) *LifecyclePolicyPreviewSummary {
	s.ExpiringImageTotalCount = &v
	return s
}

// The type of action to be taken.
type LifecyclePolicyRuleAction struct {
	_ struct{} `type:"structure"`

	// The type of action to be taken.
	Type *string `locationName:"type" type:"string" enum:"ImageActionType"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyRuleAction) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LifecyclePolicyRuleAction) GoString() string {
	return s.String()
}

// SetType sets the Type field's value.
func (s *LifecyclePolicyRuleAction) SetType(v string) *LifecyclePolicyRuleAction {
	s.Type = &v
	return s
}

// The operation did not succeed because it would have exceeded a service limit
// for your account. For more information, see Amazon ECR service quotas (https://docs.aws.amazon.com/AmazonECR/latest/userguide/service-quotas.html)
// in the Amazon Elastic Container Registry User Guide.
type LimitExceededException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LimitExceededException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s LimitExceededException) GoString() string {
	return s.String()
}

func newErrorLimitExceededException(v protocol.ResponseMetadata) error {
	return &LimitExceededException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *LimitExceededException) Code() string {
	return "LimitExceededException"
}

// Message returns the exception's message.
func (s *LimitExceededException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *LimitExceededException) OrigErr() error {
	return nil
}

func (s *LimitExceededException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *LimitExceededException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *LimitExceededException) RequestID() string {
	return s.RespMetadata.RequestID
}

// An object representing a filter on a ListImages operation.
type ListImagesFilter struct {
	_ struct{} `type:"structure"`

	// The tag status with which to filter your ListImages results. You can filter
	// results based on whether they are TAGGED or UNTAGGED.
	TagStatus *string `locationName:"tagStatus" type:"string" enum:"TagStatus"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListImagesFilter) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListImagesFilter) GoString() string {
	return s.String()
}

// SetTagStatus sets the TagStatus field's value.
func (s *ListImagesFilter) SetTagStatus(v string) *ListImagesFilter {
	s.TagStatus = &v
	return s
}

type ListImagesInput struct {
	_ struct{} `type:"structure"`

	// The filter key and value with which to filter your ListImages results.
	Filter *ListImagesFilter `locationName:"filter" type:"structure"`

	// The maximum number of image results returned by ListImages in paginated output.
	// When this parameter is used, ListImages only returns maxResults results in
	// a single page along with a nextToken response element. The remaining results
	// of the initial request can be seen by sending another ListImages request
	// with the returned nextToken value. This value can be between 1 and 1000.
	// If this parameter is not used, then ListImages returns up to 100 results
	// and a nextToken value, if applicable.
	MaxResults *int64 `locationName:"maxResults" min:"1" type:"integer"`

	// The nextToken value returned from a previous paginated ListImages request
	// where maxResults was used and the results exceeded the value of that parameter.
	// Pagination continues from the end of the previous results that returned the
	// nextToken value. This value is null when there are no more results to return.
	//
	// This token should be treated as an opaque identifier that is only used to
	// retrieve the next items in a list and not for other programmatic purposes.
	NextToken *string `locationName:"nextToken" type:"string"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository in which to list images. If you do not specify a registry,
	// the default registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository with image IDs to be listed.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListImagesInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListImagesInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ListImagesInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ListImagesInput"}
	if s.MaxResults != nil && *s.MaxResults < 1 {
		invalidParams.Add(request.NewErrParamMinValue("MaxResults", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetFilter sets the Filter field's value.
func (s *ListImagesInput) SetFilter(v *ListImagesFilter) *ListImagesInput {
	s.Filter = v
	return s
}

// SetMaxResults sets the MaxResults field's value.
func (s *ListImagesInput) SetMaxResults(v int64) *ListImagesInput {
	s.MaxResults = &v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListImagesInput) SetNextToken(v string) *ListImagesInput {
	s.NextToken = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *ListImagesInput) SetRegistryId(v string) *ListImagesInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *ListImagesInput) SetRepositoryName(v string) *ListImagesInput {
	s.RepositoryName = &v
	return s
}

type ListImagesOutput struct {
	_ struct{} `type:"structure"`

	// The list of image IDs for the requested repository.
	ImageIds []*ImageIdentifier `locationName:"imageIds" min:"1" type:"list"`

	// The nextToken value to include in a future ListImages request. When the results
	// of a ListImages request exceed maxResults, this value can be used to retrieve
	// the next page of results. This value is null when there are no more results
	// to return.
	NextToken *string `locationName:"nextToken" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListImagesOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListImagesOutput) GoString() string {
	return s.String()
}

// SetImageIds sets the ImageIds field's value.
func (s *ListImagesOutput) SetImageIds(v []*ImageIdentifier) *ListImagesOutput {
	s.ImageIds = v
	return s
}

// SetNextToken sets the NextToken field's value.
func (s *ListImagesOutput) SetNextToken(v string) *ListImagesOutput {
	s.NextToken = &v
	return s
}

type ListTagsForResourceInput struct {
	_ struct{} `type:"structure"`

	// The Amazon Resource Name (ARN) that identifies the resource for which to
	// list the tags. Currently, the only supported resource is an Amazon ECR repository.
	//
	// ResourceArn is a required field
	ResourceArn *string `locationName:"resourceArn" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListTagsForResourceInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListTagsForResourceInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ListTagsForResourceInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ListTagsForResourceInput"}
	if s.ResourceArn == nil {
		invalidParams.Add(request.NewErrParamRequired("ResourceArn"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetResourceArn sets the ResourceArn field's value.
func (s *ListTagsForResourceInput) SetResourceArn(v string) *ListTagsForResourceInput {
	s.ResourceArn = &v
	return s
}

type ListTagsForResourceOutput struct {
	_ struct{} `type:"structure"`

	// The tags for the resource.
	Tags []*Tag `locationName:"tags" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListTagsForResourceOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ListTagsForResourceOutput) GoString() string {
	return s.String()
}

// SetTags sets the Tags field's value.
func (s *ListTagsForResourceOutput) SetTags(v []*Tag) *ListTagsForResourceOutput {
	s.Tags = v
	return s
}

// Information about a package vulnerability finding.
type PackageVulnerabilityDetails struct {
	_ struct{} `type:"structure"`

	// An object that contains details about the CVSS score of a finding.
	Cvss []*CvssScore `locationName:"cvss" type:"list"`

	// One or more URLs that contain details about this vulnerability type.
	ReferenceUrls []*string `locationName:"referenceUrls" type:"list"`

	// One or more vulnerabilities related to the one identified in this finding.
	RelatedVulnerabilities []*string `locationName:"relatedVulnerabilities" type:"list"`

	// The source of the vulnerability information.
	Source *string `locationName:"source" type:"string"`

	// A URL to the source of the vulnerability information.
	SourceUrl *string `locationName:"sourceUrl" type:"string"`

	// The date and time that this vulnerability was first added to the vendor's
	// database.
	VendorCreatedAt *time.Time `locationName:"vendorCreatedAt" type:"timestamp"`

	// The severity the vendor has given to this vulnerability type.
	VendorSeverity *string `locationName:"vendorSeverity" type:"string"`

	// The date and time the vendor last updated this vulnerability in their database.
	VendorUpdatedAt *time.Time `locationName:"vendorUpdatedAt" type:"timestamp"`

	// The ID given to this vulnerability.
	VulnerabilityId *string `locationName:"vulnerabilityId" type:"string"`

	// The packages impacted by this vulnerability.
	VulnerablePackages []*VulnerablePackage `locationName:"vulnerablePackages" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PackageVulnerabilityDetails) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PackageVulnerabilityDetails) GoString() string {
	return s.String()
}

// SetCvss sets the Cvss field's value.
func (s *PackageVulnerabilityDetails) SetCvss(v []*CvssScore) *PackageVulnerabilityDetails {
	s.Cvss = v
	return s
}

// SetReferenceUrls sets the ReferenceUrls field's value.
func (s *PackageVulnerabilityDetails) SetReferenceUrls(v []*string) *PackageVulnerabilityDetails {
	s.ReferenceUrls = v
	return s
}

// SetRelatedVulnerabilities sets the RelatedVulnerabilities field's value.
func (s *PackageVulnerabilityDetails) SetRelatedVulnerabilities(v []*string) *PackageVulnerabilityDetails {
	s.RelatedVulnerabilities = v
	return s
}

// SetSource sets the Source field's value.
func (s *PackageVulnerabilityDetails) SetSource(v string) *PackageVulnerabilityDetails {
	s.Source = &v
	return s
}

// SetSourceUrl sets the SourceUrl field's value.
func (s *PackageVulnerabilityDetails) SetSourceUrl(v string) *PackageVulnerabilityDetails {
	s.SourceUrl = &v
	return s
}

// SetVendorCreatedAt sets the VendorCreatedAt field's value.
func (s *PackageVulnerabilityDetails) SetVendorCreatedAt(v time.Time) *PackageVulnerabilityDetails {
	s.VendorCreatedAt = &v
	return s
}

// SetVendorSeverity sets the VendorSeverity field's value.
func (s *PackageVulnerabilityDetails) SetVendorSeverity(v string) *PackageVulnerabilityDetails {
	s.VendorSeverity = &v
	return s
}

// SetVendorUpdatedAt sets the VendorUpdatedAt field's value.
func (s *PackageVulnerabilityDetails) SetVendorUpdatedAt(v time.Time) *PackageVulnerabilityDetails {
	s.VendorUpdatedAt = &v
	return s
}

// SetVulnerabilityId sets the VulnerabilityId field's value.
func (s *PackageVulnerabilityDetails) SetVulnerabilityId(v string) *PackageVulnerabilityDetails {
	s.VulnerabilityId = &v
	return s
}

// SetVulnerablePackages sets the VulnerablePackages field's value.
func (s *PackageVulnerabilityDetails) SetVulnerablePackages(v []*VulnerablePackage) *PackageVulnerabilityDetails {
	s.VulnerablePackages = v
	return s
}

// The details of a pull through cache rule.
type PullThroughCacheRule struct {
	_ struct{} `type:"structure"`

	// The date and time the pull through cache was created.
	CreatedAt *time.Time `locationName:"createdAt" type:"timestamp"`

	// The Amazon ECR repository prefix associated with the pull through cache rule.
	EcrRepositoryPrefix *string `locationName:"ecrRepositoryPrefix" min:"2" type:"string"`

	// The Amazon Web Services account ID associated with the registry the pull
	// through cache rule is associated with.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The upstream registry URL associated with the pull through cache rule.
	UpstreamRegistryUrl *string `locationName:"upstreamRegistryUrl" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PullThroughCacheRule) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PullThroughCacheRule) GoString() string {
	return s.String()
}

// SetCreatedAt sets the CreatedAt field's value.
func (s *PullThroughCacheRule) SetCreatedAt(v time.Time) *PullThroughCacheRule {
	s.CreatedAt = &v
	return s
}

// SetEcrRepositoryPrefix sets the EcrRepositoryPrefix field's value.
func (s *PullThroughCacheRule) SetEcrRepositoryPrefix(v string) *PullThroughCacheRule {
	s.EcrRepositoryPrefix = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PullThroughCacheRule) SetRegistryId(v string) *PullThroughCacheRule {
	s.RegistryId = &v
	return s
}

// SetUpstreamRegistryUrl sets the UpstreamRegistryUrl field's value.
func (s *PullThroughCacheRule) SetUpstreamRegistryUrl(v string) *PullThroughCacheRule {
	s.UpstreamRegistryUrl = &v
	return s
}

// A pull through cache rule with these settings already exists for the private
// registry.
type PullThroughCacheRuleAlreadyExistsException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PullThroughCacheRuleAlreadyExistsException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PullThroughCacheRuleAlreadyExistsException) GoString() string {
	return s.String()
}

func newErrorPullThroughCacheRuleAlreadyExistsException(v protocol.ResponseMetadata) error {
	return &PullThroughCacheRuleAlreadyExistsException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *PullThroughCacheRuleAlreadyExistsException) Code() string {
	return "PullThroughCacheRuleAlreadyExistsException"
}

// Message returns the exception's message.
func (s *PullThroughCacheRuleAlreadyExistsException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *PullThroughCacheRuleAlreadyExistsException) OrigErr() error {
	return nil
}

func (s *PullThroughCacheRuleAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *PullThroughCacheRuleAlreadyExistsException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *PullThroughCacheRuleAlreadyExistsException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The pull through cache rule was not found. Specify a valid pull through cache
// rule and try again.
type PullThroughCacheRuleNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PullThroughCacheRuleNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PullThroughCacheRuleNotFoundException) GoString() string {
	return s.String()
}

func newErrorPullThroughCacheRuleNotFoundException(v protocol.ResponseMetadata) error {
	return &PullThroughCacheRuleNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *PullThroughCacheRuleNotFoundException) Code() string {
	return "PullThroughCacheRuleNotFoundException"
}

// Message returns the exception's message.
func (s *PullThroughCacheRuleNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *PullThroughCacheRuleNotFoundException) OrigErr() error {
	return nil
}

func (s *PullThroughCacheRuleNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *PullThroughCacheRuleNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *PullThroughCacheRuleNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

type PutImageInput struct {
	_ struct{} `type:"structure"`

	// The image digest of the image manifest corresponding to the image.
	ImageDigest *string `locationName:"imageDigest" type:"string"`

	// The image manifest corresponding to the image to be uploaded.
	//
	// ImageManifest is a required field
	ImageManifest *string `locationName:"imageManifest" min:"1" type:"string" required:"true"`

	// The media type of the image manifest. If you push an image manifest that
	// does not contain the mediaType field, you must specify the imageManifestMediaType
	// in the request.
	ImageManifestMediaType *string `locationName:"imageManifestMediaType" type:"string"`

	// The tag to associate with the image. This parameter is required for images
	// that use the Docker Image Manifest V2 Schema 2 or Open Container Initiative
	// (OCI) formats.
	ImageTag *string `locationName:"imageTag" min:"1" type:"string"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository in which to put the image. If you do not specify a registry,
	// the default registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository in which to put the image.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *PutImageInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "PutImageInput"}
	if s.ImageManifest == nil {
		invalidParams.Add(request.NewErrParamRequired("ImageManifest"))
	}
	if s.ImageManifest != nil && len(*s.ImageManifest) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ImageManifest", 1))
	}
	if s.ImageTag != nil && len(*s.ImageTag) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("ImageTag", 1))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetImageDigest sets the ImageDigest field's value.
func (s *PutImageInput) SetImageDigest(v string) *PutImageInput {
	s.ImageDigest = &v
	return s
}

// SetImageManifest sets the ImageManifest field's value.
func (s *PutImageInput) SetImageManifest(v string) *PutImageInput {
	s.ImageManifest = &v
	return s
}

// SetImageManifestMediaType sets the ImageManifestMediaType field's value.
func (s *PutImageInput) SetImageManifestMediaType(v string) *PutImageInput {
	s.ImageManifestMediaType = &v
	return s
}

// SetImageTag sets the ImageTag field's value.
func (s *PutImageInput) SetImageTag(v string) *PutImageInput {
	s.ImageTag = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PutImageInput) SetRegistryId(v string) *PutImageInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *PutImageInput) SetRepositoryName(v string) *PutImageInput {
	s.RepositoryName = &v
	return s
}

type PutImageOutput struct {
	_ struct{} `type:"structure"`

	// Details of the image uploaded.
	Image *Image `locationName:"image" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageOutput) GoString() string {
	return s.String()
}

// SetImage sets the Image field's value.
func (s *PutImageOutput) SetImage(v *Image) *PutImageOutput {
	s.Image = v
	return s
}

type PutImageScanningConfigurationInput struct {
	_ struct{} `type:"structure"`

	// The image scanning configuration for the repository. This setting determines
	// whether images are scanned for known vulnerabilities after being pushed to
	// the repository.
	//
	// ImageScanningConfiguration is a required field
	ImageScanningConfiguration *ImageScanningConfiguration `locationName:"imageScanningConfiguration" type:"structure" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository in which to update the image scanning configuration setting.
	// If you do not specify a registry, the default registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository in which to update the image scanning configuration
	// setting.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageScanningConfigurationInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageScanningConfigurationInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *PutImageScanningConfigurationInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "PutImageScanningConfigurationInput"}
	if s.ImageScanningConfiguration == nil {
		invalidParams.Add(request.NewErrParamRequired("ImageScanningConfiguration"))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetImageScanningConfiguration sets the ImageScanningConfiguration field's value.
func (s *PutImageScanningConfigurationInput) SetImageScanningConfiguration(v *ImageScanningConfiguration) *PutImageScanningConfigurationInput {
	s.ImageScanningConfiguration = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PutImageScanningConfigurationInput) SetRegistryId(v string) *PutImageScanningConfigurationInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *PutImageScanningConfigurationInput) SetRepositoryName(v string) *PutImageScanningConfigurationInput {
	s.RepositoryName = &v
	return s
}

type PutImageScanningConfigurationOutput struct {
	_ struct{} `type:"structure"`

	// The image scanning configuration setting for the repository.
	ImageScanningConfiguration *ImageScanningConfiguration `locationName:"imageScanningConfiguration" type:"structure"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageScanningConfigurationOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageScanningConfigurationOutput) GoString() string {
	return s.String()
}

// SetImageScanningConfiguration sets the ImageScanningConfiguration field's value.
func (s *PutImageScanningConfigurationOutput) SetImageScanningConfiguration(v *ImageScanningConfiguration) *PutImageScanningConfigurationOutput {
	s.ImageScanningConfiguration = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PutImageScanningConfigurationOutput) SetRegistryId(v string) *PutImageScanningConfigurationOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *PutImageScanningConfigurationOutput) SetRepositoryName(v string) *PutImageScanningConfigurationOutput {
	s.RepositoryName = &v
	return s
}

type PutImageTagMutabilityInput struct {
	_ struct{} `type:"structure"`

	// The tag mutability setting for the repository. If MUTABLE is specified, image
	// tags can be overwritten. If IMMUTABLE is specified, all image tags within
	// the repository will be immutable which will prevent them from being overwritten.
	//
	// ImageTagMutability is a required field
	ImageTagMutability *string `locationName:"imageTagMutability" type:"string" required:"true" enum:"ImageTagMutability"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository in which to update the image tag mutability settings. If you
	// do not specify a registry, the default registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository in which to update the image tag mutability settings.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageTagMutabilityInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageTagMutabilityInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *PutImageTagMutabilityInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "PutImageTagMutabilityInput"}
	if s.ImageTagMutability == nil {
		invalidParams.Add(request.NewErrParamRequired("ImageTagMutability"))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetImageTagMutability sets the ImageTagMutability field's value.
func (s *PutImageTagMutabilityInput) SetImageTagMutability(v string) *PutImageTagMutabilityInput {
	s.ImageTagMutability = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PutImageTagMutabilityInput) SetRegistryId(v string) *PutImageTagMutabilityInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *PutImageTagMutabilityInput) SetRepositoryName(v string) *PutImageTagMutabilityInput {
	s.RepositoryName = &v
	return s
}

type PutImageTagMutabilityOutput struct {
	_ struct{} `type:"structure"`

	// The image tag mutability setting for the repository.
	ImageTagMutability *string `locationName:"imageTagMutability" type:"string" enum:"ImageTagMutability"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageTagMutabilityOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutImageTagMutabilityOutput) GoString() string {
	return s.String()
}

// SetImageTagMutability sets the ImageTagMutability field's value.
func (s *PutImageTagMutabilityOutput) SetImageTagMutability(v string) *PutImageTagMutabilityOutput {
	s.ImageTagMutability = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PutImageTagMutabilityOutput) SetRegistryId(v string) *PutImageTagMutabilityOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *PutImageTagMutabilityOutput) SetRepositoryName(v string) *PutImageTagMutabilityOutput {
	s.RepositoryName = &v
	return s
}

type PutLifecyclePolicyInput struct {
	_ struct{} `type:"structure"`

	// The JSON repository policy text to apply to the repository.
	//
	// LifecyclePolicyText is a required field
	LifecyclePolicyText *string `locationName:"lifecyclePolicyText" min:"100" type:"string" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository. If you do not specify a registry, the default registry is
	// assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository to receive the policy.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutLifecyclePolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutLifecyclePolicyInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *PutLifecyclePolicyInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "PutLifecyclePolicyInput"}
	if s.LifecyclePolicyText == nil {
		invalidParams.Add(request.NewErrParamRequired("LifecyclePolicyText"))
	}
	if s.LifecyclePolicyText != nil && len(*s.LifecyclePolicyText) < 100 {
		invalidParams.Add(request.NewErrParamMinLen("LifecyclePolicyText", 100))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetLifecyclePolicyText sets the LifecyclePolicyText field's value.
func (s *PutLifecyclePolicyInput) SetLifecyclePolicyText(v string) *PutLifecyclePolicyInput {
	s.LifecyclePolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PutLifecyclePolicyInput) SetRegistryId(v string) *PutLifecyclePolicyInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *PutLifecyclePolicyInput) SetRepositoryName(v string) *PutLifecyclePolicyInput {
	s.RepositoryName = &v
	return s
}

type PutLifecyclePolicyOutput struct {
	_ struct{} `type:"structure"`

	// The JSON repository policy text.
	LifecyclePolicyText *string `locationName:"lifecyclePolicyText" min:"100" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutLifecyclePolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutLifecyclePolicyOutput) GoString() string {
	return s.String()
}

// SetLifecyclePolicyText sets the LifecyclePolicyText field's value.
func (s *PutLifecyclePolicyOutput) SetLifecyclePolicyText(v string) *PutLifecyclePolicyOutput {
	s.LifecyclePolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PutLifecyclePolicyOutput) SetRegistryId(v string) *PutLifecyclePolicyOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *PutLifecyclePolicyOutput) SetRepositoryName(v string) *PutLifecyclePolicyOutput {
	s.RepositoryName = &v
	return s
}

type PutRegistryPolicyInput struct {
	_ struct{} `type:"structure"`

	// The JSON policy text to apply to your registry. The policy text follows the
	// same format as IAM policy text. For more information, see Registry permissions
	// (https://docs.aws.amazon.com/AmazonECR/latest/userguide/registry-permissions.html)
	// in the Amazon Elastic Container Registry User Guide.
	//
	// PolicyText is a required field
	PolicyText *string `locationName:"policyText" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutRegistryPolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutRegistryPolicyInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *PutRegistryPolicyInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "PutRegistryPolicyInput"}
	if s.PolicyText == nil {
		invalidParams.Add(request.NewErrParamRequired("PolicyText"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetPolicyText sets the PolicyText field's value.
func (s *PutRegistryPolicyInput) SetPolicyText(v string) *PutRegistryPolicyInput {
	s.PolicyText = &v
	return s
}

type PutRegistryPolicyOutput struct {
	_ struct{} `type:"structure"`

	// The JSON policy text for your registry.
	PolicyText *string `locationName:"policyText" type:"string"`

	// The registry ID.
	RegistryId *string `locationName:"registryId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutRegistryPolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutRegistryPolicyOutput) GoString() string {
	return s.String()
}

// SetPolicyText sets the PolicyText field's value.
func (s *PutRegistryPolicyOutput) SetPolicyText(v string) *PutRegistryPolicyOutput {
	s.PolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *PutRegistryPolicyOutput) SetRegistryId(v string) *PutRegistryPolicyOutput {
	s.RegistryId = &v
	return s
}

type PutRegistryScanningConfigurationInput struct {
	_ struct{} `type:"structure"`

	// The scanning rules to use for the registry. A scanning rule is used to determine
	// which repository filters are used and at what frequency scanning will occur.
	Rules []*RegistryScanningRule `locationName:"rules" type:"list"`

	// The scanning type to set for the registry.
	//
	// When a registry scanning configuration is not defined, by default the BASIC
	// scan type is used. When basic scanning is used, you may specify filters to
	// determine which individual repositories, or all repositories, are scanned
	// when new images are pushed to those repositories. Alternatively, you can
	// do manual scans of images with basic scanning.
	//
	// When the ENHANCED scan type is set, Amazon Inspector provides automated vulnerability
	// scanning. You may choose between continuous scanning or scan on push and
	// you may specify filters to determine which individual repositories, or all
	// repositories, are scanned.
	ScanType *string `locationName:"scanType" type:"string" enum:"ScanType"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutRegistryScanningConfigurationInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutRegistryScanningConfigurationInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *PutRegistryScanningConfigurationInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "PutRegistryScanningConfigurationInput"}
	if s.Rules != nil {
		for i, v := range s.Rules {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Rules", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRules sets the Rules field's value.
func (s *PutRegistryScanningConfigurationInput) SetRules(v []*RegistryScanningRule) *PutRegistryScanningConfigurationInput {
	s.Rules = v
	return s
}

// SetScanType sets the ScanType field's value.
func (s *PutRegistryScanningConfigurationInput) SetScanType(v string) *PutRegistryScanningConfigurationInput {
	s.ScanType = &v
	return s
}

type PutRegistryScanningConfigurationOutput struct {
	_ struct{} `type:"structure"`

	// The scanning configuration for your registry.
	RegistryScanningConfiguration *RegistryScanningConfiguration `locationName:"registryScanningConfiguration" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutRegistryScanningConfigurationOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutRegistryScanningConfigurationOutput) GoString() string {
	return s.String()
}

// SetRegistryScanningConfiguration sets the RegistryScanningConfiguration field's value.
func (s *PutRegistryScanningConfigurationOutput) SetRegistryScanningConfiguration(v *RegistryScanningConfiguration) *PutRegistryScanningConfigurationOutput {
	s.RegistryScanningConfiguration = v
	return s
}

type PutReplicationConfigurationInput struct {
	_ struct{} `type:"structure"`

	// An object representing the replication configuration for a registry.
	//
	// ReplicationConfiguration is a required field
	ReplicationConfiguration *ReplicationConfiguration `locationName:"replicationConfiguration" type:"structure" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutReplicationConfigurationInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutReplicationConfigurationInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *PutReplicationConfigurationInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "PutReplicationConfigurationInput"}
	if s.ReplicationConfiguration == nil {
		invalidParams.Add(request.NewErrParamRequired("ReplicationConfiguration"))
	}
	if s.ReplicationConfiguration != nil {
		if err := s.ReplicationConfiguration.Validate(); err != nil {
			invalidParams.AddNested("ReplicationConfiguration", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetReplicationConfiguration sets the ReplicationConfiguration field's value.
func (s *PutReplicationConfigurationInput) SetReplicationConfiguration(v *ReplicationConfiguration) *PutReplicationConfigurationInput {
	s.ReplicationConfiguration = v
	return s
}

type PutReplicationConfigurationOutput struct {
	_ struct{} `type:"structure"`

	// The contents of the replication configuration for the registry.
	ReplicationConfiguration *ReplicationConfiguration `locationName:"replicationConfiguration" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutReplicationConfigurationOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s PutReplicationConfigurationOutput) GoString() string {
	return s.String()
}

// SetReplicationConfiguration sets the ReplicationConfiguration field's value.
func (s *PutReplicationConfigurationOutput) SetReplicationConfiguration(v *ReplicationConfiguration) *PutReplicationConfigurationOutput {
	s.ReplicationConfiguration = v
	return s
}

// Details about the recommended course of action to remediate the finding.
type Recommendation struct {
	_ struct{} `type:"structure"`

	// The recommended course of action to remediate the finding.
	Text *string `locationName:"text" type:"string"`

	// The URL address to the CVE remediation recommendations.
	Url *string `locationName:"url" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Recommendation) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Recommendation) GoString() string {
	return s.String()
}

// SetText sets the Text field's value.
func (s *Recommendation) SetText(v string) *Recommendation {
	s.Text = &v
	return s
}

// SetUrl sets the Url field's value.
func (s *Recommendation) SetUrl(v string) *Recommendation {
	s.Url = &v
	return s
}

// The manifest list is referencing an image that does not exist.
type ReferencedImagesNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ReferencedImagesNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ReferencedImagesNotFoundException) GoString() string {
	return s.String()
}

func newErrorReferencedImagesNotFoundException(v protocol.ResponseMetadata) error {
	return &ReferencedImagesNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ReferencedImagesNotFoundException) Code() string {
	return "ReferencedImagesNotFoundException"
}

// Message returns the exception's message.
func (s *ReferencedImagesNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ReferencedImagesNotFoundException) OrigErr() error {
	return nil
}

func (s *ReferencedImagesNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ReferencedImagesNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ReferencedImagesNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The registry doesn't have an associated registry policy.
type RegistryPolicyNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegistryPolicyNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegistryPolicyNotFoundException) GoString() string {
	return s.String()
}

func newErrorRegistryPolicyNotFoundException(v protocol.ResponseMetadata) error {
	return &RegistryPolicyNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *RegistryPolicyNotFoundException) Code() string {
	return "RegistryPolicyNotFoundException"
}

// Message returns the exception's message.
func (s *RegistryPolicyNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *RegistryPolicyNotFoundException) OrigErr() error {
	return nil
}

func (s *RegistryPolicyNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *RegistryPolicyNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *RegistryPolicyNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The scanning configuration for a private registry.
type RegistryScanningConfiguration struct {
	_ struct{} `type:"structure"`

	// The scanning rules associated with the registry.
	Rules []*RegistryScanningRule `locationName:"rules" type:"list"`

	// The type of scanning configured for the registry.
	ScanType *string `locationName:"scanType" type:"string" enum:"ScanType"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegistryScanningConfiguration) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegistryScanningConfiguration) GoString() string {
	return s.String()
}

// SetRules sets the Rules field's value.
func (s *RegistryScanningConfiguration) SetRules(v []*RegistryScanningRule) *RegistryScanningConfiguration {
	s.Rules = v
	return s
}

// SetScanType sets the ScanType field's value.
func (s *RegistryScanningConfiguration) SetScanType(v string) *RegistryScanningConfiguration {
	s.ScanType = &v
	return s
}

// The details of a scanning rule for a private registry.
type RegistryScanningRule struct {
	_ struct{} `type:"structure"`

	// The repository filters associated with the scanning configuration for a private
	// registry.
	//
	// RepositoryFilters is a required field
	RepositoryFilters []*ScanningRepositoryFilter `locationName:"repositoryFilters" type:"list" required:"true"`

	// The frequency that scans are performed at for a private registry. When the
	// ENHANCED scan type is specified, the supported scan frequencies are CONTINUOUS_SCAN
	// and SCAN_ON_PUSH. When the BASIC scan type is specified, the SCAN_ON_PUSH
	// and MANUAL scan frequencies are supported.
	//
	// ScanFrequency is a required field
	ScanFrequency *string `locationName:"scanFrequency" type:"string" required:"true" enum:"ScanFrequency"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegistryScanningRule) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegistryScanningRule) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *RegistryScanningRule) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "RegistryScanningRule"}
	if s.RepositoryFilters == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryFilters"))
	}
	if s.ScanFrequency == nil {
		invalidParams.Add(request.NewErrParamRequired("ScanFrequency"))
	}
	if s.RepositoryFilters != nil {
		for i, v := range s.RepositoryFilters {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "RepositoryFilters", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRepositoryFilters sets the RepositoryFilters field's value.
func (s *RegistryScanningRule) SetRepositoryFilters(v []*ScanningRepositoryFilter) *RegistryScanningRule {
	s.RepositoryFilters = v
	return s
}

// SetScanFrequency sets the ScanFrequency field's value.
func (s *RegistryScanningRule) SetScanFrequency(v string) *RegistryScanningRule {
	s.ScanFrequency = &v
	return s
}

// Information on how to remediate a finding.
type Remediation struct {
	_ struct{} `type:"structure"`

	// An object that contains information about the recommended course of action
	// to remediate the finding.
	Recommendation *Recommendation `locationName:"recommendation" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Remediation) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Remediation) GoString() string {
	return s.String()
}

// SetRecommendation sets the Recommendation field's value.
func (s *Remediation) SetRecommendation(v *Recommendation) *Remediation {
	s.Recommendation = v
	return s
}

// The replication configuration for a registry.
type ReplicationConfiguration struct {
	_ struct{} `type:"structure"`

	// An array of objects representing the replication destinations and repository
	// filters for a replication configuration.
	//
	// Rules is a required field
	Rules []*ReplicationRule `locationName:"rules" type:"list" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ReplicationConfiguration) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ReplicationConfiguration) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ReplicationConfiguration) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ReplicationConfiguration"}
	if s.Rules == nil {
		invalidParams.Add(request.NewErrParamRequired("Rules"))
	}
	if s.Rules != nil {
		for i, v := range s.Rules {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Rules", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRules sets the Rules field's value.
func (s *ReplicationConfiguration) SetRules(v []*ReplicationRule) *ReplicationConfiguration {
	s.Rules = v
	return s
}

// An array of objects representing the destination for a replication rule.
type ReplicationDestination struct {
	_ struct{} `type:"structure"`

	// The Region to replicate to.
	//
	// Region is a required field
	Region *string `locationName:"region" min:"2" type:"string" required:"true"`

	// The Amazon Web Services account ID of the Amazon ECR private registry to
	// replicate to. When configuring cross-Region replication within your own registry,
	// specify your own account ID.
	//
	// RegistryId is a required field
	RegistryId *string `locationName:"registryId" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ReplicationDestination) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ReplicationDestination) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ReplicationDestination) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ReplicationDestination"}
	if s.Region == nil {
		invalidParams.Add(request.NewErrParamRequired("Region"))
	}
	if s.Region != nil && len(*s.Region) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("Region", 2))
	}
	if s.RegistryId == nil {
		invalidParams.Add(request.NewErrParamRequired("RegistryId"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetRegion sets the Region field's value.
func (s *ReplicationDestination) SetRegion(v string) *ReplicationDestination {
	s.Region = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *ReplicationDestination) SetRegistryId(v string) *ReplicationDestination {
	s.RegistryId = &v
	return s
}

// An array of objects representing the replication destinations and repository
// filters for a replication configuration.
type ReplicationRule struct {
	_ struct{} `type:"structure"`

	// An array of objects representing the destination for a replication rule.
	//
	// Destinations is a required field
	Destinations []*ReplicationDestination `locationName:"destinations" type:"list" required:"true"`

	// An array of objects representing the filters for a replication rule. Specifying
	// a repository filter for a replication rule provides a method for controlling
	// which repositories in a private registry are replicated.
	RepositoryFilters []*RepositoryFilter `locationName:"repositoryFilters" min:"1" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ReplicationRule) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ReplicationRule) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ReplicationRule) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ReplicationRule"}
	if s.Destinations == nil {
		invalidParams.Add(request.NewErrParamRequired("Destinations"))
	}
	if s.RepositoryFilters != nil && len(s.RepositoryFilters) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryFilters", 1))
	}
	if s.Destinations != nil {
		for i, v := range s.Destinations {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "Destinations", i), err.(request.ErrInvalidParams))
			}
		}
	}
	if s.RepositoryFilters != nil {
		for i, v := range s.RepositoryFilters {
			if v == nil {
				continue
			}
			if err := v.Validate(); err != nil {
				invalidParams.AddNested(fmt.Sprintf("%s[%v]", "RepositoryFilters", i), err.(request.ErrInvalidParams))
			}
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetDestinations sets the Destinations field's value.
func (s *ReplicationRule) SetDestinations(v []*ReplicationDestination) *ReplicationRule {
	s.Destinations = v
	return s
}

// SetRepositoryFilters sets the RepositoryFilters field's value.
func (s *ReplicationRule) SetRepositoryFilters(v []*RepositoryFilter) *ReplicationRule {
	s.RepositoryFilters = v
	return s
}

// An object representing a repository.
type Repository struct {
	_ struct{} `type:"structure"`

	// The date and time, in JavaScript date format, when the repository was created.
	CreatedAt *time.Time `locationName:"createdAt" type:"timestamp"`

	// The encryption configuration for the repository. This determines how the
	// contents of your repository are encrypted at rest.
	EncryptionConfiguration *EncryptionConfiguration `locationName:"encryptionConfiguration" type:"structure"`

	// The image scanning configuration for a repository.
	ImageScanningConfiguration *ImageScanningConfiguration `locationName:"imageScanningConfiguration" type:"structure"`

	// The tag mutability setting for the repository.
	ImageTagMutability *string `locationName:"imageTagMutability" type:"string" enum:"ImageTagMutability"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The Amazon Resource Name (ARN) that identifies the repository. The ARN contains
	// the arn:aws:ecr namespace, followed by the region of the repository, Amazon
	// Web Services account ID of the repository owner, repository namespace, and
	// repository name. For example, arn:aws:ecr:region:************:repository/test.
	RepositoryArn *string `locationName:"repositoryArn" type:"string"`

	// The name of the repository.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`

	// The URI for the repository. You can use this URI for container image push
	// and pull operations.
	RepositoryUri *string `locationName:"repositoryUri" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Repository) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Repository) GoString() string {
	return s.String()
}

// SetCreatedAt sets the CreatedAt field's value.
func (s *Repository) SetCreatedAt(v time.Time) *Repository {
	s.CreatedAt = &v
	return s
}

// SetEncryptionConfiguration sets the EncryptionConfiguration field's value.
func (s *Repository) SetEncryptionConfiguration(v *EncryptionConfiguration) *Repository {
	s.EncryptionConfiguration = v
	return s
}

// SetImageScanningConfiguration sets the ImageScanningConfiguration field's value.
func (s *Repository) SetImageScanningConfiguration(v *ImageScanningConfiguration) *Repository {
	s.ImageScanningConfiguration = v
	return s
}

// SetImageTagMutability sets the ImageTagMutability field's value.
func (s *Repository) SetImageTagMutability(v string) *Repository {
	s.ImageTagMutability = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *Repository) SetRegistryId(v string) *Repository {
	s.RegistryId = &v
	return s
}

// SetRepositoryArn sets the RepositoryArn field's value.
func (s *Repository) SetRepositoryArn(v string) *Repository {
	s.RepositoryArn = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *Repository) SetRepositoryName(v string) *Repository {
	s.RepositoryName = &v
	return s
}

// SetRepositoryUri sets the RepositoryUri field's value.
func (s *Repository) SetRepositoryUri(v string) *Repository {
	s.RepositoryUri = &v
	return s
}

// The specified repository already exists in the specified registry.
type RepositoryAlreadyExistsException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryAlreadyExistsException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryAlreadyExistsException) GoString() string {
	return s.String()
}

func newErrorRepositoryAlreadyExistsException(v protocol.ResponseMetadata) error {
	return &RepositoryAlreadyExistsException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *RepositoryAlreadyExistsException) Code() string {
	return "RepositoryAlreadyExistsException"
}

// Message returns the exception's message.
func (s *RepositoryAlreadyExistsException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *RepositoryAlreadyExistsException) OrigErr() error {
	return nil
}

func (s *RepositoryAlreadyExistsException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *RepositoryAlreadyExistsException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *RepositoryAlreadyExistsException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The filter settings used with image replication. Specifying a repository
// filter to a replication rule provides a method for controlling which repositories
// in a private registry are replicated. If no repository filter is specified,
// all images in the repository are replicated.
type RepositoryFilter struct {
	_ struct{} `type:"structure"`

	// The repository filter details. When the PREFIX_MATCH filter type is specified,
	// this value is required and should be the repository name prefix to configure
	// replication for.
	//
	// Filter is a required field
	Filter *string `locationName:"filter" min:"2" type:"string" required:"true"`

	// The repository filter type. The only supported value is PREFIX_MATCH, which
	// is a repository name prefix specified with the filter parameter.
	//
	// FilterType is a required field
	FilterType *string `locationName:"filterType" type:"string" required:"true" enum:"RepositoryFilterType"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryFilter) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryFilter) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *RepositoryFilter) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "RepositoryFilter"}
	if s.Filter == nil {
		invalidParams.Add(request.NewErrParamRequired("Filter"))
	}
	if s.Filter != nil && len(*s.Filter) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("Filter", 2))
	}
	if s.FilterType == nil {
		invalidParams.Add(request.NewErrParamRequired("FilterType"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetFilter sets the Filter field's value.
func (s *RepositoryFilter) SetFilter(v string) *RepositoryFilter {
	s.Filter = &v
	return s
}

// SetFilterType sets the FilterType field's value.
func (s *RepositoryFilter) SetFilterType(v string) *RepositoryFilter {
	s.FilterType = &v
	return s
}

// The specified repository contains images. To delete a repository that contains
// images, you must force the deletion with the force parameter.
type RepositoryNotEmptyException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryNotEmptyException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryNotEmptyException) GoString() string {
	return s.String()
}

func newErrorRepositoryNotEmptyException(v protocol.ResponseMetadata) error {
	return &RepositoryNotEmptyException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *RepositoryNotEmptyException) Code() string {
	return "RepositoryNotEmptyException"
}

// Message returns the exception's message.
func (s *RepositoryNotEmptyException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *RepositoryNotEmptyException) OrigErr() error {
	return nil
}

func (s *RepositoryNotEmptyException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *RepositoryNotEmptyException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *RepositoryNotEmptyException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The specified repository could not be found. Check the spelling of the specified
// repository and ensure that you are performing operations on the correct registry.
type RepositoryNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryNotFoundException) GoString() string {
	return s.String()
}

func newErrorRepositoryNotFoundException(v protocol.ResponseMetadata) error {
	return &RepositoryNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *RepositoryNotFoundException) Code() string {
	return "RepositoryNotFoundException"
}

// Message returns the exception's message.
func (s *RepositoryNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *RepositoryNotFoundException) OrigErr() error {
	return nil
}

func (s *RepositoryNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *RepositoryNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *RepositoryNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The specified repository and registry combination does not have an associated
// repository policy.
type RepositoryPolicyNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryPolicyNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryPolicyNotFoundException) GoString() string {
	return s.String()
}

func newErrorRepositoryPolicyNotFoundException(v protocol.ResponseMetadata) error {
	return &RepositoryPolicyNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *RepositoryPolicyNotFoundException) Code() string {
	return "RepositoryPolicyNotFoundException"
}

// Message returns the exception's message.
func (s *RepositoryPolicyNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *RepositoryPolicyNotFoundException) OrigErr() error {
	return nil
}

func (s *RepositoryPolicyNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *RepositoryPolicyNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *RepositoryPolicyNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The details of the scanning configuration for a repository.
type RepositoryScanningConfiguration struct {
	_ struct{} `type:"structure"`

	// The scan filters applied to the repository.
	AppliedScanFilters []*ScanningRepositoryFilter `locationName:"appliedScanFilters" type:"list"`

	// The ARN of the repository.
	RepositoryArn *string `locationName:"repositoryArn" type:"string"`

	// The name of the repository.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`

	// The scan frequency for the repository.
	ScanFrequency *string `locationName:"scanFrequency" type:"string" enum:"ScanFrequency"`

	// Whether or not scan on push is configured for the repository.
	ScanOnPush *bool `locationName:"scanOnPush" type:"boolean"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryScanningConfiguration) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryScanningConfiguration) GoString() string {
	return s.String()
}

// SetAppliedScanFilters sets the AppliedScanFilters field's value.
func (s *RepositoryScanningConfiguration) SetAppliedScanFilters(v []*ScanningRepositoryFilter) *RepositoryScanningConfiguration {
	s.AppliedScanFilters = v
	return s
}

// SetRepositoryArn sets the RepositoryArn field's value.
func (s *RepositoryScanningConfiguration) SetRepositoryArn(v string) *RepositoryScanningConfiguration {
	s.RepositoryArn = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *RepositoryScanningConfiguration) SetRepositoryName(v string) *RepositoryScanningConfiguration {
	s.RepositoryName = &v
	return s
}

// SetScanFrequency sets the ScanFrequency field's value.
func (s *RepositoryScanningConfiguration) SetScanFrequency(v string) *RepositoryScanningConfiguration {
	s.ScanFrequency = &v
	return s
}

// SetScanOnPush sets the ScanOnPush field's value.
func (s *RepositoryScanningConfiguration) SetScanOnPush(v bool) *RepositoryScanningConfiguration {
	s.ScanOnPush = &v
	return s
}

// The details about any failures associated with the scanning configuration
// of a repository.
type RepositoryScanningConfigurationFailure struct {
	_ struct{} `type:"structure"`

	// The failure code.
	FailureCode *string `locationName:"failureCode" type:"string" enum:"ScanningConfigurationFailureCode"`

	// The reason for the failure.
	FailureReason *string `locationName:"failureReason" type:"string"`

	// The name of the repository.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryScanningConfigurationFailure) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RepositoryScanningConfigurationFailure) GoString() string {
	return s.String()
}

// SetFailureCode sets the FailureCode field's value.
func (s *RepositoryScanningConfigurationFailure) SetFailureCode(v string) *RepositoryScanningConfigurationFailure {
	s.FailureCode = &v
	return s
}

// SetFailureReason sets the FailureReason field's value.
func (s *RepositoryScanningConfigurationFailure) SetFailureReason(v string) *RepositoryScanningConfigurationFailure {
	s.FailureReason = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *RepositoryScanningConfigurationFailure) SetRepositoryName(v string) *RepositoryScanningConfigurationFailure {
	s.RepositoryName = &v
	return s
}

// Details about the resource involved in a finding.
type Resource struct {
	_ struct{} `type:"structure"`

	// An object that contains details about the resource involved in a finding.
	Details *ResourceDetails `locationName:"details" type:"structure"`

	// The ID of the resource.
	Id *string `locationName:"id" type:"string"`

	// The tags attached to the resource.
	Tags map[string]*string `locationName:"tags" type:"map"`

	// The type of resource.
	Type *string `locationName:"type" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Resource) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Resource) GoString() string {
	return s.String()
}

// SetDetails sets the Details field's value.
func (s *Resource) SetDetails(v *ResourceDetails) *Resource {
	s.Details = v
	return s
}

// SetId sets the Id field's value.
func (s *Resource) SetId(v string) *Resource {
	s.Id = &v
	return s
}

// SetTags sets the Tags field's value.
func (s *Resource) SetTags(v map[string]*string) *Resource {
	s.Tags = v
	return s
}

// SetType sets the Type field's value.
func (s *Resource) SetType(v string) *Resource {
	s.Type = &v
	return s
}

// Contains details about the resource involved in the finding.
type ResourceDetails struct {
	_ struct{} `type:"structure"`

	// An object that contains details about the Amazon ECR container image involved
	// in the finding.
	AwsEcrContainerImage *AwsEcrContainerImageDetails `locationName:"awsEcrContainerImage" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ResourceDetails) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ResourceDetails) GoString() string {
	return s.String()
}

// SetAwsEcrContainerImage sets the AwsEcrContainerImage field's value.
func (s *ResourceDetails) SetAwsEcrContainerImage(v *AwsEcrContainerImageDetails) *ResourceDetails {
	s.AwsEcrContainerImage = v
	return s
}

// The specified image scan could not be found. Ensure that image scanning is
// enabled on the repository and try again.
type ScanNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ScanNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ScanNotFoundException) GoString() string {
	return s.String()
}

func newErrorScanNotFoundException(v protocol.ResponseMetadata) error {
	return &ScanNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ScanNotFoundException) Code() string {
	return "ScanNotFoundException"
}

// Message returns the exception's message.
func (s *ScanNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ScanNotFoundException) OrigErr() error {
	return nil
}

func (s *ScanNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ScanNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ScanNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The details of a scanning repository filter. For more information on how
// to use filters, see Using filters (https://docs.aws.amazon.com/AmazonECR/latest/userguide/image-scanning.html#image-scanning-filters)
// in the Amazon Elastic Container Registry User Guide.
type ScanningRepositoryFilter struct {
	_ struct{} `type:"structure"`

	// The filter to use when scanning.
	//
	// Filter is a required field
	Filter *string `locationName:"filter" min:"1" type:"string" required:"true"`

	// The type associated with the filter.
	//
	// FilterType is a required field
	FilterType *string `locationName:"filterType" type:"string" required:"true" enum:"ScanningRepositoryFilterType"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ScanningRepositoryFilter) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ScanningRepositoryFilter) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *ScanningRepositoryFilter) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "ScanningRepositoryFilter"}
	if s.Filter == nil {
		invalidParams.Add(request.NewErrParamRequired("Filter"))
	}
	if s.Filter != nil && len(*s.Filter) < 1 {
		invalidParams.Add(request.NewErrParamMinLen("Filter", 1))
	}
	if s.FilterType == nil {
		invalidParams.Add(request.NewErrParamRequired("FilterType"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetFilter sets the Filter field's value.
func (s *ScanningRepositoryFilter) SetFilter(v string) *ScanningRepositoryFilter {
	s.Filter = &v
	return s
}

// SetFilterType sets the FilterType field's value.
func (s *ScanningRepositoryFilter) SetFilterType(v string) *ScanningRepositoryFilter {
	s.FilterType = &v
	return s
}

// Information about the Amazon Inspector score given to a finding.
type ScoreDetails struct {
	_ struct{} `type:"structure"`

	// An object that contains details about the CVSS score given to a finding.
	Cvss *CvssScoreDetails `locationName:"cvss" type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ScoreDetails) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ScoreDetails) GoString() string {
	return s.String()
}

// SetCvss sets the Cvss field's value.
func (s *ScoreDetails) SetCvss(v *CvssScoreDetails) *ScoreDetails {
	s.Cvss = v
	return s
}

// These errors are usually caused by a server-side issue.
type ServerException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ServerException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ServerException) GoString() string {
	return s.String()
}

func newErrorServerException(v protocol.ResponseMetadata) error {
	return &ServerException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ServerException) Code() string {
	return "ServerException"
}

// Message returns the exception's message.
func (s *ServerException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ServerException) OrigErr() error {
	return nil
}

func (s *ServerException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ServerException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ServerException) RequestID() string {
	return s.RespMetadata.RequestID
}

type SetRepositoryPolicyInput struct {
	_ struct{} `type:"structure"`

	// If the policy you are attempting to set on a repository policy would prevent
	// you from setting another policy in the future, you must force the SetRepositoryPolicy
	// operation. This is intended to prevent accidental repository lock outs.
	Force *bool `locationName:"force" type:"boolean"`

	// The JSON repository policy text to apply to the repository. For more information,
	// see Amazon ECR repository policies (https://docs.aws.amazon.com/AmazonECR/latest/userguide/repository-policy-examples.html)
	// in the Amazon Elastic Container Registry User Guide.
	//
	// PolicyText is a required field
	PolicyText *string `locationName:"policyText" type:"string" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository. If you do not specify a registry, the default registry is
	// assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository to receive the policy.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s SetRepositoryPolicyInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s SetRepositoryPolicyInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *SetRepositoryPolicyInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "SetRepositoryPolicyInput"}
	if s.PolicyText == nil {
		invalidParams.Add(request.NewErrParamRequired("PolicyText"))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetForce sets the Force field's value.
func (s *SetRepositoryPolicyInput) SetForce(v bool) *SetRepositoryPolicyInput {
	s.Force = &v
	return s
}

// SetPolicyText sets the PolicyText field's value.
func (s *SetRepositoryPolicyInput) SetPolicyText(v string) *SetRepositoryPolicyInput {
	s.PolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *SetRepositoryPolicyInput) SetRegistryId(v string) *SetRepositoryPolicyInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *SetRepositoryPolicyInput) SetRepositoryName(v string) *SetRepositoryPolicyInput {
	s.RepositoryName = &v
	return s
}

type SetRepositoryPolicyOutput struct {
	_ struct{} `type:"structure"`

	// The JSON repository policy text applied to the repository.
	PolicyText *string `locationName:"policyText" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s SetRepositoryPolicyOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s SetRepositoryPolicyOutput) GoString() string {
	return s.String()
}

// SetPolicyText sets the PolicyText field's value.
func (s *SetRepositoryPolicyOutput) SetPolicyText(v string) *SetRepositoryPolicyOutput {
	s.PolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *SetRepositoryPolicyOutput) SetRegistryId(v string) *SetRepositoryPolicyOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *SetRepositoryPolicyOutput) SetRepositoryName(v string) *SetRepositoryPolicyOutput {
	s.RepositoryName = &v
	return s
}

type StartImageScanInput struct {
	_ struct{} `type:"structure"`

	// An object with identifying information for an image in an Amazon ECR repository.
	//
	// ImageId is a required field
	ImageId *ImageIdentifier `locationName:"imageId" type:"structure" required:"true"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository in which to start an image scan request. If you do not specify
	// a registry, the default registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository that contains the images to scan.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartImageScanInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartImageScanInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *StartImageScanInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "StartImageScanInput"}
	if s.ImageId == nil {
		invalidParams.Add(request.NewErrParamRequired("ImageId"))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.ImageId != nil {
		if err := s.ImageId.Validate(); err != nil {
			invalidParams.AddNested("ImageId", err.(request.ErrInvalidParams))
		}
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetImageId sets the ImageId field's value.
func (s *StartImageScanInput) SetImageId(v *ImageIdentifier) *StartImageScanInput {
	s.ImageId = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *StartImageScanInput) SetRegistryId(v string) *StartImageScanInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *StartImageScanInput) SetRepositoryName(v string) *StartImageScanInput {
	s.RepositoryName = &v
	return s
}

type StartImageScanOutput struct {
	_ struct{} `type:"structure"`

	// An object with identifying information for an image in an Amazon ECR repository.
	ImageId *ImageIdentifier `locationName:"imageId" type:"structure"`

	// The current state of the scan.
	ImageScanStatus *ImageScanStatus `locationName:"imageScanStatus" type:"structure"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartImageScanOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartImageScanOutput) GoString() string {
	return s.String()
}

// SetImageId sets the ImageId field's value.
func (s *StartImageScanOutput) SetImageId(v *ImageIdentifier) *StartImageScanOutput {
	s.ImageId = v
	return s
}

// SetImageScanStatus sets the ImageScanStatus field's value.
func (s *StartImageScanOutput) SetImageScanStatus(v *ImageScanStatus) *StartImageScanOutput {
	s.ImageScanStatus = v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *StartImageScanOutput) SetRegistryId(v string) *StartImageScanOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *StartImageScanOutput) SetRepositoryName(v string) *StartImageScanOutput {
	s.RepositoryName = &v
	return s
}

type StartLifecyclePolicyPreviewInput struct {
	_ struct{} `type:"structure"`

	// The policy to be evaluated against. If you do not specify a policy, the current
	// policy for the repository is used.
	LifecyclePolicyText *string `locationName:"lifecyclePolicyText" min:"100" type:"string"`

	// The Amazon Web Services account ID associated with the registry that contains
	// the repository. If you do not specify a registry, the default registry is
	// assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository to be evaluated.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartLifecyclePolicyPreviewInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartLifecyclePolicyPreviewInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *StartLifecyclePolicyPreviewInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "StartLifecyclePolicyPreviewInput"}
	if s.LifecyclePolicyText != nil && len(*s.LifecyclePolicyText) < 100 {
		invalidParams.Add(request.NewErrParamMinLen("LifecyclePolicyText", 100))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetLifecyclePolicyText sets the LifecyclePolicyText field's value.
func (s *StartLifecyclePolicyPreviewInput) SetLifecyclePolicyText(v string) *StartLifecyclePolicyPreviewInput {
	s.LifecyclePolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *StartLifecyclePolicyPreviewInput) SetRegistryId(v string) *StartLifecyclePolicyPreviewInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *StartLifecyclePolicyPreviewInput) SetRepositoryName(v string) *StartLifecyclePolicyPreviewInput {
	s.RepositoryName = &v
	return s
}

type StartLifecyclePolicyPreviewOutput struct {
	_ struct{} `type:"structure"`

	// The JSON repository policy text.
	LifecyclePolicyText *string `locationName:"lifecyclePolicyText" min:"100" type:"string"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`

	// The status of the lifecycle policy preview request.
	Status *string `locationName:"status" type:"string" enum:"LifecyclePolicyPreviewStatus"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartLifecyclePolicyPreviewOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartLifecyclePolicyPreviewOutput) GoString() string {
	return s.String()
}

// SetLifecyclePolicyText sets the LifecyclePolicyText field's value.
func (s *StartLifecyclePolicyPreviewOutput) SetLifecyclePolicyText(v string) *StartLifecyclePolicyPreviewOutput {
	s.LifecyclePolicyText = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *StartLifecyclePolicyPreviewOutput) SetRegistryId(v string) *StartLifecyclePolicyPreviewOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *StartLifecyclePolicyPreviewOutput) SetRepositoryName(v string) *StartLifecyclePolicyPreviewOutput {
	s.RepositoryName = &v
	return s
}

// SetStatus sets the Status field's value.
func (s *StartLifecyclePolicyPreviewOutput) SetStatus(v string) *StartLifecyclePolicyPreviewOutput {
	s.Status = &v
	return s
}

// The metadata to apply to a resource to help you categorize and organize them.
// Each tag consists of a key and a value, both of which you define. Tag keys
// can have a maximum character length of 128 characters, and tag values can
// have a maximum length of 256 characters.
type Tag struct {
	_ struct{} `type:"structure"`

	// One part of a key-value pair that make up a tag. A key is a general label
	// that acts like a category for more specific tag values.
	Key *string `type:"string"`

	// A value acts as a descriptor within a tag category (key).
	Value *string `type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Tag) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s Tag) GoString() string {
	return s.String()
}

// SetKey sets the Key field's value.
func (s *Tag) SetKey(v string) *Tag {
	s.Key = &v
	return s
}

// SetValue sets the Value field's value.
func (s *Tag) SetValue(v string) *Tag {
	s.Value = &v
	return s
}

type TagResourceInput struct {
	_ struct{} `type:"structure"`

	// The Amazon Resource Name (ARN) of the the resource to which to add tags.
	// Currently, the only supported resource is an Amazon ECR repository.
	//
	// ResourceArn is a required field
	ResourceArn *string `locationName:"resourceArn" type:"string" required:"true"`

	// The tags to add to the resource. A tag is an array of key-value pairs. Tag
	// keys can have a maximum character length of 128 characters, and tag values
	// can have a maximum length of 256 characters.
	//
	// Tags is a required field
	Tags []*Tag `locationName:"tags" type:"list" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s TagResourceInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s TagResourceInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *TagResourceInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "TagResourceInput"}
	if s.ResourceArn == nil {
		invalidParams.Add(request.NewErrParamRequired("ResourceArn"))
	}
	if s.Tags == nil {
		invalidParams.Add(request.NewErrParamRequired("Tags"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetResourceArn sets the ResourceArn field's value.
func (s *TagResourceInput) SetResourceArn(v string) *TagResourceInput {
	s.ResourceArn = &v
	return s
}

// SetTags sets the Tags field's value.
func (s *TagResourceInput) SetTags(v []*Tag) *TagResourceInput {
	s.Tags = v
	return s
}

type TagResourceOutput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s TagResourceOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s TagResourceOutput) GoString() string {
	return s.String()
}

// The list of tags on the repository is over the limit. The maximum number
// of tags that can be applied to a repository is 50.
type TooManyTagsException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s TooManyTagsException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s TooManyTagsException) GoString() string {
	return s.String()
}

func newErrorTooManyTagsException(v protocol.ResponseMetadata) error {
	return &TooManyTagsException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *TooManyTagsException) Code() string {
	return "TooManyTagsException"
}

// Message returns the exception's message.
func (s *TooManyTagsException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *TooManyTagsException) OrigErr() error {
	return nil
}

func (s *TooManyTagsException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *TooManyTagsException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *TooManyTagsException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The image is of a type that cannot be scanned.
type UnsupportedImageTypeException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UnsupportedImageTypeException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UnsupportedImageTypeException) GoString() string {
	return s.String()
}

func newErrorUnsupportedImageTypeException(v protocol.ResponseMetadata) error {
	return &UnsupportedImageTypeException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *UnsupportedImageTypeException) Code() string {
	return "UnsupportedImageTypeException"
}

// Message returns the exception's message.
func (s *UnsupportedImageTypeException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *UnsupportedImageTypeException) OrigErr() error {
	return nil
}

func (s *UnsupportedImageTypeException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *UnsupportedImageTypeException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *UnsupportedImageTypeException) RequestID() string {
	return s.RespMetadata.RequestID
}

// The specified upstream registry isn't supported.
type UnsupportedUpstreamRegistryException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UnsupportedUpstreamRegistryException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UnsupportedUpstreamRegistryException) GoString() string {
	return s.String()
}

func newErrorUnsupportedUpstreamRegistryException(v protocol.ResponseMetadata) error {
	return &UnsupportedUpstreamRegistryException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *UnsupportedUpstreamRegistryException) Code() string {
	return "UnsupportedUpstreamRegistryException"
}

// Message returns the exception's message.
func (s *UnsupportedUpstreamRegistryException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *UnsupportedUpstreamRegistryException) OrigErr() error {
	return nil
}

func (s *UnsupportedUpstreamRegistryException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *UnsupportedUpstreamRegistryException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *UnsupportedUpstreamRegistryException) RequestID() string {
	return s.RespMetadata.RequestID
}

type UntagResourceInput struct {
	_ struct{} `type:"structure"`

	// The Amazon Resource Name (ARN) of the resource from which to remove tags.
	// Currently, the only supported resource is an Amazon ECR repository.
	//
	// ResourceArn is a required field
	ResourceArn *string `locationName:"resourceArn" type:"string" required:"true"`

	// The keys of the tags to be removed.
	//
	// TagKeys is a required field
	TagKeys []*string `locationName:"tagKeys" type:"list" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UntagResourceInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UntagResourceInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *UntagResourceInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "UntagResourceInput"}
	if s.ResourceArn == nil {
		invalidParams.Add(request.NewErrParamRequired("ResourceArn"))
	}
	if s.TagKeys == nil {
		invalidParams.Add(request.NewErrParamRequired("TagKeys"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetResourceArn sets the ResourceArn field's value.
func (s *UntagResourceInput) SetResourceArn(v string) *UntagResourceInput {
	s.ResourceArn = &v
	return s
}

// SetTagKeys sets the TagKeys field's value.
func (s *UntagResourceInput) SetTagKeys(v []*string) *UntagResourceInput {
	s.TagKeys = v
	return s
}

type UntagResourceOutput struct {
	_ struct{} `type:"structure"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UntagResourceOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UntagResourceOutput) GoString() string {
	return s.String()
}

type UploadLayerPartInput struct {
	_ struct{} `type:"structure"`

	// The base64-encoded layer part payload.
	// LayerPartBlob is automatically base64 encoded/decoded by the SDK.
	//
	// LayerPartBlob is a required field
	LayerPartBlob []byte `locationName:"layerPartBlob" type:"blob" required:"true"`

	// The position of the first byte of the layer part witin the overall image
	// layer.
	//
	// PartFirstByte is a required field
	PartFirstByte *int64 `locationName:"partFirstByte" type:"long" required:"true"`

	// The position of the last byte of the layer part within the overall image
	// layer.
	//
	// PartLastByte is a required field
	PartLastByte *int64 `locationName:"partLastByte" type:"long" required:"true"`

	// The Amazon Web Services account ID associated with the registry to which
	// you are uploading layer parts. If you do not specify a registry, the default
	// registry is assumed.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The name of the repository to which you are uploading layer parts.
	//
	// RepositoryName is a required field
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string" required:"true"`

	// The upload ID from a previous InitiateLayerUpload operation to associate
	// with the layer part upload.
	//
	// UploadId is a required field
	UploadId *string `locationName:"uploadId" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UploadLayerPartInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UploadLayerPartInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *UploadLayerPartInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "UploadLayerPartInput"}
	if s.LayerPartBlob == nil {
		invalidParams.Add(request.NewErrParamRequired("LayerPartBlob"))
	}
	if s.PartFirstByte == nil {
		invalidParams.Add(request.NewErrParamRequired("PartFirstByte"))
	}
	if s.PartLastByte == nil {
		invalidParams.Add(request.NewErrParamRequired("PartLastByte"))
	}
	if s.RepositoryName == nil {
		invalidParams.Add(request.NewErrParamRequired("RepositoryName"))
	}
	if s.RepositoryName != nil && len(*s.RepositoryName) < 2 {
		invalidParams.Add(request.NewErrParamMinLen("RepositoryName", 2))
	}
	if s.UploadId == nil {
		invalidParams.Add(request.NewErrParamRequired("UploadId"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetLayerPartBlob sets the LayerPartBlob field's value.
func (s *UploadLayerPartInput) SetLayerPartBlob(v []byte) *UploadLayerPartInput {
	s.LayerPartBlob = v
	return s
}

// SetPartFirstByte sets the PartFirstByte field's value.
func (s *UploadLayerPartInput) SetPartFirstByte(v int64) *UploadLayerPartInput {
	s.PartFirstByte = &v
	return s
}

// SetPartLastByte sets the PartLastByte field's value.
func (s *UploadLayerPartInput) SetPartLastByte(v int64) *UploadLayerPartInput {
	s.PartLastByte = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *UploadLayerPartInput) SetRegistryId(v string) *UploadLayerPartInput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *UploadLayerPartInput) SetRepositoryName(v string) *UploadLayerPartInput {
	s.RepositoryName = &v
	return s
}

// SetUploadId sets the UploadId field's value.
func (s *UploadLayerPartInput) SetUploadId(v string) *UploadLayerPartInput {
	s.UploadId = &v
	return s
}

type UploadLayerPartOutput struct {
	_ struct{} `type:"structure"`

	// The integer value of the last byte received in the request.
	LastByteReceived *int64 `locationName:"lastByteReceived" type:"long"`

	// The registry ID associated with the request.
	RegistryId *string `locationName:"registryId" type:"string"`

	// The repository name associated with the request.
	RepositoryName *string `locationName:"repositoryName" min:"2" type:"string"`

	// The upload ID associated with the request.
	UploadId *string `locationName:"uploadId" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UploadLayerPartOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UploadLayerPartOutput) GoString() string {
	return s.String()
}

// SetLastByteReceived sets the LastByteReceived field's value.
func (s *UploadLayerPartOutput) SetLastByteReceived(v int64) *UploadLayerPartOutput {
	s.LastByteReceived = &v
	return s
}

// SetRegistryId sets the RegistryId field's value.
func (s *UploadLayerPartOutput) SetRegistryId(v string) *UploadLayerPartOutput {
	s.RegistryId = &v
	return s
}

// SetRepositoryName sets the RepositoryName field's value.
func (s *UploadLayerPartOutput) SetRepositoryName(v string) *UploadLayerPartOutput {
	s.RepositoryName = &v
	return s
}

// SetUploadId sets the UploadId field's value.
func (s *UploadLayerPartOutput) SetUploadId(v string) *UploadLayerPartOutput {
	s.UploadId = &v
	return s
}

// The upload could not be found, or the specified upload ID is not valid for
// this repository.
type UploadNotFoundException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// The error message associated with the exception.
	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UploadNotFoundException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UploadNotFoundException) GoString() string {
	return s.String()
}

func newErrorUploadNotFoundException(v protocol.ResponseMetadata) error {
	return &UploadNotFoundException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *UploadNotFoundException) Code() string {
	return "UploadNotFoundException"
}

// Message returns the exception's message.
func (s *UploadNotFoundException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *UploadNotFoundException) OrigErr() error {
	return nil
}

func (s *UploadNotFoundException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *UploadNotFoundException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *UploadNotFoundException) RequestID() string {
	return s.RespMetadata.RequestID
}

// There was an exception validating this request.
type ValidationException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ValidationException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ValidationException) GoString() string {
	return s.String()
}

func newErrorValidationException(v protocol.ResponseMetadata) error {
	return &ValidationException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ValidationException) Code() string {
	return "ValidationException"
}

// Message returns the exception's message.
func (s *ValidationException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ValidationException) OrigErr() error {
	return nil
}

func (s *ValidationException) Error() string {
	return fmt.Sprintf("%s: %s", s.Code(), s.Message())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ValidationException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ValidationException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Information on the vulnerable package identified by a finding.
type VulnerablePackage struct {
	_ struct{} `type:"structure"`

	// The architecture of the vulnerable package.
	Arch *string `locationName:"arch" type:"string"`

	// The epoch of the vulnerable package.
	Epoch *int64 `locationName:"epoch" type:"integer"`

	// The file path of the vulnerable package.
	FilePath *string `locationName:"filePath" type:"string"`

	// The name of the vulnerable package.
	Name *string `locationName:"name" type:"string"`

	// The package manager of the vulnerable package.
	PackageManager *string `locationName:"packageManager" type:"string"`

	// The release of the vulnerable package.
	Release *string `locationName:"release" type:"string"`

	// The source layer hash of the vulnerable package.
	SourceLayerHash *string `locationName:"sourceLayerHash" type:"string"`

	// The version of the vulnerable package.
	Version *string `locationName:"version" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s VulnerablePackage) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s VulnerablePackage) GoString() string {
	return s.String()
}

// SetArch sets the Arch field's value.
func (s *VulnerablePackage) SetArch(v string) *VulnerablePackage {
	s.Arch = &v
	return s
}

// SetEpoch sets the Epoch field's value.
func (s *VulnerablePackage) SetEpoch(v int64) *VulnerablePackage {
	s.Epoch = &v
	return s
}

// SetFilePath sets the FilePath field's value.
func (s *VulnerablePackage) SetFilePath(v string) *VulnerablePackage {
	s.FilePath = &v
	return s
}

// SetName sets the Name field's value.
func (s *VulnerablePackage) SetName(v string) *VulnerablePackage {
	s.Name = &v
	return s
}

// SetPackageManager sets the PackageManager field's value.
func (s *VulnerablePackage) SetPackageManager(v string) *VulnerablePackage {
	s.PackageManager = &v
	return s
}

// SetRelease sets the Release field's value.
func (s *VulnerablePackage) SetRelease(v string) *VulnerablePackage {
	s.Release = &v
	return s
}

// SetSourceLayerHash sets the SourceLayerHash field's value.
func (s *VulnerablePackage) SetSourceLayerHash(v string) *VulnerablePackage {
	s.SourceLayerHash = &v
	return s
}

// SetVersion sets the Version field's value.
func (s *VulnerablePackage) SetVersion(v string) *VulnerablePackage {
	s.Version = &v
	return s
}

const (
	// EncryptionTypeAes256 is a EncryptionType enum value
	EncryptionTypeAes256 = "AES256"

	// EncryptionTypeKms is a EncryptionType enum value
	EncryptionTypeKms = "KMS"
)

// EncryptionType_Values returns all elements of the EncryptionType enum
func EncryptionType_Values() []string {
	return []string{
		EncryptionTypeAes256,
		EncryptionTypeKms,
	}
}

const (
	// FindingSeverityInformational is a FindingSeverity enum value
	FindingSeverityInformational = "INFORMATIONAL"

	// FindingSeverityLow is a FindingSeverity enum value
	FindingSeverityLow = "LOW"

	// FindingSeverityMedium is a FindingSeverity enum value
	FindingSeverityMedium = "MEDIUM"

	// FindingSeverityHigh is a FindingSeverity enum value
	FindingSeverityHigh = "HIGH"

	// FindingSeverityCritical is a FindingSeverity enum value
	FindingSeverityCritical = "CRITICAL"

	// FindingSeverityUndefined is a FindingSeverity enum value
	FindingSeverityUndefined = "UNDEFINED"
)

// FindingSeverity_Values returns all elements of the FindingSeverity enum
func FindingSeverity_Values() []string {
	return []string{
		FindingSeverityInformational,
		FindingSeverityLow,
		FindingSeverityMedium,
		FindingSeverityHigh,
		FindingSeverityCritical,
		FindingSeverityUndefined,
	}
}

const (
	// ImageActionTypeExpire is a ImageActionType enum value
	ImageActionTypeExpire = "EXPIRE"
)

// ImageActionType_Values returns all elements of the ImageActionType enum
func ImageActionType_Values() []string {
	return []string{
		ImageActionTypeExpire,
	}
}

const (
	// ImageFailureCodeInvalidImageDigest is a ImageFailureCode enum value
	ImageFailureCodeInvalidImageDigest = "InvalidImageDigest"

	// ImageFailureCodeInvalidImageTag is a ImageFailureCode enum value
	ImageFailureCodeInvalidImageTag = "InvalidImageTag"

	// ImageFailureCodeImageTagDoesNotMatchDigest is a ImageFailureCode enum value
	ImageFailureCodeImageTagDoesNotMatchDigest = "ImageTagDoesNotMatchDigest"

	// ImageFailureCodeImageNotFound is a ImageFailureCode enum value
	ImageFailureCodeImageNotFound = "ImageNotFound"

	// ImageFailureCodeMissingDigestAndTag is a ImageFailureCode enum value
	ImageFailureCodeMissingDigestAndTag = "MissingDigestAndTag"

	// ImageFailureCodeImageReferencedByManifestList is a ImageFailureCode enum value
	ImageFailureCodeImageReferencedByManifestList = "ImageReferencedByManifestList"

	// ImageFailureCodeKmsError is a ImageFailureCode enum value
	ImageFailureCodeKmsError = "KmsError"
)

// ImageFailureCode_Values returns all elements of the ImageFailureCode enum
func ImageFailureCode_Values() []string {
	return []string{
		ImageFailureCodeInvalidImageDigest,
		ImageFailureCodeInvalidImageTag,
		ImageFailureCodeImageTagDoesNotMatchDigest,
		ImageFailureCodeImageNotFound,
		ImageFailureCodeMissingDigestAndTag,
		ImageFailureCodeImageReferencedByManifestList,
		ImageFailureCodeKmsError,
	}
}

const (
	// ImageTagMutabilityMutable is a ImageTagMutability enum value
	ImageTagMutabilityMutable = "MUTABLE"

	// ImageTagMutabilityImmutable is a ImageTagMutability enum value
	ImageTagMutabilityImmutable = "IMMUTABLE"
)

// ImageTagMutability_Values returns all elements of the ImageTagMutability enum
func ImageTagMutability_Values() []string {
	return []string{
		ImageTagMutabilityMutable,
		ImageTagMutabilityImmutable,
	}
}

const (
	// LayerAvailabilityAvailable is a LayerAvailability enum value
	LayerAvailabilityAvailable = "AVAILABLE"

	// LayerAvailabilityUnavailable is a LayerAvailability enum value
	LayerAvailabilityUnavailable = "UNAVAILABLE"
)

// LayerAvailability_Values returns all elements of the LayerAvailability enum
func LayerAvailability_Values() []string {
	return []string{
		LayerAvailabilityAvailable,
		LayerAvailabilityUnavailable,
	}
}

const (
	// LayerFailureCodeInvalidLayerDigest is a LayerFailureCode enum value
	LayerFailureCodeInvalidLayerDigest = "InvalidLayerDigest"

	// LayerFailureCodeMissingLayerDigest is a LayerFailureCode enum value
	LayerFailureCodeMissingLayerDigest = "MissingLayerDigest"
)

// LayerFailureCode_Values returns all elements of the LayerFailureCode enum
func LayerFailureCode_Values() []string {
	return []string{
		LayerFailureCodeInvalidLayerDigest,
		LayerFailureCodeMissingLayerDigest,
	}
}

const (
	// LifecyclePolicyPreviewStatusInProgress is a LifecyclePolicyPreviewStatus enum value
	LifecyclePolicyPreviewStatusInProgress = "IN_PROGRESS"

	// LifecyclePolicyPreviewStatusComplete is a LifecyclePolicyPreviewStatus enum value
	LifecyclePolicyPreviewStatusComplete = "COMPLETE"

	// LifecyclePolicyPreviewStatusExpired is a LifecyclePolicyPreviewStatus enum value
	LifecyclePolicyPreviewStatusExpired = "EXPIRED"

	// LifecyclePolicyPreviewStatusFailed is a LifecyclePolicyPreviewStatus enum value
	LifecyclePolicyPreviewStatusFailed = "FAILED"
)

// LifecyclePolicyPreviewStatus_Values returns all elements of the LifecyclePolicyPreviewStatus enum
func LifecyclePolicyPreviewStatus_Values() []string {
	return []string{
		LifecyclePolicyPreviewStatusInProgress,
		LifecyclePolicyPreviewStatusComplete,
		LifecyclePolicyPreviewStatusExpired,
		LifecyclePolicyPreviewStatusFailed,
	}
}

const (
	// ReplicationStatusInProgress is a ReplicationStatus enum value
	ReplicationStatusInProgress = "IN_PROGRESS"

	// ReplicationStatusComplete is a ReplicationStatus enum value
	ReplicationStatusComplete = "COMPLETE"

	// ReplicationStatusFailed is a ReplicationStatus enum value
	ReplicationStatusFailed = "FAILED"
)

// ReplicationStatus_Values returns all elements of the ReplicationStatus enum
func ReplicationStatus_Values() []string {
	return []string{
		ReplicationStatusInProgress,
		ReplicationStatusComplete,
		ReplicationStatusFailed,
	}
}

const (
	// RepositoryFilterTypePrefixMatch is a RepositoryFilterType enum value
	RepositoryFilterTypePrefixMatch = "PREFIX_MATCH"
)

// RepositoryFilterType_Values returns all elements of the RepositoryFilterType enum
func RepositoryFilterType_Values() []string {
	return []string{
		RepositoryFilterTypePrefixMatch,
	}
}

const (
	// ScanFrequencyScanOnPush is a ScanFrequency enum value
	ScanFrequencyScanOnPush = "SCAN_ON_PUSH"

	// ScanFrequencyContinuousScan is a ScanFrequency enum value
	ScanFrequencyContinuousScan = "CONTINUOUS_SCAN"

	// ScanFrequencyManual is a ScanFrequency enum value
	ScanFrequencyManual = "MANUAL"
)

// ScanFrequency_Values returns all elements of the ScanFrequency enum
func ScanFrequency_Values() []string {
	return []string{
		ScanFrequencyScanOnPush,
		ScanFrequencyContinuousScan,
		ScanFrequencyManual,
	}
}

const (
	// ScanStatusInProgress is a ScanStatus enum value
	ScanStatusInProgress = "IN_PROGRESS"

	// ScanStatusComplete is a ScanStatus enum value
	ScanStatusComplete = "COMPLETE"

	// ScanStatusFailed is a ScanStatus enum value
	ScanStatusFailed = "FAILED"

	// ScanStatusUnsupportedImage is a ScanStatus enum value
	ScanStatusUnsupportedImage = "UNSUPPORTED_IMAGE"

	// ScanStatusActive is a ScanStatus enum value
	ScanStatusActive = "ACTIVE"

	// ScanStatusPending is a ScanStatus enum value
	ScanStatusPending = "PENDING"

	// ScanStatusScanEligibilityExpired is a ScanStatus enum value
	ScanStatusScanEligibilityExpired = "SCAN_ELIGIBILITY_EXPIRED"

	// ScanStatusFindingsUnavailable is a ScanStatus enum value
	ScanStatusFindingsUnavailable = "FINDINGS_UNAVAILABLE"
)

// ScanStatus_Values returns all elements of the ScanStatus enum
func ScanStatus_Values() []string {
	return []string{
		ScanStatusInProgress,
		ScanStatusComplete,
		ScanStatusFailed,
		ScanStatusUnsupportedImage,
		ScanStatusActive,
		ScanStatusPending,
		ScanStatusScanEligibilityExpired,
		ScanStatusFindingsUnavailable,
	}
}

const (
	// ScanTypeBasic is a ScanType enum value
	ScanTypeBasic = "BASIC"

	// ScanTypeEnhanced is a ScanType enum value
	ScanTypeEnhanced = "ENHANCED"
)

// ScanType_Values returns all elements of the ScanType enum
func ScanType_Values() []string {
	return []string{
		ScanTypeBasic,
		ScanTypeEnhanced,
	}
}

const (
	// ScanningConfigurationFailureCodeRepositoryNotFound is a ScanningConfigurationFailureCode enum value
	ScanningConfigurationFailureCodeRepositoryNotFound = "REPOSITORY_NOT_FOUND"
)

// ScanningConfigurationFailureCode_Values returns all elements of the ScanningConfigurationFailureCode enum
func ScanningConfigurationFailureCode_Values() []string {
	return []string{
		ScanningConfigurationFailureCodeRepositoryNotFound,
	}
}

const (
	// ScanningRepositoryFilterTypeWildcard is a ScanningRepositoryFilterType enum value
	ScanningRepositoryFilterTypeWildcard = "WILDCARD"
)

// ScanningRepositoryFilterType_Values returns all elements of the ScanningRepositoryFilterType enum
func ScanningRepositoryFilterType_Values() []string {
	return []string{
		ScanningRepositoryFilterTypeWildcard,
	}
}

const (
	// TagStatusTagged is a TagStatus enum value
	TagStatusTagged = "TAGGED"

	// TagStatusUntagged is a TagStatus enum value
	TagStatusUntagged = "UNTAGGED"

	// TagStatusAny is a TagStatus enum value
	TagStatusAny = "ANY"
)

// TagStatus_Values returns all elements of the TagStatus enum
func TagStatus_Values() []string {
	return []string{
		TagStatusTagged,
		TagStatusUntagged,
		TagStatusAny,
	}
}
