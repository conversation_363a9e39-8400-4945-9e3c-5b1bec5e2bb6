# EditorConfig is awesome: http://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Indentiation
[*.{py,rst}]
indent_style = space
indent_size = 4
[{Makefile,*.go}]
indent_style = tab
indent_size = 4
[*.{ini,yml}]
indent_style = space
indent_size = 2
