// Copyright 2021 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.21.12
// source: internal/proto/common/common.proto

package common_go_proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The ciphersuites supported by S2A. The name determines the confidentiality,
// and authentication ciphers as well as the hash algorithm used for PRF in
// TLS 1.2 or HKDF in TLS 1.3. Thus, the components of the name are:
//   - AEAD -- for encryption and authentication, e.g., AES_128_GCM.
//   - Hash algorithm -- used in PRF or HKDF, e.g., SHA256.
type Ciphersuite int32

const (
	Ciphersuite_AES_128_GCM_SHA256       Ciphersuite = 0
	Ciphersuite_AES_256_GCM_SHA384       Ciphersuite = 1
	Ciphersuite_CHACHA20_POLY1305_SHA256 Ciphersuite = 2
)

// Enum value maps for Ciphersuite.
var (
	Ciphersuite_name = map[int32]string{
		0: "AES_128_GCM_SHA256",
		1: "AES_256_GCM_SHA384",
		2: "CHACHA20_POLY1305_SHA256",
	}
	Ciphersuite_value = map[string]int32{
		"AES_128_GCM_SHA256":       0,
		"AES_256_GCM_SHA384":       1,
		"CHACHA20_POLY1305_SHA256": 2,
	}
)

func (x Ciphersuite) Enum() *Ciphersuite {
	p := new(Ciphersuite)
	*p = x
	return p
}

func (x Ciphersuite) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Ciphersuite) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_proto_common_common_proto_enumTypes[0].Descriptor()
}

func (Ciphersuite) Type() protoreflect.EnumType {
	return &file_internal_proto_common_common_proto_enumTypes[0]
}

func (x Ciphersuite) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Ciphersuite.Descriptor instead.
func (Ciphersuite) EnumDescriptor() ([]byte, []int) {
	return file_internal_proto_common_common_proto_rawDescGZIP(), []int{0}
}

// The TLS versions supported by S2A's handshaker module.
type TLSVersion int32

const (
	TLSVersion_TLS1_2 TLSVersion = 0
	TLSVersion_TLS1_3 TLSVersion = 1
)

// Enum value maps for TLSVersion.
var (
	TLSVersion_name = map[int32]string{
		0: "TLS1_2",
		1: "TLS1_3",
	}
	TLSVersion_value = map[string]int32{
		"TLS1_2": 0,
		"TLS1_3": 1,
	}
)

func (x TLSVersion) Enum() *TLSVersion {
	p := new(TLSVersion)
	*p = x
	return p
}

func (x TLSVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TLSVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_proto_common_common_proto_enumTypes[1].Descriptor()
}

func (TLSVersion) Type() protoreflect.EnumType {
	return &file_internal_proto_common_common_proto_enumTypes[1]
}

func (x TLSVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TLSVersion.Descriptor instead.
func (TLSVersion) EnumDescriptor() ([]byte, []int) {
	return file_internal_proto_common_common_proto_rawDescGZIP(), []int{1}
}

type Identity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to IdentityOneof:
	//
	//	*Identity_SpiffeId
	//	*Identity_Hostname
	//	*Identity_Uid
	//	*Identity_MdbUsername
	//	*Identity_GaiaId
	IdentityOneof isIdentity_IdentityOneof `protobuf_oneof:"identity_oneof"`
	// Additional identity-specific attributes.
	Attributes map[string]string `protobuf:"bytes,3,rep,name=attributes,proto3" json:"attributes,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *Identity) Reset() {
	*x = Identity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_common_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Identity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identity) ProtoMessage() {}

func (x *Identity) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_common_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identity.ProtoReflect.Descriptor instead.
func (*Identity) Descriptor() ([]byte, []int) {
	return file_internal_proto_common_common_proto_rawDescGZIP(), []int{0}
}

func (m *Identity) GetIdentityOneof() isIdentity_IdentityOneof {
	if m != nil {
		return m.IdentityOneof
	}
	return nil
}

func (x *Identity) GetSpiffeId() string {
	if x, ok := x.GetIdentityOneof().(*Identity_SpiffeId); ok {
		return x.SpiffeId
	}
	return ""
}

func (x *Identity) GetHostname() string {
	if x, ok := x.GetIdentityOneof().(*Identity_Hostname); ok {
		return x.Hostname
	}
	return ""
}

func (x *Identity) GetUid() string {
	if x, ok := x.GetIdentityOneof().(*Identity_Uid); ok {
		return x.Uid
	}
	return ""
}

func (x *Identity) GetMdbUsername() string {
	if x, ok := x.GetIdentityOneof().(*Identity_MdbUsername); ok {
		return x.MdbUsername
	}
	return ""
}

func (x *Identity) GetGaiaId() string {
	if x, ok := x.GetIdentityOneof().(*Identity_GaiaId); ok {
		return x.GaiaId
	}
	return ""
}

func (x *Identity) GetAttributes() map[string]string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

type isIdentity_IdentityOneof interface {
	isIdentity_IdentityOneof()
}

type Identity_SpiffeId struct {
	// The SPIFFE ID of a connection endpoint.
	SpiffeId string `protobuf:"bytes,1,opt,name=spiffe_id,json=spiffeId,proto3,oneof"`
}

type Identity_Hostname struct {
	// The hostname of a connection endpoint.
	Hostname string `protobuf:"bytes,2,opt,name=hostname,proto3,oneof"`
}

type Identity_Uid struct {
	// The UID of a connection endpoint.
	Uid string `protobuf:"bytes,4,opt,name=uid,proto3,oneof"`
}

type Identity_MdbUsername struct {
	// The MDB username of a connection endpoint.
	MdbUsername string `protobuf:"bytes,5,opt,name=mdb_username,json=mdbUsername,proto3,oneof"`
}

type Identity_GaiaId struct {
	// The Gaia ID of a connection endpoint.
	GaiaId string `protobuf:"bytes,6,opt,name=gaia_id,json=gaiaId,proto3,oneof"`
}

func (*Identity_SpiffeId) isIdentity_IdentityOneof() {}

func (*Identity_Hostname) isIdentity_IdentityOneof() {}

func (*Identity_Uid) isIdentity_IdentityOneof() {}

func (*Identity_MdbUsername) isIdentity_IdentityOneof() {}

func (*Identity_GaiaId) isIdentity_IdentityOneof() {}

var File_internal_proto_common_common_proto protoreflect.FileDescriptor

var file_internal_proto_common_common_proto_rawDesc = []byte{
	0x0a, 0x22, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x73, 0x32, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xb1, 0x02, 0x0a, 0x08, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x09,
	0x73, 0x70, 0x69, 0x66, 0x66, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x08, 0x73, 0x70, 0x69, 0x66, 0x66, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x08, 0x68,
	0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0c, 0x6d, 0x64, 0x62, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x64, 0x62, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x07, 0x67, 0x61, 0x69, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x06, 0x67, 0x61, 0x69, 0x61, 0x49, 0x64, 0x12, 0x43, 0x0a,
	0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x73, 0x32, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x42, 0x10, 0x0a, 0x0e, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6f, 0x6e,
	0x65, 0x6f, 0x66, 0x2a, 0x5b, 0x0a, 0x0b, 0x43, 0x69, 0x70, 0x68, 0x65, 0x72, 0x73, 0x75, 0x69,
	0x74, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x45, 0x53, 0x5f, 0x31, 0x32, 0x38, 0x5f, 0x47, 0x43,
	0x4d, 0x5f, 0x53, 0x48, 0x41, 0x32, 0x35, 0x36, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x45,
	0x53, 0x5f, 0x32, 0x35, 0x36, 0x5f, 0x47, 0x43, 0x4d, 0x5f, 0x53, 0x48, 0x41, 0x33, 0x38, 0x34,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x48, 0x41, 0x43, 0x48, 0x41, 0x32, 0x30, 0x5f, 0x50,
	0x4f, 0x4c, 0x59, 0x31, 0x33, 0x30, 0x35, 0x5f, 0x53, 0x48, 0x41, 0x32, 0x35, 0x36, 0x10, 0x02,
	0x2a, 0x24, 0x0a, 0x0a, 0x54, 0x4c, 0x53, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0a,
	0x0a, 0x06, 0x54, 0x4c, 0x53, 0x31, 0x5f, 0x32, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x4c,
	0x53, 0x31, 0x5f, 0x33, 0x10, 0x01, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x73, 0x32, 0x61, 0x2f,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5f, 0x67, 0x6f, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_internal_proto_common_common_proto_rawDescOnce sync.Once
	file_internal_proto_common_common_proto_rawDescData = file_internal_proto_common_common_proto_rawDesc
)

func file_internal_proto_common_common_proto_rawDescGZIP() []byte {
	file_internal_proto_common_common_proto_rawDescOnce.Do(func() {
		file_internal_proto_common_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_proto_common_common_proto_rawDescData)
	})
	return file_internal_proto_common_common_proto_rawDescData
}

var file_internal_proto_common_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_internal_proto_common_common_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_internal_proto_common_common_proto_goTypes = []interface{}{
	(Ciphersuite)(0), // 0: s2a.proto.Ciphersuite
	(TLSVersion)(0),  // 1: s2a.proto.TLSVersion
	(*Identity)(nil), // 2: s2a.proto.Identity
	nil,              // 3: s2a.proto.Identity.AttributesEntry
}
var file_internal_proto_common_common_proto_depIdxs = []int32{
	3, // 0: s2a.proto.Identity.attributes:type_name -> s2a.proto.Identity.AttributesEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_internal_proto_common_common_proto_init() }
func file_internal_proto_common_common_proto_init() {
	if File_internal_proto_common_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_proto_common_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Identity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_internal_proto_common_common_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*Identity_SpiffeId)(nil),
		(*Identity_Hostname)(nil),
		(*Identity_Uid)(nil),
		(*Identity_MdbUsername)(nil),
		(*Identity_GaiaId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_proto_common_common_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_proto_common_common_proto_goTypes,
		DependencyIndexes: file_internal_proto_common_common_proto_depIdxs,
		EnumInfos:         file_internal_proto_common_common_proto_enumTypes,
		MessageInfos:      file_internal_proto_common_common_proto_msgTypes,
	}.Build()
	File_internal_proto_common_common_proto = out.File
	file_internal_proto_common_common_proto_rawDesc = nil
	file_internal_proto_common_common_proto_goTypes = nil
	file_internal_proto_common_common_proto_depIdxs = nil
}
