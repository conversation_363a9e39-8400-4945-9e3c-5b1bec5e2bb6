{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/v1/login": {"post": {"responses": {}}}, "/api/v1/user-group": {"post": {"description": "创建用户组", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "创建用户组", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/core_handler_user.createUserGroupReq"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/user-group/{id}/bulk-create-users": {"post": {"description": "添加用户到用户组", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "添加用户到用户组", "parameters": [{"type": "string", "description": "用户组ID", "name": "id", "in": "path", "required": true}, {"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/core_handler_user.bulkUserReq"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/users/search": {"post": {"description": "获取用户列表只需要传page和per_page参数，搜索时需要再加上name参数", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "获取用户列表", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/types.UsersResp"}}}}}}, "definitions": {"core_handler_user.bulkUserReq": {"type": "object", "properties": {"uids": {"type": "array", "items": {"type": "string"}}}}, "core_handler_user.createUserGroupReq": {"type": "object", "properties": {"description": {"type": "string"}, "name": {"type": "string"}, "uids": {"type": "array", "items": {"type": "string"}}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs": {"type": "object", "properties": {"account": {"type": "string"}, "captcha_answer": {"type": "string"}, "captcha_id": {"type": "string"}, "password": {"type": "string"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User": {"type": "object", "properties": {"account": {"type": "string"}, "email": {"type": "string"}, "group_ids": {"type": "array", "items": {"type": "string"}}, "identityType": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "token": {"type": "string"}, "uid": {"type": "string"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs": {"type": "object", "properties": {"account": {"type": "string"}, "identity_type": {"type": "string"}, "name": {"type": "string"}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "roles": {"type": "array", "items": {"type": "string"}}, "uids": {"type": "array", "items": {"type": "string"}}}}, "setting.ResourceType": {"type": "string", "enum": ["system", "custom"], "x-enum-varnames": ["ResourceTypeSystem", "ResourceTypeCustom"]}, "types.RoleBinding": {"type": "object", "properties": {"name": {"type": "string"}, "preset": {"type": "boolean"}, "role": {"type": "string"}, "type": {"$ref": "#/definitions/setting.ResourceType"}, "uid": {"type": "string"}}}, "types.UserInfo": {"type": "object", "properties": {"account": {"type": "string"}, "admin": {"type": "boolean"}, "email": {"type": "string"}, "identity_type": {"type": "string"}, "last_login_time": {"type": "integer"}, "name": {"type": "string"}, "phone": {"type": "string"}, "system_role_bindings": {"type": "array", "items": {"$ref": "#/definitions/types.RoleBinding"}}, "token": {"type": "string"}, "uid": {"type": "string"}}}, "types.UsersResp": {"type": "object", "properties": {"total_count": {"type": "integer"}, "users": {"type": "array", "items": {"$ref": "#/definitions/types.UserInfo"}}}}}}