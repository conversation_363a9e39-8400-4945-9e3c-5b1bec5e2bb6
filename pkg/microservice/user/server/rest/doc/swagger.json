{"swagger": "2.0", "info": {"contact": {}}, "paths": {"/api/v1/login": {"get": {"responses": {}}, "post": {"description": "Authenticate a user using local login credentials.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Local Login", "parameters": [{"description": "Login arguments", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs"}}], "responses": {"200": {"description": "Login successful", "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User"}, "headers": {"x-require-captcha": {"type": "string", "description": "Indicates if cap<PERSON><PERSON> is required"}}}}}}, "/api/v1/policy/role-bindings": {"get": {"description": "根据项目命名空间获取角色绑定信息，支持按用户ID或用户组ID进行过滤", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["policy"], "summary": "获取角色绑定列表", "parameters": [{"type": "string", "description": "项目命名空间", "name": "namespace", "in": "query", "required": true}, {"type": "string", "description": "用户ID，用于过滤特定用户的角色绑定", "name": "uid", "in": "query"}, {"type": "string", "description": "用户组ID，用于过滤特定用户组的角色绑定", "name": "gid", "in": "query"}], "responses": {"200": {"description": "角色绑定列表\" \"服务器内部错误", "schema": {"type": "array", "items": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.RoleBindingResp"}}}}}}, "/api/v1/policy/role-bindings/user/:uid": {"post": {"description": "更新用户系统角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["policy"], "summary": "更新用户系统角色", "parameters": [{"type": "string", "description": "用户ID", "name": "uid", "in": "path", "required": true}, {"type": "string", "description": "项目名称，系统角色为*", "name": "namespace", "in": "query", "required": true}, {"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/core_handler_permission.UpdateRoleBindingForUserReq"}}], "responses": {"200": {"description": "success", "schema": {"type": "string"}}}}}, "/api/v1/policy/role-templates": {"post": {"description": "添加系统角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["policy"], "summary": "添加系统角色", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq"}}], "responses": {"200": {"description": "success", "schema": {"type": "string"}}}}}, "/api/v1/policy/role-templates/{name}": {"put": {"description": "添加系统角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["policy"], "summary": "添加系统角色", "parameters": [{"type": "string", "description": "name", "name": "name", "in": "path", "required": true}, {"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq"}}], "responses": {"200": {"description": "success", "schema": {"type": "string"}}}}}, "/api/v1/policy/roles": {"get": {"description": "List Project Role Templates", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["policy"], "summary": "List Project Role Templates", "responses": {"200": {"description": "get user info successful", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.Role"}}}}}, "post": {"description": "添加系统角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["policy"], "summary": "添加系统角色", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq"}}], "responses": {"200": {"description": "success", "schema": {"type": "string"}}}}}, "/api/v1/policy/roles/{name}": {"put": {"description": "更新系统角色", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["policy"], "summary": "更新系统角色", "parameters": [{"type": "string", "description": "role name", "name": "name", "in": "path", "required": true}, {"type": "string", "description": "project name, system role is *", "name": "namespace", "in": "query", "required": true}, {"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq"}}], "responses": {"200": {"description": "success", "schema": {"type": "string"}}}}}, "/api/v1/user-group": {"post": {"description": "创建用户组", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "创建用户组", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/core_handler_user.createUserGroupReq"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/user-group/{id}/bulk-create-users": {"post": {"description": "添加用户到用户组", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "添加用户到用户组", "parameters": [{"type": "string", "description": "用户组ID", "name": "id", "in": "path", "required": true}, {"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/core_handler_user.bulkUserReq"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/userInfo": {"get": {"description": "Authenticate a user using local login credentials.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "User Info", "responses": {"200": {"description": "get user info successful", "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User"}}}}}, "/api/v1/users": {"post": {"description": "Create a new user in the system", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Create User", "parameters": [{"description": "User Info", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User"}}], "responses": {"200": {"description": "Create user successful", "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User"}}}}}, "/api/v1/users/search": {"post": {"description": "获取用户列表只需要传page和per_page参数，搜索时需要再加上name参数", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "获取用户列表", "parameters": [{"description": "body", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/types.UsersResp"}}}}}, "/api/v1/users/{uid}": {"put": {"description": "Update the information of a specific user by their UID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "Update user information", "parameters": [{"type": "string", "description": "User ID", "name": "uid", "in": "path", "required": true}, {"description": "User update information", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UpdateUserInfo"}}], "responses": {"200": {"description": "Success response", "schema": {"type": "object", "additionalProperties": true}}}}}, "/api/v1/users/{uid}/password": {"put": {"description": "更新用户密码", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["user"], "summary": "更新用户密码", "parameters": [{"type": "string", "description": "User ID", "name": "uid", "in": "path", "required": true}, {"description": "update password", "name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Password"}}], "responses": {"200": {"description": "OK"}}}}}, "definitions": {"core_handler_permission.UpdateRoleBindingForUserReq": {"type": "object", "properties": {"roles": {"type": "array", "items": {"type": "string"}}}}, "core_handler_user.bulkUserReq": {"type": "object", "properties": {"uids": {"type": "array", "items": {"type": "string"}}}}, "core_handler_user.createUserGroupReq": {"type": "object", "properties": {"description": {"type": "string"}, "name": {"type": "string"}, "uids": {"type": "array", "items": {"type": "string"}}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs": {"type": "object", "properties": {"account": {"type": "string"}, "captcha_answer": {"type": "string"}, "captcha_id": {"type": "string"}, "password": {"type": "string"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User": {"type": "object", "properties": {"account": {"type": "string"}, "email": {"type": "string"}, "group_ids": {"type": "array", "items": {"type": "string"}}, "identityType": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}, "token": {"type": "string"}, "uid": {"type": "string"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingGroupInfo": {"type": "object", "properties": {"group_id": {"type": "string"}, "name": {"type": "string"}, "user_infos": {"type": "array", "items": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo"}}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo": {"type": "object", "properties": {"account": {"type": "string"}, "identity_type": {"type": "string"}, "uid": {"type": "string"}, "username": {"type": "string"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq": {"type": "object", "properties": {"actions": {"type": "array", "items": {"type": "string"}}, "desc": {"type": "string"}, "name": {"type": "string"}, "namespace": {"type": "string"}, "type": {"type": "string"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Password": {"type": "object", "properties": {"newPassword": {"type": "string"}, "oldPassword": {"type": "string"}, "uid": {"type": "string"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs": {"type": "object", "properties": {"account": {"type": "string"}, "identity_type": {"type": "string"}, "name": {"type": "string"}, "page": {"type": "integer"}, "page_size": {"type": "integer"}, "roles": {"type": "array", "items": {"type": "string"}}, "uids": {"type": "array", "items": {"type": "string"}}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.RoleBindingResp": {"type": "object", "properties": {"binding_type": {"type": "string"}, "group_info": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingGroupInfo"}, "roles": {"type": "array", "items": {"type": "string"}}, "user_info": {"$ref": "#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UpdateUserInfo": {"type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}, "phone": {"type": "string"}}}, "github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User": {"type": "object", "properties": {"account": {"type": "string"}, "email": {"type": "string"}, "name": {"type": "string"}, "password": {"type": "string"}, "phone": {"type": "string"}}}, "setting.ResourceType": {"type": "string", "enum": ["system", "custom"], "x-enum-varnames": ["ResourceTypeSystem", "ResourceTypeCustom"]}, "types.Role": {"type": "object", "properties": {"desc": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "namespace": {"type": "string"}, "type": {"type": "string"}}}, "types.RoleBinding": {"type": "object", "properties": {"name": {"type": "string"}, "preset": {"type": "boolean"}, "role": {"type": "string"}, "type": {"$ref": "#/definitions/setting.ResourceType"}, "uid": {"type": "string"}}}, "types.UserInfo": {"type": "object", "properties": {"account": {"type": "string"}, "admin": {"type": "boolean"}, "email": {"type": "string"}, "identity_type": {"type": "string"}, "last_login_time": {"type": "integer"}, "name": {"type": "string"}, "phone": {"type": "string"}, "system_role_bindings": {"type": "array", "items": {"$ref": "#/definitions/types.RoleBinding"}}, "token": {"type": "string"}, "uid": {"type": "string"}}}, "types.UsersResp": {"type": "object", "properties": {"total": {"type": "integer"}, "users": {"type": "array", "items": {"$ref": "#/definitions/types.UserInfo"}}}}}}