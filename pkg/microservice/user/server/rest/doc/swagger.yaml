definitions:
  core_handler_permission.UpdateRoleBindingForUserReq:
    properties:
      roles:
        items:
          type: string
        type: array
    type: object
  core_handler_user.bulkUserReq:
    properties:
      uids:
        items:
          type: string
        type: array
    type: object
  core_handler_user.createUserGroupReq:
    properties:
      description:
        type: string
      name:
        type: string
      uids:
        items:
          type: string
        type: array
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs:
    properties:
      account:
        type: string
      captcha_answer:
        type: string
      captcha_id:
        type: string
      password:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User:
    properties:
      account:
        type: string
      email:
        type: string
      group_ids:
        items:
          type: string
        type: array
      identityType:
        type: string
      name:
        type: string
      phone:
        type: string
      token:
        type: string
      uid:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingGroupInfo:
    properties:
      group_id:
        type: string
      name:
        type: string
      user_infos:
        items:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo'
        type: array
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo:
    properties:
      account:
        type: string
      identity_type:
        type: string
      uid:
        type: string
      username:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq:
    properties:
      actions:
        items:
          type: string
        type: array
      desc:
        type: string
      name:
        type: string
      namespace:
        type: string
      type:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Password:
    properties:
      newPassword:
        type: string
      oldPassword:
        type: string
      uid:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs:
    properties:
      account:
        type: string
      identity_type:
        type: string
      name:
        type: string
      page:
        type: integer
      page_size:
        type: integer
      roles:
        items:
          type: string
        type: array
      uids:
        items:
          type: string
        type: array
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.RoleBindingResp:
    properties:
      binding_type:
        type: string
      group_info:
        $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingGroupInfo'
      roles:
        items:
          type: string
        type: array
      user_info:
        $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.BindingUserInfo'
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UpdateUserInfo:
    properties:
      email:
        type: string
      name:
        type: string
      phone:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User:
    properties:
      account:
        type: string
      email:
        type: string
      name:
        type: string
      password:
        type: string
      phone:
        type: string
    type: object
  setting.ResourceType:
    enum:
    - system
    - custom
    type: string
    x-enum-varnames:
    - ResourceTypeSystem
    - ResourceTypeCustom
  types.Role:
    properties:
      desc:
        type: string
      id:
        type: integer
      name:
        type: string
      namespace:
        type: string
      type:
        type: string
    type: object
  types.RoleBinding:
    properties:
      name:
        type: string
      preset:
        type: boolean
      role:
        type: string
      type:
        $ref: '#/definitions/setting.ResourceType'
      uid:
        type: string
    type: object
  types.UserInfo:
    properties:
      account:
        type: string
      admin:
        type: boolean
      email:
        type: string
      identity_type:
        type: string
      last_login_time:
        type: integer
      name:
        type: string
      phone:
        type: string
      system_role_bindings:
        items:
          $ref: '#/definitions/types.RoleBinding'
        type: array
      token:
        type: string
      uid:
        type: string
    type: object
  types.UsersResp:
    properties:
      total:
        type: integer
      users:
        items:
          $ref: '#/definitions/types.UserInfo'
        type: array
    type: object
info:
  contact: {}
paths:
  /api/v1/login:
    get:
      responses: {}
    post:
      consumes:
      - application/json
      description: Authenticate a user using local login credentials.
      parameters:
      - description: Login arguments
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          headers:
            x-require-captcha:
              description: Indicates if captcha is required
              type: string
          schema:
            $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User'
      summary: Local Login
      tags:
      - user
  /api/v1/policy/role-bindings:
    get:
      consumes:
      - application/json
      description: 根据项目命名空间获取角色绑定信息，支持按用户ID或用户组ID进行过滤
      parameters:
      - description: 项目命名空间
        in: query
        name: namespace
        required: true
        type: string
      - description: 用户ID，用于过滤特定用户的角色绑定
        in: query
        name: uid
        type: string
      - description: 用户组ID，用于过滤特定用户组的角色绑定
        in: query
        name: gid
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 角色绑定列表" "服务器内部错误
          schema:
            items:
              $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.RoleBindingResp'
            type: array
      summary: 获取角色绑定列表
      tags:
      - policy
  /api/v1/policy/role-bindings/user/:uid:
    post:
      consumes:
      - application/json
      description: 更新用户系统角色
      parameters:
      - description: 用户ID
        in: path
        name: uid
        required: true
        type: string
      - description: 项目名称，系统角色为*
        in: query
        name: namespace
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_permission.UpdateRoleBindingForUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 更新用户系统角色
      tags:
      - policy
  /api/v1/policy/role-templates:
    post:
      consumes:
      - application/json
      description: 添加系统角色
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 添加系统角色
      tags:
      - policy
  /api/v1/policy/role-templates/{name}:
    put:
      consumes:
      - application/json
      description: 添加系统角色
      parameters:
      - description: name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 添加系统角色
      tags:
      - policy
  /api/v1/policy/roles:
    get:
      consumes:
      - application/json
      description: List Project Role Templates
      produces:
      - application/json
      responses:
        "200":
          description: get user info successful
          schema:
            items:
              $ref: '#/definitions/types.Role'
            type: array
      summary: List Project Role Templates
      tags:
      - policy
    post:
      consumes:
      - application/json
      description: 添加系统角色
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 添加系统角色
      tags:
      - policy
  /api/v1/policy/roles/{name}:
    put:
      consumes:
      - application/json
      description: 更新系统角色
      parameters:
      - description: role name
        in: path
        name: name
        required: true
        type: string
      - description: project name, system role is *
        in: query
        name: namespace
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.CreateRoleReq'
      produces:
      - application/json
      responses:
        "200":
          description: success
          schema:
            type: string
      summary: 更新系统角色
      tags:
      - policy
  /api/v1/user-group:
    post:
      consumes:
      - application/json
      description: 创建用户组
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_user.createUserGroupReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 创建用户组
      tags:
      - user
  /api/v1/user-group/{id}/bulk-create-users:
    post:
      consumes:
      - application/json
      description: 添加用户到用户组
      parameters:
      - description: 用户组ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_user.bulkUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 添加用户到用户组
      tags:
      - user
  /api/v1/userInfo:
    get:
      consumes:
      - application/json
      description: Authenticate a user using local login credentials.
      produces:
      - application/json
      responses:
        "200":
          description: get user info successful
          schema:
            $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User'
      summary: User Info
      tags:
      - user
  /api/v1/users:
    post:
      consumes:
      - application/json
      description: Create a new user in the system
      parameters:
      - description: User Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User'
      produces:
      - application/json
      responses:
        "200":
          description: Create user successful
          schema:
            $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.User'
      summary: Create User
      tags:
      - user
  /api/v1/users/{uid}:
    put:
      consumes:
      - application/json
      description: Update the information of a specific user by their UID
      parameters:
      - description: User ID
        in: path
        name: uid
        required: true
        type: string
      - description: User update information
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.UpdateUserInfo'
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            additionalProperties: true
            type: object
      summary: Update user information
      tags:
      - user
  /api/v1/users/{uid}/password:
    put:
      consumes:
      - application/json
      description: 更新用户密码
      parameters:
      - description: User ID
        in: path
        name: uid
        required: true
        type: string
      - description: update password
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.Password'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 更新用户密码
      tags:
      - user
  /api/v1/users/search:
    post:
      consumes:
      - application/json
      description: 获取用户列表只需要传page和per_page参数，搜索时需要再加上name参数
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.UsersResp'
      summary: 获取用户列表
      tags:
      - user
swagger: "2.0"
