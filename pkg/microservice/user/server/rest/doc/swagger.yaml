definitions:
  core_handler_user.bulkUserReq:
    properties:
      uids:
        items:
          type: string
        type: array
    type: object
  core_handler_user.createUserGroupReq:
    properties:
      description:
        type: string
      name:
        type: string
      uids:
        items:
          type: string
        type: array
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.LoginArgs:
    properties:
      account:
        type: string
      captcha_answer:
        type: string
      captcha_id:
        type: string
      password:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_login.User:
    properties:
      account:
        type: string
      email:
        type: string
      group_ids:
        items:
          type: string
        type: array
      identityType:
        type: string
      name:
        type: string
      phone:
        type: string
      token:
        type: string
      uid:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs:
    properties:
      account:
        type: string
      identity_type:
        type: string
      name:
        type: string
      page:
        type: integer
      page_size:
        type: integer
      roles:
        items:
          type: string
        type: array
      uids:
        items:
          type: string
        type: array
    type: object
  setting.ResourceType:
    enum:
    - system
    - custom
    type: string
    x-enum-varnames:
    - ResourceTypeSystem
    - ResourceTypeCustom
  types.RoleBinding:
    properties:
      name:
        type: string
      preset:
        type: boolean
      role:
        type: string
      type:
        $ref: '#/definitions/setting.ResourceType'
      uid:
        type: string
    type: object
  types.UserInfo:
    properties:
      account:
        type: string
      admin:
        type: boolean
      email:
        type: string
      identity_type:
        type: string
      last_login_time:
        type: integer
      name:
        type: string
      phone:
        type: string
      system_role_bindings:
        items:
          $ref: '#/definitions/types.RoleBinding'
        type: array
      token:
        type: string
      uid:
        type: string
    type: object
  types.UsersResp:
    properties:
      total_count:
        type: integer
      users:
        items:
          $ref: '#/definitions/types.UserInfo'
        type: array
    type: object
info:
  contact: {}
paths:
  /api/v1/login:
    post:
      responses: {}
  /api/v1/user-group:
    post:
      consumes:
      - application/json
      description: 创建用户组
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_user.createUserGroupReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 创建用户组
      tags:
      - user
  /api/v1/user-group/{id}/bulk-create-users:
    post:
      consumes:
      - application/json
      description: 添加用户到用户组
      parameters:
      - description: 用户组ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/core_handler_user.bulkUserReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 添加用户到用户组
      tags:
      - user
  /api/v1/users/search:
    post:
      consumes:
      - application/json
      description: 获取用户列表只需要传page和per_page参数，搜索时需要再加上name参数
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_user_core_service_permission.QueryArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.UsersResp'
      summary: 获取用户列表
      tags:
      - user
swagger: "2.0"
