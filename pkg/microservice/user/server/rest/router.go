/*
Copyright 2023 The KodeRover Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package rest

import (
	"github.com/gin-gonic/gin"
	"github.com/koderover/zadig/v2/pkg/microservice/user/core/handler"
	_ "github.com/koderover/zadig/v2/pkg/microservice/user/server/rest/doc"
	swaggerfiles "github.com/swaggo/files"
	ginswagger "github.com/swaggo/gin-swagger"
)

func (s *engine) injectRouterGroup(router *gin.RouterGroup) {
	router.GET("/api/v1/users/apidocs/*any", ginswagger.WrapHandler(swaggerfiles.Handler))
	for name, r := range map[string]injector{
		"/openapi/": new(handler.OpenAPIRouter),
	} {
		r.Inject(router.Group(name))
	}

	for _, r := range []injector{
		new(handler.Router),
	} {
		r.Inject(router.Group("/api/v1"))
	}
}

type injector interface {
	Inject(router *gin.RouterGroup)
}
