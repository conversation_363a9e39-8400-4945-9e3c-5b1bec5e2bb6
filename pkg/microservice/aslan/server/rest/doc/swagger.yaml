definitions:
  client.Branch:
    properties:
      merged:
        type: boolean
      name:
        type: string
      protected:
        type: boolean
    type: object
  client.Commit:
    properties:
      author:
        type: string
      commit_id:
        type: string
      commit_message:
        type: string
      created_at:
        type: integer
    type: object
  client.Namespace:
    properties:
      kind:
        type: string
      name:
        type: string
      path:
        type: string
      project_uuid:
        type: string
    type: object
  client.Project:
    properties:
      defaultBranch:
        type: string
      description:
        type: string
      id:
        type: integer
      name:
        type: string
      namespace:
        type: string
      repo_id:
        type: string
      repo_uuid:
        type: string
    type: object
  client.PullRequest:
    properties:
      authorUsername:
        type: string
      base:
        type: string
      createdAt:
        type: integer
      id:
        type: integer
      number:
        type: integer
      projectId:
        type: integer
      sourceBranch:
        type: string
      state:
        type: string
      targetBranch:
        type: string
      title:
        type: string
      updatedAt:
        type: integer
      user:
        type: string
    type: object
  client.Tag:
    properties:
      message:
        type: string
      name:
        type: string
      tarball_url:
        type: string
      zipball_url:
        type: string
    type: object
  config.ApprovalStatus:
    enum:
    - ""
    - approve
    - reject
    - redirect
    - done
    type: string
    x-enum-varnames:
    - ApprovalStatusPending
    - ApprovalStatusApprove
    - ApprovalStatusReject
    - ApprovalStatusRedirect
    - ApprovalStatusDone
  config.ApprovalType:
    enum:
    - native
    - lark
    - dingtalk
    - workwx
    type: string
    x-enum-varnames:
    - NativeApproval
    - LarkApproval
    - DingTalkApproval
    - WorkWXApproval
  config.CollaborationType:
    enum:
    - share
    - new
    type: string
    x-enum-varnames:
    - CollaborationShare
    - CollaborationNew
  config.CommonEnvCfgType:
    enum:
    - Ingress
    - ConfigMap
    - Secret
    - PVC
    type: string
    x-enum-varnames:
    - CommonEnvCfgTypeIngress
    - CommonEnvCfgTypeConfigMap
    - CommonEnvCfgTypeSecret
    - CommonEnvCfgTypePvc
  config.CustomWorkflowTaskType:
    enum:
    - workflow
    - test
    - scan
    - delivery
    type: string
    x-enum-varnames:
    - WorkflowTaskTypeWorkflow
    - WorkflowTaskTypeTesting
    - WorkflowTaskTypeScanning
    - WorkflowTaskTypeDelivery
  config.DBInstanceType:
    enum:
    - mysql
    - mariadb
    type: string
    x-enum-varnames:
    - DBInstanceTypeMySQL
    - DBInstanceTypeMariaDB
  config.DeployContent:
    enum:
    - image
    - vars
    - config
    type: string
    x-enum-varnames:
    - DeployImage
    - DeployVars
    - DeployConfig
  config.DeploySourceType:
    enum:
    - runtime
    - fixed
    - fromjob
    type: string
    x-enum-varnames:
    - SourceRuntime
    - SourceFixed
    - SourceFromJob
  config.DistributeImageMethod:
    enum:
    - image_push
    - cloud_sync
    type: string
    x-enum-varnames:
    - DistributeImageMethodImagePush
    - DistributeImageMethodCloudSync
  config.EnvOperation:
    enum:
    - default
    - rollback
    type: string
    x-enum-varnames:
    - EnvOperationDefault
    - EnvOperationRollback
  config.EnvOperationType:
    enum:
    - zadig
    - sae
    - sae_change_order
    type: string
    x-enum-varnames:
    - EnvOperationTypeZadig
    - EnvOperationTypeSae
    - EnvOperationTypeSaeChangeOrder
  config.EnvType:
    enum:
    - zadig
    - sae
    type: string
    x-enum-varnames:
    - EnvTypeZadig
    - EnvTypeSae
  config.HookEventType:
    enum:
    - push
    - pull_request
    - tag
    - ref-updated
    type: string
    x-enum-varnames:
    - HookEventPush
    - HookEventPr
    - HookEventTag
    - HookEventUpdated
  config.JiraAuthType:
    enum:
    - password_or_token
    - personal_access_token
    type: string
    x-enum-varnames:
    - JiraBasicAuth
    - JiraPersonalAccessToken
  config.JobErrorPolicy:
    enum:
    - stop
    - ignore_error
    - manual_check
    - retry
    type: string
    x-enum-varnames:
    - JobErrorPolicyStop
    - JobErrorPolicyIgnoreError
    - JobErrorPolicyManualCheck
    - JobErrorPolicyRetry
  config.JobRunPolicy:
    enum:
    - ""
    - default_not_run
    - force_run
    - skip
    type: string
    x-enum-comments:
      DefaultNotRun: default not run this job
      DefaultRun: default run this job
      ForceRun: force run this job
    x-enum-varnames:
    - DefaultRun
    - DefaultNotRun
    - ForceRun
    - SkipRun
  config.JobType:
    enum:
    - build
    - deploy
    - zadig-build
    - zadig-distribute-image
    - zadig-test
    - zadig-scanning
    - custom-deploy
    - zadig-deploy
    - zadig-vm-deploy
    - zadig-helm-deploy
    - zadig-helm-chart-deploy
    - freestyle
    - plugin
    - k8s-blue-green-deploy
    - k8s-blue-green-release
    - k8s-canary-deploy
    - k8s-canary-release
    - k8s-gray-release
    - k8s-gray-rollback
    - k8s-resource-patch
    - istio-release
    - istio-rollback
    - update-env-istio-config
    - jira
    - nacos
    - apollo
    - sql
    - jenkins
    - meego-transition
    - workflow-trigger
    - offline-service
    - mse-gray-release
    - mse-gray-offline
    - guanceyun-check
    - grafana
    - blueking
    - approval
    - notification
    - sae-deploy
    type: string
    x-enum-varnames:
    - JobBuild
    - JobDeploy
    - JobZadigBuild
    - JobZadigDistributeImage
    - JobZadigTesting
    - JobZadigScanning
    - JobCustomDeploy
    - JobZadigDeploy
    - JobZadigVMDeploy
    - JobZadigHelmDeploy
    - JobZadigHelmChartDeploy
    - JobFreestyle
    - JobPlugin
    - JobK8sBlueGreenDeploy
    - JobK8sBlueGreenRelease
    - JobK8sCanaryDeploy
    - JobK8sCanaryRelease
    - JobK8sGrayRelease
    - JobK8sGrayRollback
    - JobK8sPatch
    - JobIstioRelease
    - JobIstioRollback
    - JobUpdateEnvIstioConfig
    - JobJira
    - JobNacos
    - JobApollo
    - JobSQL
    - JobJenkins
    - JobMeegoTransition
    - JobWorkflowTrigger
    - JobOfflineService
    - JobMseGrayRelease
    - JobMseGrayOffline
    - JobGuanceyunCheck
    - JobGrafana
    - JobBlueKing
    - JobApproval
    - JobNotification
    - JobSAEDeploy
  config.PipelineType:
    enum:
    - single
    - workflow
    - freestyle
    - test
    - service
    - workflow_v3
    - workflow_v4
    - artifact
    - scanning
    type: string
    x-enum-varnames:
    - SingleType
    - WorkflowType
    - FreestyleType
    - TestType
    - PipelineTypeService
    - WorkflowTypeV3
    - WorkflowTypeV4
    - ArtifactType
    - ScanningType
  config.ReleasePlanJobStatus:
    enum:
    - todo
    - done
    - skipped
    - failed
    - running
    type: string
    x-enum-varnames:
    - ReleasePlanJobStatusTodo
    - ReleasePlanJobStatusDone
    - ReleasePlanJobStatusSkipped
    - ReleasePlanJobStatusFailed
    - ReleasePlanJobStatusRunning
  config.ReleasePlanJobType:
    enum:
    - text
    - workflow
    type: string
    x-enum-varnames:
    - JobText
    - JobWorkflow
  config.ReleasePlanStatus:
    enum:
    - planning
    - wait_for_approval
    - executing
    - denied
    - timeout
    - success
    - cancel
    type: string
    x-enum-varnames:
    - StatusPlanning
    - StatusWaitForApprove
    - StatusExecuting
    - StatusApprovalDenied
    - StatusTimeoutForWindow
    - StatusSuccess
    - StatusCancel
  config.ScanningModuleType:
    enum:
    - ""
    - service_scanning
    type: string
    x-enum-varnames:
    - NormalScanningType
    - ServiceScanningType
  config.ScheduleType:
    enum:
    - timing
    - gap
    - unix_stamp
    type: string
    x-enum-varnames:
    - TimingSchedule
    - GapSchedule
    - UnixstampSchedule
  config.ServiceType:
    enum:
    - helm
    - helm_chart
    - k8s
    - sae
    - vm
    type: string
    x-enum-varnames:
    - ServiceTypeHelm
    - ServiceTypeHelmChart
    - ServiceTypeK8S
    - ServiceTypeSae
    - ServiceTypeVM
  config.Status:
    enum:
    - disabled
    - created
    - running
    - passed
    - skipped
    - failed
    - timeout
    - cancelled
    - pause
    - waiting
    - queued
    - blocked
    - pending
    - changed
    - notRun
    - prepare
    - reject
    - distributed
    - wait_for_approval
    - debug_before
    - debug_after
    - unstable
    - wait_for_manual_error_handling
    type: string
    x-enum-varnames:
    - StatusDisabled
    - StatusCreated
    - StatusRunning
    - StatusPassed
    - StatusSkipped
    - StatusFailed
    - StatusTimeout
    - StatusCancelled
    - StatusPause
    - StatusWaiting
    - StatusQueued
    - StatusBlocked
    - QueueItemPending
    - StatusChanged
    - StatusNotRun
    - StatusPrepare
    - StatusReject
    - StatusDistributed
    - StatusWaitingApprove
    - StatusDebugBefore
    - StatusDebugAfter
    - StatusUnstable
    - StatusManualApproval
  git.TreeNode:
    properties:
      full_path:
        type: string
      is_dir:
        type: boolean
      name:
        type: string
      size:
        type: integer
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_aslan_core_common_service.EnvService:
    properties:
      deployed:
        type: boolean
      latest_variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      latest_variable_yaml:
        type: string
      override_kvs:
        type: string
      service_modules:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      service_name:
        type: string
      updatable:
        type: boolean
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      variable_yaml:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_aslan_core_delivery_service.ImageData:
    properties:
      containerName:
        type: string
      image:
        type: string
      imageName:
        type: string
      imageTag:
        type: string
      selected:
        type: boolean
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_aslan_core_environment_service.ProductResp:
    properties:
      cluster_id:
        type: string
      cluster_name:
        type: string
      env_name:
        type: string
      error:
        type: string
      id:
        type: string
      is_existed:
        type: boolean
      is_local:
        type: boolean
      is_prod:
        type: boolean
      isPublic:
        type: boolean
      istio_grayscale_base_env:
        type: string
      istio_grayscale_enable:
        type: boolean
      istio_grayscale_is_base:
        type: boolean
      namespace:
        type: string
      product_name:
        type: string
      recycle_day:
        type: integer
      registry_id:
        type: string
      related_envs:
        description: New Since v2.1.0
        items:
          $ref: '#/definitions/service.SharedNSEnvs'
        type: array
      render:
        $ref: '#/definitions/models.RenderInfo'
      services:
        items:
          items:
            $ref: '#/definitions/models.ProductService'
          type: array
        type: array
      share_env_base_env:
        type: string
      share_env_enable:
        description: New Since v1.11.0
        type: boolean
      share_env_is_base:
        type: boolean
      source:
        type: string
      status:
        type: string
      update_by:
        type: string
      update_time:
        type: integer
      vars:
        items:
          $ref: '#/definitions/template.RenderKV'
        type: array
      yaml_data:
        allOf:
        - $ref: '#/definitions/template.CustomYaml'
        description: used for cron service
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_aslan_core_multicluster_service.EnvInfo:
    properties:
      display_name:
        type: string
      name:
        type: string
      production:
        type: boolean
      project_name:
        type: string
    type: object
  github_com_koderover_zadig_v2_pkg_microservice_aslan_core_service_service.Variable:
    properties:
      description:
        type: string
      key:
        type: string
      value:
        type: string
    type: object
  handler.AdvancedRegistrySetting:
    properties:
      enable_tls:
        type: boolean
      modified:
        type: boolean
      tls_cert:
        type: string
    type: object
  handler.BindSAEAppToServiceReq:
    properties:
      service_module:
        type: string
      service_name:
        type: string
    type: object
  handler.CreateLLMIntegrationRequest:
    properties:
      base_url:
        type: string
      enable_proxy:
        type: boolean
      model:
        type: string
      provider_name:
        $ref: '#/definitions/llm.Provider'
      token:
        type: string
    type: object
  handler.DeleteProductServicesRequest:
    properties:
      service_names:
        items:
          type: string
        type: array
    type: object
  handler.FetchServiceYamlResponse:
    properties:
      yaml:
        type: string
    type: object
  handler.GetLarkUserGroupMembersResp:
    properties:
      has_more:
        type: boolean
      members:
        items:
          $ref: '#/definitions/lark.UserInfo'
        type: array
      page_token:
        type: string
    type: object
  handler.GetLarkUserGroupsResp:
    properties:
      has_more:
        type: boolean
      page_token:
        type: string
      user_groups:
        items:
          $ref: '#/definitions/lark.LarkUserGroup'
        type: array
    type: object
  handler.HelmDeployJobMergeImageRequest:
    properties:
      env_name:
        type: string
      production:
        type: boolean
      service_modules:
        items:
          $ref: '#/definitions/handler.ModuleAndImage'
        type: array
      service_name:
        type: string
      update_service_revision:
        type: boolean
      values_yaml:
        type: string
    type: object
  handler.ListImagesOption:
    properties:
      names:
        items:
          type: string
        type: array
    type: object
  handler.ListTarsOption:
    properties:
      names:
        items:
          type: string
        type: array
    type: object
  handler.MatchBranchesListRequest:
    properties:
      regular:
        type: string
      repo_name:
        description: pro Name, id/name -> gitlab = id
        type: string
      repo_owner:
        type: string
    type: object
  handler.ModuleAndImage:
    properties:
      image:
        type: string
      name:
        type: string
    type: object
  handler.OpenAPIGetOperationLogsResponse:
    properties:
      operation_logs:
        items:
          $ref: '#/definitions/handler.OpenAPIOperationLog'
        type: array
      total:
        type: integer
    type: object
  handler.OpenAPIOperationLog:
    properties:
      body_type:
        $ref: '#/definitions/types.RequestBodyType'
      created_at:
        type: integer
      detail:
        type: string
      function:
        type: string
      method:
        type: string
      project_key:
        type: string
      request_body:
        type: string
      scene:
        type: string
      status:
        type: integer
      targets:
        items:
          type: string
        type: array
      username:
        type: string
    type: object
  handler.Registry:
    properties:
      access_key:
        type: string
      advanced_setting:
        $ref: '#/definitions/handler.AdvancedRegistrySetting'
      id:
        type: string
      is_default:
        type: boolean
      namespace:
        type: string
      reg_addr:
        type: string
      secret_key:
        type: string
    type: object
  handler.ServiceDeployHistoryResp:
    properties:
      rows:
        items:
          $ref: '#/definitions/service.ListEnvServiceVersionsResponse'
        type: array
      total:
        type: integer
    type: object
  handler.UpdateProductParams:
    properties:
      alias:
        type: string
      analysis_config:
        allOf:
        - $ref: '#/definitions/models.AnalysisConfig'
        description: New Since v.1.18.0, env configs
      base_env_name:
        type: string
      base_name:
        type: string
      chart_infos:
        items:
          $ref: '#/definitions/template.ServiceRender'
        type: array
      cluster_id:
        type: string
      create_time:
        type: integer
      default_values:
        description: |-
          New Since v1.19.0, for env global variables
          GlobalValues for helm projects
        type: string
      enabled:
        type: boolean
      env_configs:
        description: New Since v1.13.0.
        items:
          $ref: '#/definitions/models.CreateUpdateCommonEnvCfgArgs'
        type: array
      env_name:
        type: string
      error:
        type: string
      global_variables:
        description: GlobalValues for k8s projects
        items:
          $ref: '#/definitions/types.GlobalVariableKV'
        type: array
      id:
        type: string
      is_existed:
        description: IsExisted is true if this environment is created from an existing
          one
        type: boolean
      is_opensource:
        type: boolean
      isPublic:
        type: boolean
      istio_grayscale:
        allOf:
        - $ref: '#/definitions/models.IstioGrayscale'
        description: New Since v2.1.0.
      namespace:
        type: string
      notification_configs:
        items:
          $ref: '#/definitions/models.NotificationConfig'
        type: array
      pre_sleep_status:
        additionalProperties:
          type: integer
        description: New Since v1.19.0, env sleep configs
        type: object
      product_name:
        type: string
      production:
        description: For production environment
        type: boolean
      recycle_day:
        type: integer
      registry_id:
        type: string
      render:
        allOf:
        - $ref: '#/definitions/models.RenderInfo'
        description: Deprecated in 1.19.0, will be removed in 1.20.0
      revision:
        type: integer
      roleIds:
        items:
          type: integer
        type: array
      service_deploy_strategy:
        additionalProperties:
          type: string
        description: New Since v1.16.0, used to determine whether to install resources
        type: object
      service_names:
        items:
          type: string
        type: array
      services:
        items:
          items:
            $ref: '#/definitions/models.ProductService'
          type: array
        type: array
      share_env:
        allOf:
        - $ref: '#/definitions/models.ProductShareEnv'
        description: New Since v1.11.0.
      source:
        type: string
      status:
        type: string
      update_by:
        type: string
      update_time:
        type: integer
      variable_yaml:
        type: string
      visibility:
        type: string
      yaml_data:
        $ref: '#/definitions/template.CustomYaml'
    type: object
  handler.UpdateSprintTemplateStageWorkflowsRequest:
    properties:
      update_time:
        type: integer
      workflows:
        items:
          $ref: '#/definitions/models.SprintWorkflow'
        type: array
    type: object
  handler.UpdateSprintWorkItemDescRequest:
    properties:
      des:
        type: string
    type: object
  handler.UpdateSprintWorkItemTitleRequest:
    properties:
      title:
        type: string
    type: object
  handler.ValidatorResp:
    properties:
      message:
        type: string
    type: object
  handler.addServiceLabelReq:
    properties:
      label_id:
        type: string
      production:
        type: string
      project_key:
        type: string
      service_name:
        type: string
      value:
        type: string
    type: object
  handler.checkLLMIntegrationResponse:
    properties:
      check:
        type: boolean
    type: object
  handler.createServiceTemplateRequest:
    properties:
      product_name:
        type: string
      service_name:
        type: string
      service_variable_kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      source:
        type: string
      type:
        type: string
      variable_yaml:
        type: string
      visibility:
        type: string
      yaml:
        type: string
    required:
    - product_name
    - service_name
    - source
    - type
    - visibility
    - yaml
    type: object
  handler.getGlobalVariablesRespone:
    properties:
      global_variables:
        items:
          $ref: '#/definitions/types.GlobalVariableKV'
        type: array
      revision:
        type: integer
    type: object
  handler.getInitProductResponse:
    properties:
      chart_infos:
        items:
          $ref: '#/definitions/template.ServiceRender'
        type: array
      create_time:
        type: integer
      product_name:
        type: string
      render:
        $ref: '#/definitions/models.RenderInfo'
      revision:
        type: integer
      services:
        items:
          items:
            $ref: '#/definitions/models.ProductService'
          type: array
        type: array
      source:
        type: string
      update_by:
        type: string
    type: object
  handler.getYamlTemplateVariablesReq:
    properties:
      content:
        type: string
      variable_yaml:
        type: string
    required:
    - content
    - variable_yaml
    type: object
  handler.listWorkflowV4Resp:
    properties:
      total:
        type: integer
      workflow_list:
        items:
          $ref: '#/definitions/workflow.Workflow'
        type: array
    type: object
  handler.updateGlobalVariablesRequest:
    properties:
      global_variables:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
    type: object
  handler.updateK8sProductGlobalVariablesRequest:
    properties:
      current_revision:
        type: integer
      global_variables:
        items:
          $ref: '#/definitions/types.GlobalVariableKV'
        type: array
    type: object
  handler.updateServiceLabelReq:
    properties:
      value:
        type: string
    type: object
  handler.updateServiceVariableRequest:
    properties:
      service_variable_kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      variable_yaml:
        type: string
    required:
    - service_variable_kvs
    - variable_yaml
    type: object
  jira.IssueTypeWithStatus:
    properties:
      status:
        items:
          type: string
        type: array
      type:
        type: string
    type: object
  jira.Status:
    properties:
      id:
        type: string
      name:
        type: string
      statusCategory:
        $ref: '#/definitions/jira.StatusCategory'
    type: object
  jira.StatusCategory:
    properties:
      id:
        type: integer
      key:
        type: string
    type: object
  kube.SetIstioGrayscaleConfigRequest:
    properties:
      grayscale_strategy:
        type: string
      header_match_configs:
        items:
          $ref: '#/definitions/models.IstioHeaderMatchConfig'
        type: array
      weight_configs:
        items:
          $ref: '#/definitions/models.IstioWeightConfig'
        type: array
    type: object
  lark.ApproveNodeType:
    enum:
    - ""
    - user_group
    - user_dynamic_group
    type: string
    x-enum-varnames:
    - ApproveNodeTypeUser
    - ApproveNodeTypeUserGroup
    - ApproveNodeTypeUserDynamicGroup
  lark.ApproveType:
    enum:
    - AND
    - OR
    - SEQUENTIAL
    type: string
    x-enum-varnames:
    - ApproveTypeAnd
    - ApproveTypeOr
    - ApproveTypeSequential
  lark.LarkUserGroup:
    properties:
      description:
        type: string
      group_id:
        type: string
      group_name:
        type: string
      member_department_count:
        type: integer
      member_user_count:
        type: integer
    type: object
  lark.UserInfo:
    properties:
      avatar:
        type: string
      id:
        type: string
      id_type:
        type: string
      is_executor:
        description: IsExecutor marks if the user is the executor of the workflow
        type: boolean
      name:
        type: string
    type: object
  llm.Provider:
    enum:
    - openai
    - deepseek
    - deepseek_siliconcloud
    - azure_openai
    - azure_ad_openai
    type: string
    x-enum-varnames:
    - ProviderOpenAI
    - ProviderDeepSeek
    - ProviderDeepSeekSiliconCloud
    - ProviderAzure
    - ProviderAzureAD
  models.AdvancedConfig:
    properties:
      cluster_access_yaml:
        type: string
      enable_irsa:
        type: boolean
      irsa_role_arn:
        type: string
      node_labels:
        items:
          $ref: '#/definitions/models.NodeSelectorRequirement'
        type: array
      schedule_strategy:
        items:
          $ref: '#/definitions/models.ScheduleStrategy'
        type: array
      schedule_workflow:
        type: boolean
      strategy:
        type: string
      tolerations:
        type: string
    type: object
  models.AnalysisConfig:
    properties:
      resource_types:
        items:
          $ref: '#/definitions/models.ResourceType'
        type: array
    type: object
  models.ApolloJobSpec:
    properties:
      apolloID:
        type: string
      namespaceList:
        items:
          $ref: '#/definitions/models.ApolloNamespace'
        type: array
      namespaceListOption:
        items:
          $ref: '#/definitions/models.ApolloNamespace'
        type: array
    type: object
  models.ApolloKV:
    properties:
      key:
        type: string
      val:
        type: string
    type: object
  models.ApolloNamespace:
    properties:
      appID:
        type: string
      clusterID:
        type: string
      env:
        type: string
      kv:
        items:
          $ref: '#/definitions/models.ApolloKV'
        type: array
      namespace:
        type: string
      original_config:
        items:
          $ref: '#/definitions/models.ApolloKV'
        type: array
      type:
        type: string
    type: object
  models.Approval:
    properties:
      description:
        type: string
      dingtalk_approval:
        $ref: '#/definitions/models.DingTalkApproval'
      enabled:
        type: boolean
      end_time:
        type: integer
      lark_approval:
        $ref: '#/definitions/models.LarkApproval'
      native_approval:
        $ref: '#/definitions/models.NativeApproval'
      start_time:
        type: integer
      status:
        $ref: '#/definitions/config.Status'
      type:
        $ref: '#/definitions/config.ApprovalType'
      workwx_approval:
        $ref: '#/definitions/models.WorkWXApproval'
    type: object
  models.ArtifactArgs:
    properties:
      deploy:
        items:
          $ref: '#/definitions/models.DeployEnv'
        type: array
      file_name:
        type: string
      image:
        type: string
      image_name:
        type: string
      name:
        type: string
      service_name:
        type: string
      task_id:
        type: integer
      url:
        type: string
      workflow_name:
        type: string
    type: object
  models.BlueGreenDeployV2JobSpec:
    properties:
      env:
        type: string
      env_options:
        items:
          $ref: '#/definitions/models.ZadigBlueGreenDeployEnvInformation'
        type: array
      production:
        type: boolean
      service_options:
        items:
          $ref: '#/definitions/models.BlueGreenDeployV2Service'
        type: array
      services:
        items:
          $ref: '#/definitions/models.BlueGreenDeployV2Service'
        type: array
      source:
        type: string
      version:
        type: string
    type: object
  models.BlueGreenDeployV2Service:
    properties:
      blue_deployment_name:
        type: string
      blue_deployment_yaml:
        type: string
      blue_service_name:
        type: string
      blue_service_yaml:
        type: string
      green_deployment_name:
        type: string
      green_service_name:
        type: string
      service_and_image:
        items:
          $ref: '#/definitions/models.BlueGreenDeployV2ServiceModuleAndImage'
        type: array
      service_name:
        description: ServiceName is zadig service name
        type: string
    type: object
  models.BlueGreenDeployV2ServiceModuleAndImage:
    properties:
      image:
        type: string
      image_name:
        description: Following fields only save for frontend
        type: string
      name:
        description: Name is the service module name for the sake of old data.
        type: string
      service_module:
        type: string
      service_name:
        type: string
      value:
        type: string
    type: object
  models.Build:
    properties:
      advanced_setting_modified:
        description: New since V1.10.0. Only to tell the webpage should the advanced
          settings be displayed
        type: boolean
      cache_dir_type:
        $ref: '#/definitions/types.CacheDirType'
      cache_enable:
        description: New since V1.10.0.
        type: boolean
      cache_user_dir:
        type: string
      caches:
        description: 'TODO: Deprecated.'
        items:
          type: string
        type: array
      deploy_infrastructure:
        type: string
      deploy_repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      deploy_vm_labels:
        items:
          type: string
        type: array
      desc:
        type: string
      id:
        type: string
      infrastructure:
        type: string
      jenkins_build:
        $ref: '#/definitions/models.JenkinsBuild'
      name:
        type: string
      outputs:
        items:
          $ref: '#/definitions/models.Output'
        type: array
      pm_deploy_script_type:
        $ref: '#/definitions/types.ScriptType'
      pm_deploy_scripts:
        type: string
      post_build:
        $ref: '#/definitions/models.PostBuild'
      pre_build:
        $ref: '#/definitions/models.PreBuild'
      pre_deploy:
        $ref: '#/definitions/models.PreDeploy'
      product_name:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      script_type:
        $ref: '#/definitions/types.ScriptType'
      scripts:
        type: string
      source:
        type: string
      sshs:
        items:
          type: string
        type: array
      target_repos:
        items:
          $ref: '#/definitions/models.TargetRepo'
        type: array
      targets:
        description: |-
          在任一编译配置模板中只能出现一次
          对于k8s部署是传入容器名称
          对于物理机部署是服务名称
        items:
          $ref: '#/definitions/models.ServiceModuleTarget'
        type: array
      team:
        type: string
      template_id:
        type: string
      timeout:
        type: integer
      update_by:
        type: string
      update_time:
        type: integer
      vm_labels:
        items:
          type: string
        type: array
    type: object
  models.BuildArgs:
    properties:
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
    type: object
  models.CallbackArgs:
    properties:
      callback_url:
        description: url-encoded full path
        type: string
      callback_vars:
        additionalProperties: true
        description: custom defied vars, will be set to body of callback request
        type: object
    type: object
  models.CanaryDeployJobSpec:
    properties:
      cluster_id:
        type: string
      docker_registry_id:
        type: string
      namespace:
        type: string
      target_options:
        items:
          $ref: '#/definitions/models.CanaryTarget'
        type: array
      targets:
        items:
          $ref: '#/definitions/models.CanaryTarget'
        type: array
    type: object
  models.CanaryTarget:
    properties:
      canary_percentage:
        type: integer
      container_name:
        type: string
      deploy_timeout:
        description: unit is minute.
        type: integer
      image:
        type: string
      k8s_service_name:
        type: string
      workload_name:
        type: string
      workload_type:
        type: string
    type: object
  models.CodeHost:
    properties:
      access_token:
        type: string
      address:
        type: string
      alias:
        type: string
      application_id:
        type: string
      auth_type:
        $ref: '#/definitions/types.AuthType'
      client_secret:
        type: string
      created_at:
        type: integer
      deleted_at:
        type: integer
      enable_proxy:
        type: boolean
      id:
        type: integer
      integration_level:
        $ref: '#/definitions/setting.IntegrationLevel'
      is_ready:
        type: string
      namespace:
        type: string
      password:
        type: string
      perforce_host:
        description: perforce Type parameters
        type: string
      perforce_port:
        type: integer
      private_access_token:
        type: string
      project:
        type: string
      refresh_token:
        type: string
      region:
        type: string
      ssh_key:
        type: string
      type:
        type: string
      updated_at:
        type: integer
      username:
        description: common parameters
        type: string
    type: object
  models.Commit:
    properties:
      message:
        type: string
      sha:
        type: string
    type: object
  models.Container:
    properties:
      image:
        type: string
      image_name:
        type: string
      imagePath:
        $ref: '#/definitions/models.ImagePathSpec'
      name:
        type: string
      type:
        $ref: '#/definitions/setting.ContainerType'
    type: object
  models.CreateUpdateCommonEnvCfgArgs:
    properties:
      auto_sync:
        type: boolean
      common_env_cfg_type:
        $ref: '#/definitions/config.CommonEnvCfgType'
      env_name:
        type: string
      git_repo_config:
        $ref: '#/definitions/template.GitRepoConfig'
      name:
        type: string
      product_name:
        type: string
      production:
        type: boolean
      restart_associated_svc:
        type: boolean
      service_name:
        type: string
      services:
        items:
          type: string
        type: array
      yaml_data:
        type: string
    type: object
  models.CustomDeployJobSpec:
    properties:
      cluster_id:
        type: string
      cluster_source:
        type: string
      docker_registry_id:
        type: string
      namespace:
        type: string
      skip_check_run_status:
        type: boolean
      source:
        description: support two sources, runtime/fixed.
        type: string
      target_options:
        items:
          $ref: '#/definitions/models.DeployTargets'
        type: array
      targets:
        items:
          $ref: '#/definitions/models.DeployTargets'
        type: array
      timeout:
        description: unit is minute.
        type: integer
    type: object
  models.CustomField:
    properties:
      build_code_msg:
        additionalProperties:
          type: integer
        type: object
      build_service_component:
        additionalProperties:
          type: integer
        type: object
      deploy_env:
        additionalProperties:
          type: integer
        type: object
      deploy_service_component:
        additionalProperties:
          type: integer
        type: object
      duration:
        type: integer
      executor:
        type: integer
      remark:
        type: integer
      status:
        type: integer
      task_id:
        type: integer
      test_result:
        additionalProperties:
          type: integer
        type: object
    type: object
  models.DBInstance:
    properties:
      created_at:
        type: integer
      host:
        type: string
      id:
        type: string
      name:
        type: string
      password:
        type: string
      port:
        type: string
      projects:
        items:
          type: string
        type: array
      type:
        $ref: '#/definitions/config.DBInstanceType'
      update_by:
        type: string
      updated_at:
        type: integer
      username:
        type: string
    type: object
  models.DeployArgs:
    properties:
      image:
        description: |-
          部署镜像名称
          格式: xxx.com/{namespace}/{service name}:{timestamp}-{suffix}}
          timestamp format: 20060102150405
        type: string
      namespace:
        description: 目标部署环境
        type: string
      package_file:
        description: |-
          部署二进制包名称
          格式: {service name}-{timestamp}-{suffix}}.tar.gz
          timestamp format: 20060102150405
        type: string
      suffix:
        description: 镜像或者二进制名称后缀, 一般为branch或者PR
        type: string
    type: object
  models.DeployEnv:
    properties:
      env:
        type: string
      product_name:
        type: string
      type:
        type: string
    type: object
  models.DeployHelmChart:
    properties:
      chart_name:
        type: string
      chart_repo:
        type: string
      chart_version:
        type: string
      override_kvs:
        description: used for helm services, json-encoded string of kv value
        type: string
      release_name:
        type: string
      values_yaml:
        type: string
    type: object
  models.DeployModuleInfo:
    properties:
      image:
        type: string
      image_name:
        type: string
      service_module:
        type: string
    type: object
  models.DeployOptionInfo:
    properties:
      auto_sync:
        type: boolean
      deployed:
        type: boolean
      env_variable:
        $ref: '#/definitions/models.DeployVariableInfo'
      modules:
        items:
          $ref: '#/definitions/models.DeployModuleInfo'
        type: array
      service_name:
        type: string
      service_variable:
        $ref: '#/definitions/models.DeployVariableInfo'
      updatable:
        type: boolean
      update_config:
        type: boolean
    type: object
  models.DeployServiceInfo:
    properties:
      auto_sync:
        type: boolean
      deployed:
        type: boolean
      modules:
        items:
          $ref: '#/definitions/models.DeployModuleInfo'
        type: array
      override_kvs:
        description: used for helm services, json-encoded string of kv value
        type: string
      service_name:
        type: string
      updatable:
        type: boolean
      update_config:
        type: boolean
      value_merge_strategy:
        type: string
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      variable_yaml:
        description: final yaml for both helm and k8s service to deploy
        type: string
    type: object
  models.DeployServiceVariableConfig:
    properties:
      auto_sync:
        type: boolean
      deployed:
        type: boolean
      modules:
        items:
          $ref: '#/definitions/models.DeployModuleInfo'
        type: array
      service_name:
        type: string
      updatable:
        type: boolean
      update_config:
        type: boolean
      variable_configs:
        description: VariableConfigs used to determine if a variable is visible to
          the workflow user.
        items:
          $ref: '#/definitions/models.DeployVariableConfig'
        type: array
    type: object
  models.DeployTargets:
    properties:
      image:
        type: string
      image_name:
        type: string
      target:
        description: workload_type/workload_name/container_name.
        type: string
    type: object
  models.DeployVariableConfig:
    properties:
      source:
        type: string
      value:
        type: string
      variable_key:
        type: string
    type: object
  models.DeployVariableInfo:
    properties:
      override_kvs:
        description: used for helm services, json-encoded string of kv value
        type: string
      value_merge_strategy:
        type: string
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      variable_yaml:
        description: final yaml for both helm and k8s service to deploy
        type: string
    type: object
  models.DindCfg:
    properties:
      replicas:
        type: integer
      resources:
        $ref: '#/definitions/models.Resources'
      storage:
        $ref: '#/definitions/models.DindStorage'
      strategy_id:
        type: string
    type: object
  models.DindStorage:
    properties:
      storage_class:
        type: string
      storage_size_in_gib:
        type: integer
      type:
        $ref: '#/definitions/models.DindStorageType'
    type: object
  models.DindStorageType:
    enum:
    - rootfs
    - dynamic
    type: string
    x-enum-varnames:
    - DindStorageRootfs
    - DindStorageDynamic
  models.DingDingNotificationConfig:
    properties:
      at_mobiles:
        items:
          type: string
        type: array
      hook_address:
        type: string
      is_at_all:
        type: boolean
    type: object
  models.DingTalkApproval:
    properties:
      approval_id:
        description: 'ID: dintalk im app mongodb id'
        type: string
      approval_nodes:
        items:
          $ref: '#/definitions/models.DingTalkApprovalNode'
        type: array
      default_approval_initiator:
        allOf:
        - $ref: '#/definitions/models.DingTalkApprovalUser'
        description: DefaultApprovalInitiator if not set, use workflow task creator
          as approval initiator
      instance_code:
        description: 'InstanceCode: dingtalk approval instance code'
        type: string
      timeout:
        type: integer
    type: object
  models.DingTalkApprovalNode:
    properties:
      approve_users:
        items:
          $ref: '#/definitions/models.DingTalkApprovalUser'
        type: array
      reject_or_approve:
        $ref: '#/definitions/config.ApprovalStatus'
      type:
        type: string
    type: object
  models.DingTalkApprovalUser:
    properties:
      avatar:
        type: string
      comment:
        type: string
      id:
        type: string
      name:
        type: string
      operation_time:
        type: integer
      reject_or_approve:
        $ref: '#/definitions/config.ApprovalStatus'
    type: object
  models.DistributeTarget:
    properties:
      image_name:
        type: string
      service_module:
        type: string
      service_name:
        type: string
      source_image:
        type: string
      source_tag:
        type: string
      target_image:
        type: string
      target_tag:
        type: string
      update_tag:
        description: if UpdateTag was false, use SourceTag as TargetTag.
        type: boolean
    type: object
  models.DockerBuild:
    properties:
      build_args:
        description: BuildArgs docker build args
        type: string
      docker_file:
        description: DockerFile name, default is Dockerfile
        type: string
      source:
        description: Source whether dockerfile comes from template or existing file
        type: string
      template_id:
        description: TemplateId is the id of the template dockerfile
        type: string
      template_name:
        description: TemplateName is the name of the template dockerfile
        type: string
      work_dir:
        description: WorkDir docker run path
        type: string
    type: object
  models.EnvArgs:
    properties:
      env_name:
        type: string
      name:
        type: string
      product_name:
        type: string
      production:
        type: boolean
    type: object
  models.EnvConfig:
    properties:
      env_name:
        type: string
      host_ids:
        items:
          type: string
        type: array
      labels:
        items:
          type: string
        type: array
    type: object
  models.EnvStatus:
    properties:
      address:
        type: string
      env_name:
        type: string
      health_checks:
        $ref: '#/definitions/models.PmHealthCheck'
      host_id:
        type: string
      pm_info:
        $ref: '#/definitions/models.PmInfo'
      status:
        type: string
    type: object
  models.FileArchive:
    properties:
      file_location:
        type: string
    type: object
  models.GUIConfig:
    properties:
      deployment: {}
      ingress: {}
      service: {}
    type: object
  models.GeneralHook:
    properties:
      description:
        type: string
      enabled:
        type: boolean
      name:
        type: string
      workflow_arg:
        $ref: '#/definitions/models.WorkflowV4'
    type: object
  models.GrayReleaseJobSpec:
    properties:
      cluster_id:
        type: string
      deploy_timeout:
        description: unit is minute.
        type: integer
      docker_registry_id:
        type: string
      from_job:
        type: string
      gray_scale:
        type: integer
      namespace:
        type: string
      target_options:
        items:
          $ref: '#/definitions/models.GrayReleaseTarget'
        type: array
      targets:
        items:
          $ref: '#/definitions/models.GrayReleaseTarget'
        type: array
    type: object
  models.GrayReleaseTarget:
    properties:
      container_name:
        type: string
      image:
        type: string
      replica:
        type: integer
      workload_name:
        type: string
      workload_type:
        type: string
    type: object
  models.GrayRollbackJobSpec:
    properties:
      cluster_id:
        type: string
      cluster_source:
        type: string
      namespace:
        type: string
      rollback_timeout:
        description: unit is minute.
        type: integer
      target_options:
        items:
          $ref: '#/definitions/models.GrayRollbackTarget'
        type: array
      targets:
        items:
          $ref: '#/definitions/models.GrayRollbackTarget'
        type: array
    type: object
  models.GrayRollbackTarget:
    properties:
      origin_image:
        type: string
      origin_replica:
        type: integer
      workload_name:
        type: string
      workload_type:
        type: string
    type: object
  models.HelmChart:
    properties:
      name:
        type: string
      repo:
        type: string
      values_yaml:
        description: full values yaml in service
        type: string
      version:
        type: string
    type: object
  models.HelmRepo:
    properties:
      created_at:
        type: integer
      enable_proxy:
        type: boolean
      id:
        type: string
      password:
        type: string
      projects:
        items:
          type: string
        type: array
      repo_name:
        type: string
      update_by:
        type: string
      updated_at:
        type: integer
      url:
        type: string
      username:
        type: string
    type: object
  models.HookPayload:
    properties:
      branch:
        type: string
      check_run_id:
        type: integer
      codehost_id:
        type: integer
      commit_id:
        type: string
      delivery_id:
        type: string
      event_type:
        type: string
      is_pr:
        type: boolean
      merge_request_id:
        type: string
      owner:
        type: string
      ref:
        type: string
      repo:
        type: string
    type: object
  models.ImagePathSpec:
    properties:
      image:
        type: string
      namespace:
        type: string
      repo:
        type: string
      tag:
        type: string
    type: object
  models.IstioGrayscale:
    properties:
      base_env:
        type: string
      enable:
        type: boolean
      grayscale_strategy:
        type: string
      header_match_configs:
        items:
          $ref: '#/definitions/models.IstioHeaderMatchConfig'
        type: array
      is_base:
        type: boolean
      weight_configs:
        items:
          $ref: '#/definitions/models.IstioWeightConfig'
        type: array
    type: object
  models.IstioHeaderMatch:
    properties:
      key:
        type: string
      match:
        type: string
      value:
        type: string
    type: object
  models.IstioHeaderMatchConfig:
    properties:
      env:
        type: string
      header_match:
        items:
          $ref: '#/definitions/models.IstioHeaderMatch'
        type: array
    type: object
  models.IstioWeightConfig:
    properties:
      env:
        type: string
      weight:
        type: integer
    type: object
  models.Item:
    properties:
      name:
        type: string
      version:
        type: string
    type: object
  models.JenkinsBuild:
    properties:
      jenkins_build_params:
        items:
          $ref: '#/definitions/types.JenkinsBuildParam'
        type: array
      jenkins_id:
        type: string
      job_name:
        type: string
    type: object
  models.JenkinsBuildArgs:
    properties:
      jenkins_build_params:
        items:
          $ref: '#/definitions/types.JenkinsBuildParam'
        type: array
      job_name:
        type: string
    type: object
  models.JiraHook:
    properties:
      description:
        type: string
      enabled:
        type: boolean
      enabled_issue_status_change:
        type: boolean
      from_status:
        $ref: '#/definitions/models.JiraHookStatus'
      jira_id:
        type: string
      jira_system_identity:
        type: string
      jira_url:
        type: string
      name:
        type: string
      to_status:
        $ref: '#/definitions/models.JiraHookStatus'
      workflow_arg:
        $ref: '#/definitions/models.WorkflowV4'
    type: object
  models.JiraHookStatus:
    properties:
      id:
        type: string
      name:
        type: string
    type: object
  models.Job:
    properties:
      error_policy:
        $ref: '#/definitions/models.JobErrorPolicy'
      name:
        type: string
      run_policy:
        $ref: '#/definitions/config.JobRunPolicy'
      service_modules:
        items:
          $ref: '#/definitions/models.WorkflowServiceModule'
        type: array
      skipped:
        description: only for webhook workflow args to skip some tasks.
        type: boolean
      spec: {}
      type:
        $ref: '#/definitions/config.JobType'
    type: object
  models.JobErrorPolicy:
    properties:
      approval_users:
        items:
          $ref: '#/definitions/models.User'
        type: array
      maximum_retry:
        type: integer
      policy:
        $ref: '#/definitions/config.JobErrorPolicy'
    type: object
  models.JobTaskSQLSpec:
    properties:
      detail:
        type: string
      id:
        type: string
      results:
        items:
          $ref: '#/definitions/models.SQLExecResult'
        type: array
      sql:
        type: string
      type:
        $ref: '#/definitions/config.DBInstanceType'
    type: object
  models.K8SCluster:
    properties:
      advanced_config:
        $ref: '#/definitions/models.AdvancedConfig'
      cache:
        $ref: '#/definitions/types.Cache'
      createdAt:
        type: integer
      createdBy:
        type: string
      description:
        type: string
      dind_cfg:
        $ref: '#/definitions/models.DindCfg'
      error:
        type: string
      id:
        type: string
      info:
        $ref: '#/definitions/models.K8SClusterInfo'
      kube_config:
        type: string
      last_connection_time:
        type: integer
      local:
        type: boolean
      name:
        type: string
      namespace:
        description: Deprecated field, it should be deleted in version 1.15 since
          no more namespace settings is used
        type: string
      production:
        type: boolean
      provider:
        type: integer
      share_storage:
        $ref: '#/definitions/types.ShareStorage'
      status:
        $ref: '#/definitions/setting.K8SClusterStatus'
      tags:
        items:
          type: string
        type: array
      token:
        type: string
      type:
        description: new field in 1.14, intended to enable kubeconfig for cluster
          management
        type: string
      update_hubagent_error_msg:
        type: string
      yaml:
        type: string
    type: object
  models.K8SClusterInfo:
    properties:
      cpu:
        type: string
      memory:
        type: string
      nodes:
        type: integer
      version:
        type: string
    type: object
  models.K8sPatchJobSpec:
    properties:
      cluster_id:
        type: string
      cluster_source:
        type: string
      namespace:
        type: string
      patch_item_options:
        items:
          $ref: '#/definitions/models.PatchItem'
        type: array
      patch_items:
        items:
          $ref: '#/definitions/models.PatchItem'
        type: array
    type: object
  models.KeyVal:
    properties:
      call_function:
        type: string
      choice_option:
        items:
          type: string
        type: array
      choice_value:
        items:
          type: string
        type: array
      description:
        type: string
      function_reference:
        items:
          type: string
        type: array
      is_credential:
        type: boolean
      key:
        type: string
      registry_id:
        type: string
      script:
        type: string
      type:
        $ref: '#/definitions/models.ParameterSettingType'
      value:
        type: string
    type: object
  models.LLMIntegration:
    properties:
      base_url:
        type: string
      enable_proxy:
        type: boolean
      id:
        type: string
      is_default:
        type: boolean
      model:
        type: string
      provider_name:
        $ref: '#/definitions/llm.Provider'
      token:
        type: string
      update_time:
        type: integer
      updated_by:
        type: string
    type: object
  models.Label:
    properties:
      created_at:
        type: integer
      description:
        type: string
      id:
        type: string
      key:
        type: string
      updated_at:
        type: integer
    type: object
  models.LarkApproval:
    properties:
      approval_id:
        description: 'ID: lark im app mongodb id'
        type: string
      approval_nodes:
        items:
          $ref: '#/definitions/models.LarkApprovalNode'
        type: array
      approve_users:
        description: 'Deprecated: use ApprovalNodes instead'
        items:
          $ref: '#/definitions/models.LarkApprovalUser'
        type: array
      default_approval_initiator:
        allOf:
        - $ref: '#/definitions/models.LarkApprovalUser'
        description: DefaultApprovalInitiator if not set, use workflow task creator
          as approval initiator
      instance_code:
        description: 'InstanceCode: lark approval instance code'
        type: string
      timeout:
        type: integer
    type: object
  models.LarkApprovalGroup:
    properties:
      group_id:
        type: string
      group_name:
        type: string
    type: object
  models.LarkApprovalNode:
    properties:
      approve_groups:
        items:
          $ref: '#/definitions/models.LarkApprovalGroup'
        type: array
      approve_node_type:
        $ref: '#/definitions/lark.ApproveNodeType'
      approve_users:
        items:
          $ref: '#/definitions/models.LarkApprovalUser'
        type: array
      reject_or_approve:
        $ref: '#/definitions/config.ApprovalStatus'
      type:
        $ref: '#/definitions/lark.ApproveType'
    type: object
  models.LarkApprovalUser:
    properties:
      avatar:
        type: string
      comment:
        type: string
      id:
        type: string
      id_type:
        type: string
      is_executor:
        description: IsExecutor marks if the user is the executor of the workflow
        type: boolean
      name:
        type: string
      operation_time:
        type: integer
      reject_or_approve:
        $ref: '#/definitions/config.ApprovalStatus'
    type: object
  models.LarkChat:
    properties:
      chat_id:
        type: string
      chat_name:
        type: string
    type: object
  models.LarkGroupNotificationConfig:
    properties:
      app_id:
        type: string
      at_users:
        items:
          $ref: '#/definitions/lark.UserInfo'
        type: array
      chat:
        $ref: '#/definitions/models.LarkChat'
      is_at_all:
        type: boolean
    type: object
  models.LarkHookNotificationConfig:
    properties:
      at_users:
        items:
          type: string
        type: array
      hook_address:
        type: string
      is_at_all:
        type: boolean
    type: object
  models.LarkPersonNotificationConfig:
    properties:
      app_id:
        type: string
      target_users:
        items:
          $ref: '#/definitions/lark.UserInfo'
        type: array
    type: object
  models.Limits:
    properties:
      cpu:
        type: integer
      memory:
        type: integer
    type: object
  models.MSTeamsNotificationConfig:
    properties:
      at_emails:
        items:
          type: string
        type: array
      hook_address:
        type: string
    type: object
  models.MailNotificationConfig:
    properties:
      target_users:
        items:
          $ref: '#/definitions/models.User'
        type: array
    type: object
  models.MainHookRepo:
    properties:
      branch:
        type: string
      codehost_id:
        type: integer
      committer:
        type: string
      description:
        type: string
      events:
        items:
          $ref: '#/definitions/config.HookEventType'
        type: array
      is_regular:
        type: boolean
      label:
        type: string
      match_folders:
        items:
          type: string
        type: array
      name:
        type: string
      repo_name:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      revision:
        type: string
      source:
        type: string
      tag:
        type: string
    type: object
  models.ManualExec:
    properties:
      enabled:
        type: boolean
      excuted:
        type: boolean
      manual_exec_users:
        items:
          $ref: '#/definitions/models.User'
        type: array
      manual_exector_id:
        type: string
      manual_exector_name:
        type: string
      modify_params:
        type: boolean
    type: object
  models.MeegoHook:
    properties:
      description:
        type: string
      enabled:
        type: boolean
      meego_id:
        type: string
      meego_system_identity:
        type: string
      meego_url:
        type: string
      name:
        type: string
      workflow_arg:
        $ref: '#/definitions/models.WorkflowV4'
    type: object
  models.MonthlyReleaseStat:
    properties:
      average_approval_duration:
        type: number
      average_execution_duration:
        type: number
      create_time:
        type: integer
      date:
        type: string
      total:
        type: integer
      update_time:
        type: integer
    type: object
  models.NativeApproval:
    properties:
      approve_users:
        items:
          $ref: '#/definitions/models.User'
        type: array
      flat_approve_users:
        items:
          $ref: '#/definitions/models.User'
        type: array
      instance_code:
        description: 'InstanceCode: native approval instance code, save for working
          after restart aslan'
        type: string
      needed_approvers:
        type: integer
      reject_or_approve:
        $ref: '#/definitions/config.ApprovalStatus'
      timeout:
        type: integer
    type: object
  models.NodeSelectorRequirement:
    properties:
      key:
        type: string
      operator:
        $ref: '#/definitions/v1.NodeSelectorOperator'
      value:
        items:
          type: string
        type: array
    type: object
  models.NotificationConfig:
    properties:
      events:
        items:
          $ref: '#/definitions/models.NotificationEvent'
        type: array
      webhook_type:
        $ref: '#/definitions/models.WebHookType'
      webhook_url:
        type: string
    type: object
  models.NotificationEvent:
    enum:
    - notification_event_analyzer_normal
    - notification_event_analyzer_abnormal
    type: string
    x-enum-varnames:
    - NotificationEventAnalyzerNoraml
    - NotificationEventAnalyzerAbnormal
  models.NotifyCtl:
    properties:
      at_mobiles:
        items:
          type: string
        type: array
      dingding_notification_config:
        $ref: '#/definitions/models.DingDingNotificationConfig'
      dingding_webhook:
        type: string
      enabled:
        type: boolean
      feishu_app_id:
        type: string
      feishu_chat:
        $ref: '#/definitions/models.LarkChat'
      feishu_webhook:
        type: string
      is_at_all:
        type: boolean
      lark_at_users:
        items:
          $ref: '#/definitions/lark.UserInfo'
        type: array
      lark_group_notification_config:
        $ref: '#/definitions/models.LarkGroupNotificationConfig'
      lark_hook_notification_config:
        $ref: '#/definitions/models.LarkHookNotificationConfig'
      lark_person_notification_config:
        $ref: '#/definitions/models.LarkPersonNotificationConfig'
      lark_user_ids:
        items:
          type: string
        type: array
      mail_notification_config:
        $ref: '#/definitions/models.MailNotificationConfig'
      mail_users:
        items:
          $ref: '#/definitions/models.User'
        type: array
      msteams_notification_config:
        $ref: '#/definitions/models.MSTeamsNotificationConfig'
      notify_type:
        items:
          type: string
        type: array
      weChat_webHook:
        description: |-
          below is the deprecated field. the value of those will be empty if the data is created after version 3.3.0. These
          field will only be used for data compatibility. USE WITH CAUTION!!!
        type: string
      webhook_notification_config:
        $ref: '#/definitions/models.WebhookNotificationConfig'
      webhook_notify:
        $ref: '#/definitions/models.WebhookNotificationConfig'
      webhook_type:
        $ref: '#/definitions/setting.NotifyWebHookType'
      wechat_notification_config:
        $ref: '#/definitions/models.WechatNotificationConfig'
      wechat_user_ids:
        items:
          type: string
        type: array
    type: object
  models.ObjectStorageUpload:
    properties:
      enabled:
        type: boolean
      object_storage_id:
        type: string
      upload_detail:
        items:
          $ref: '#/definitions/types.ObjectStoragePathDetail'
        type: array
    type: object
  models.Output:
    properties:
      description:
        type: string
      name:
        type: string
    type: object
  models.Param:
    properties:
      choice_option:
        items:
          type: string
        type: array
      choice_value:
        items:
          type: string
        type: array
      default:
        type: string
      description:
        type: string
      is_credential:
        type: boolean
      name:
        type: string
      repo:
        $ref: '#/definitions/types.Repository'
      source:
        type: string
      type:
        description: support string/text/choice/repo type
        type: string
      value:
        type: string
    type: object
  models.ParamVal:
    properties:
      target:
        type: string
      value:
        type: string
    type: object
  models.Parameter:
    properties:
      default_value:
        type: string
      name:
        type: string
      param_val:
        items:
          $ref: '#/definitions/models.ParamVal'
        type: array
    type: object
  models.ParameterSettingType:
    enum:
    - string
    - choice
    - multi-select
    - image
    - script
    - external
    type: string
    x-enum-varnames:
    - StringType
    - ChoiceType
    - MultiSelectType
    - ImageType
    - Script
    - ExternalType
  models.PatchItem:
    properties:
      params:
        items:
          $ref: '#/definitions/models.Param'
        type: array
      patch_content:
        type: string
      patch_strategy:
        description: support strategic-merge/merge/json
        type: string
      resource_group:
        type: string
      resource_kind:
        type: string
      resource_name:
        type: string
      resource_version:
        type: string
    type: object
  models.PmHealthCheck:
    properties:
      current_healthy_num:
        type: integer
      current_unhealthy_num:
        type: integer
      healthy_threshold:
        type: integer
      interval:
        type: integer
      path:
        type: string
      port:
        type: integer
      protocol:
        type: string
      time_out:
        type: integer
      unhealthy_threshold:
        type: integer
    type: object
  models.PmInfo:
    properties:
      id:
        type: string
      ip:
        type: string
      is_prod:
        type: boolean
      label:
        type: string
      name:
        type: string
      port:
        type: integer
      provider:
        type: integer
      status:
        $ref: '#/definitions/setting.PMHostStatus'
    type: object
  models.PostBuild:
    properties:
      docker_build:
        $ref: '#/definitions/models.DockerBuild'
      file_archive:
        $ref: '#/definitions/models.FileArchive'
      object_storage_upload:
        $ref: '#/definitions/models.ObjectStorageUpload'
      scripts:
        type: string
    type: object
  models.PreBuild:
    properties:
      build_os:
        description: BuildOS defines job image OS, it supports 18.04 and 20.04
        type: string
      clean_workspace:
        description: 'TODO: Deprecated.'
        type: boolean
      cluster_id:
        type: string
      cluster_source:
        type: string
      custom_annotations:
        items:
          $ref: '#/definitions/util.KeyValue'
        type: array
      custom_labels:
        items:
          $ref: '#/definitions/util.KeyValue'
        type: array
      enable_proxy:
        description: EnableProxy
        type: boolean
      envs:
        description: Envs stores user defined env key val for build
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      image_from:
        type: string
      image_id:
        type: string
      installs:
        description: Installs defines apps to be installed for build
        items:
          $ref: '#/definitions/models.Item'
        type: array
      namespace:
        description: 'TODO: Deprecated.'
        type: string
      parameters:
        description: Parameters
        items:
          $ref: '#/definitions/models.Parameter'
        type: array
      res_req:
        allOf:
        - $ref: '#/definitions/setting.Request'
        description: ResReq defines job requested resources
      res_req_spec:
        $ref: '#/definitions/setting.RequestSpec'
      strategy_id:
        type: string
      upload_pkg:
        description: UploadPkg uploads package to s3
        type: boolean
      use_host_docker_daemon:
        description: UseHostDockerDaemon determines is dockerDaemon on host node is
          used in pod
        type: boolean
    type: object
  models.PreDeploy:
    properties:
      build_os:
        type: string
      image_from:
        type: string
      image_id:
        type: string
      installs:
        items:
          $ref: '#/definitions/models.Item'
        type: array
    type: object
  models.ProductService:
    properties:
      containers:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      deploy_strategy:
        type: string
      env_configs:
        items:
          $ref: '#/definitions/models.EnvConfig'
        type: array
      error:
        type: string
      product_name:
        type: string
      release_name:
        type: string
      render:
        allOf:
        - $ref: '#/definitions/template.ServiceRender'
        description: New since 1.9.0 used to replace service renders in render_set
      rendered_yaml:
        type: string
      resources:
        items:
          $ref: '#/definitions/models.ServiceResource'
        type: array
      revision:
        type: integer
      service_name:
        type: string
      type:
        type: string
      updatable:
        type: boolean
      update_time:
        type: integer
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      variable_yaml:
        type: string
    type: object
  models.ProductShareEnv:
    properties:
      base_env:
        type: string
      enable:
        type: boolean
      is_base:
        type: boolean
    type: object
  models.ProjectDetail:
    properties:
      project_deploy_type:
        type: string
      project_key:
        type: string
      project_name:
        type: string
    type: object
  models.ProjectManagement:
    properties:
      id:
        type: string
      jira_auth_type:
        $ref: '#/definitions/config.JiraAuthType'
      jira_host:
        type: string
      jira_personal_access_token:
        description: JiraPersonalAccessToken is used for bearer token
        type: string
      jira_token:
        description: JiraToken is used in place of password for basic auth with username
        type: string
      jira_user:
        type: string
      meego_host:
        type: string
      meego_plugin_id:
        type: string
      meego_plugin_secret:
        type: string
      meego_user_key:
        type: string
      system_identity:
        type: string
      type:
        type: string
      updated_at:
        type: integer
    type: object
  models.RegistryAdvancedSetting:
    properties:
      enable_tls:
        description: New field since v1.11 to support self-signed TLS certificate
        type: boolean
      modified:
        description: |-
          indicator to make sure if the advanced has been modified
          This is solely a field for frontend display and not used anywhere else in the backend system
          TODO: whether this field should exist needs a discussion
        type: boolean
      tls_cert:
        type: string
    type: object
  models.RegistryNamespace:
    properties:
      access_key:
        type: string
      advanced_setting:
        $ref: '#/definitions/models.RegistryAdvancedSetting'
      id:
        type: string
      is_default:
        type: boolean
      namespace:
        description: |-
          Namespace is NOT a required field, this could be empty when the registry is AWS ECR or so.
          use with CAUTION !!!!
        type: string
      projects:
        items:
          type: string
        type: array
      reg_addr:
        type: string
      reg_provider:
        type: string
      reg_type:
        type: string
      region:
        type: string
      secret_key:
        type: string
      update_by:
        type: string
      update_time:
        type: integer
    type: object
  models.ReleaseImage:
    properties:
      image:
        type: string
      service_module:
        type: string
      service_name:
        type: string
    type: object
  models.ReleaseJob:
    properties:
      executed_by:
        type: string
      executed_time:
        type: integer
      id:
        type: string
      last_status:
        allOf:
        - $ref: '#/definitions/config.ReleasePlanJobStatus'
        description: |-
          ReleasePlan can return to PlanningStatus when some release jobs have been executed
          So we need to record the last status of the release job
      name:
        type: string
      spec: {}
      status:
        $ref: '#/definitions/config.ReleasePlanJobStatus'
      type:
        $ref: '#/definitions/config.ReleasePlanJobType'
      updated:
        description: Updated is used to indicate whether the release job has been
          updated
        type: boolean
    type: object
  models.ReleasePlan:
    properties:
      approval:
        $ref: '#/definitions/models.Approval'
      approval_time:
        type: integer
      create_time:
        type: integer
      created_by:
        type: string
      description:
        type: string
      end_time:
        type: integer
      executing_time:
        type: integer
      id:
        type: string
      index:
        type: integer
      jira_sprint_association:
        $ref: '#/definitions/models.ReleasePlanJiraSprintAssociation'
      jobs:
        items:
          $ref: '#/definitions/models.ReleaseJob'
        type: array
      manager:
        type: string
      manager_id:
        description: ManagerID is the user id of the manager
        type: string
      name:
        type: string
      planning_time:
        type: integer
      schedule_execute_time:
        type: integer
      start_time:
        type: integer
      status:
        $ref: '#/definitions/config.ReleasePlanStatus'
      success_time:
        type: integer
      update_time:
        type: integer
      updated_by:
        type: string
    type: object
  models.ReleasePlanArgs:
    properties:
      id:
        type: string
      index:
        type: integer
      name:
        type: string
    type: object
  models.ReleasePlanJiraSprint:
    properties:
      board_id:
        type: integer
      project_key:
        type: string
      project_name:
        type: string
      sprint_id:
        type: integer
      sprint_name:
        type: string
    type: object
  models.ReleasePlanJiraSprintAssociation:
    properties:
      jira_id:
        type: string
      sprints:
        items:
          $ref: '#/definitions/models.ReleasePlanJiraSprint'
        type: array
    type: object
  models.ReleasePlanTemplate:
    properties:
      approval:
        $ref: '#/definitions/models.Approval'
      create_time:
        type: integer
      created_by:
        type: string
      id:
        type: string
      jobs:
        items:
          $ref: '#/definitions/models.ReleaseJob'
        type: array
      template_name:
        type: string
      update_time:
        type: integer
      updated_by:
        type: string
    type: object
  models.RenderInfo:
    properties:
      description:
        type: string
      name:
        type: string
      product_tmpl:
        type: string
      revision:
        type: integer
    type: object
  models.ResourceType:
    enum:
    - Pod
    - Deployment
    - ReplicaSet
    - PersistentVolumeClaim
    - Service
    - Ingress
    - StatefulSet
    - CronJob
    - HorizontalPodAutoScaler
    - PodDisruptionBudget
    - NetworkPolicy
    type: string
    x-enum-varnames:
    - ResourceTypePod
    - ResourceTypeDeployment
    - ResourceTypeReplicaSet
    - ResourceTypePVC
    - ResourceTypeService
    - ResourceTypeIngress
    - ResourceTypeStatefulSet
    - ResourceTypeCronJob
    - ResourceTypeHPA
    - ResourceTypePDB
    - ResourceTypeNetworkPolicy
  models.Resources:
    properties:
      limits:
        $ref: '#/definitions/models.Limits'
    type: object
  models.RuntimeKeyVal:
    properties:
      call_function:
        type: string
      choice_option:
        items:
          type: string
        type: array
      choice_value:
        items:
          type: string
        type: array
      description:
        type: string
      function_reference:
        items:
          type: string
        type: array
      is_credential:
        type: boolean
      key:
        type: string
      registry_id:
        type: string
      script:
        type: string
      source:
        type: string
      type:
        $ref: '#/definitions/models.ParameterSettingType'
      value:
        type: string
    type: object
  models.S3Storage:
    properties:
      ak:
        type: string
      bucket:
        type: string
      endpoint:
        type: string
      id:
        type: string
      insecure:
        type: boolean
      is_default:
        type: boolean
      projects:
        items:
          type: string
        type: array
      provider:
        type: integer
      region:
        type: string
      sk:
        type: string
      subfolder:
        type: string
      update_time:
        type: integer
      updated_by:
        type: string
    type: object
  models.SAE:
    properties:
      access_key_id:
        type: string
      access_key_secret:
        type: string
      created_at:
        type: integer
      id:
        type: string
      name:
        type: string
      update_by:
        type: string
      updated_at:
        type: integer
    type: object
  models.SAEApplication:
    properties:
      app_id:
        type: string
      app_name:
        type: string
      cpu:
        type: integer
      image_url:
        type: string
      instances:
        type: integer
      mem:
        type: integer
      package_url:
        type: string
      running_instances:
        type: integer
      service_module:
        type: string
      service_name:
        type: string
      tags:
        items:
          $ref: '#/definitions/models.SAETag'
        type: array
    type: object
  models.SAEEnv:
    properties:
      applications:
        items:
          $ref: '#/definitions/models.SAEApplication'
        type: array
      create_time:
        type: integer
      env_name:
        type: string
      id:
        type: string
      namespace_id:
        type: string
      namespace_name:
        type: string
      production:
        type: boolean
      project_name:
        type: string
      region_id:
        type: string
      update_by:
        type: string
      update_time:
        type: integer
    type: object
  models.SAETag:
    properties:
      key:
        type: string
      value:
        type: string
    type: object
  models.SQLExecResult:
    properties:
      elapsed_time:
        type: integer
      rows_affected:
        type: integer
      sql:
        type: string
      status:
        $ref: '#/definitions/setting.SQLExecStatus'
    type: object
  models.ScanningAdvancedSetting:
    properties:
      artifact_paths:
        items:
          type: string
        type: array
      cache:
        $ref: '#/definitions/models.ScanningCacheSetting'
      cluster_id:
        type: string
      cluster_source:
        type: string
      concurrency_limit:
        type: integer
      custom_annotations:
        items:
          $ref: '#/definitions/util.KeyValue'
        type: array
      custom_labels:
        items:
          $ref: '#/definitions/util.KeyValue'
        type: array
      hook_ctl:
        $ref: '#/definitions/models.ScanningHookCtl'
      notify_ctls:
        items:
          $ref: '#/definitions/models.NotifyCtl'
        type: array
      res_req:
        $ref: '#/definitions/setting.Request'
      res_req_spec:
        $ref: '#/definitions/setting.RequestSpec'
      strategy_id:
        type: string
      timeout:
        type: integer
    type: object
  models.ScanningCacheSetting:
    properties:
      cache_dir_type:
        $ref: '#/definitions/types.CacheDirType'
      cache_enable:
        type: boolean
      cache_user_dir:
        type: string
    type: object
  models.ScanningHook:
    properties:
      auto_cancel:
        type: boolean
      branch:
        type: string
      codehost_id:
        type: integer
      events:
        items:
          $ref: '#/definitions/config.HookEventType'
        type: array
      is_manual:
        type: boolean
      is_regular:
        type: boolean
      match_folders:
        items:
          type: string
        type: array
      repo_name:
        type: string
      repo_owner:
        type: string
      source:
        type: string
    type: object
  models.ScanningHookCtl:
    properties:
      enabled:
        type: boolean
      items:
        items:
          $ref: '#/definitions/models.ScanningHook'
        type: array
    type: object
  models.ScanningModule:
    properties:
      key_vals:
        items:
          $ref: '#/definitions/models.RuntimeKeyVal'
        type: array
      name:
        type: string
      project_name:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      share_storage_info:
        $ref: '#/definitions/models.ShareStorageInfo'
    type: object
  models.ScanningTemplate:
    properties:
      advanced_settings:
        $ref: '#/definitions/models.ScanningAdvancedSetting'
      check_quality_gate:
        type: boolean
      created_at:
        type: integer
      enable_scanner:
        description: EnableScanner indicates whether user uses sonar scanner instead
          of the script
        type: boolean
      envs:
        description: Envs is the user defined key/values
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      id:
        type: string
      image_id:
        type: string
      infrastructure:
        type: string
      installs:
        items:
          $ref: '#/definitions/models.Item'
        type: array
      name:
        type: string
      parameter:
        description: Parameter is for sonarQube type only
        type: string
      scanner_type:
        type: string
      script:
        type: string
      script_type:
        allOf:
        - $ref: '#/definitions/types.ScriptType'
        description: Script is for other type only
      sonar_id:
        type: string
      updated_at:
        type: integer
      updated_by:
        type: string
      vm_labels:
        items:
          type: string
        type: array
    type: object
  models.Schedule:
    properties:
      cron:
        type: string
      enabled:
        description: 自由编排工作流的开关是放在schedule里面的
        type: boolean
      env_analysis_args:
        $ref: '#/definitions/models.EnvArgs'
      env_args:
        $ref: '#/definitions/models.EnvArgs'
      frequency:
        type: string
      id:
        type: string
      max_failures:
        type: integer
      number:
        type: integer
      release_plan_args:
        $ref: '#/definitions/models.ReleasePlanArgs'
      task_args:
        $ref: '#/definitions/models.TaskArgs'
      test_args:
        $ref: '#/definitions/models.TestTaskArgs'
      time:
        type: string
      type:
        $ref: '#/definitions/config.ScheduleType'
      unix_stamp:
        type: integer
      workflow_args:
        $ref: '#/definitions/models.WorkflowTaskArgs'
      workflow_v4_args:
        $ref: '#/definitions/models.WorkflowV4'
    type: object
  models.ScheduleCtrl:
    properties:
      enabled:
        type: boolean
      items:
        items:
          $ref: '#/definitions/models.Schedule'
        type: array
    type: object
  models.ScheduleStrategy:
    properties:
      default:
        type: boolean
      node_labels:
        items:
          $ref: '#/definitions/models.NodeSelectorRequirement'
        type: array
      strategy:
        type: string
      strategy_id:
        type: string
      strategy_name:
        type: string
      tolerations:
        type: string
    type: object
  models.Service:
    properties:
      auto_sync:
        type: boolean
      branch_name:
        type: string
      build_name:
        type: string
      codehost_id:
        type: integer
      commit:
        $ref: '#/definitions/models.Commit'
      containers:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      create_by:
        type: string
      create_from: {}
      create_time:
        type: integer
      deploy_time:
        type: integer
      description:
        type: string
      env_configs:
        items:
          $ref: '#/definitions/models.EnvConfig'
        type: array
      env_name:
        type: string
      env_statuses:
        items:
          $ref: '#/definitions/models.EnvStatus'
        type: array
      gerrit_branch_name:
        type: string
      gerrit_codeHost_id:
        type: integer
      gerrit_path:
        type: string
      gerrit_remote_name:
        type: string
      gerrit_repo_name:
        type: string
      gitee_path:
        type: string
      gui_config:
        $ref: '#/definitions/models.GUIConfig'
      hash256:
        type: string
      health_checks:
        items:
          $ref: '#/definitions/models.PmHealthCheck'
        type: array
      helm_chart:
        $ref: '#/definitions/models.HelmChart'
      is_dir:
        type: boolean
      load_path:
        type: string
      product_name:
        type: string
      release_naming:
        type: string
      repo_name:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      repo_uuid:
        type: string
      restart_cmd:
        type: string
      revision:
        type: integer
      service_name:
        type: string
      service_variable_kvs:
        description: New since 1.18.0, stores the variable kvs of k8s services
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      service_vars:
        description: DEPRECATED, New since 1.16.0, stores keys in variables which
          can be set in env
        items:
          type: string
        type: array
      source:
        type: string
      src_path:
        type: string
      start_cmd:
        type: string
      status:
        type: string
      stop_cmd:
        type: string
      team:
        type: string
      template_id:
        type: string
      type:
        type: string
      variable_yaml:
        description: New since 1.16.0, stores the variable yaml of k8s services
        type: string
      visibility:
        description: DEPRECATED since 1.17.0
        type: string
      workload_type:
        description: WorkloadType is set in host projects
        type: string
      yaml:
        type: string
    type: object
  models.ServiceAndBuild:
    properties:
      build_name:
        type: string
      image:
        type: string
      image_name:
        type: string
      key_vals:
        items:
          $ref: '#/definitions/models.RuntimeKeyVal'
        type: array
      package:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      service_module:
        type: string
      service_name:
        type: string
      share_storage_info:
        $ref: '#/definitions/models.ShareStorageInfo'
    type: object
  models.ServiceAndImage:
    properties:
      image:
        type: string
      image_name:
        type: string
      service_module:
        type: string
      service_name:
        type: string
    type: object
  models.ServiceAndScannings:
    properties:
      key_vals:
        items:
          $ref: '#/definitions/models.RuntimeKeyVal'
        type: array
      name:
        type: string
      project_name:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      service_module:
        type: string
      service_name:
        type: string
      share_storage_info:
        $ref: '#/definitions/models.ShareStorageInfo'
    type: object
  models.ServiceAndVMDeploy:
    properties:
      artifact_url:
        type: string
      file_name:
        type: string
      image:
        type: string
      job_task_name:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      service_module:
        type: string
      service_name:
        type: string
      task_id:
        type: integer
      workflow_name:
        type: string
      workflow_type:
        $ref: '#/definitions/config.PipelineType'
    type: object
  models.ServiceModuleTarget:
    properties:
      build_name:
        type: string
      envs:
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      product_name:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      service_module:
        type: string
      service_name:
        type: string
    type: object
  models.ServiceModuleTargetBase:
    properties:
      product_name:
        type: string
      service_module:
        type: string
      service_name:
        type: string
    type: object
  models.ServiceResource:
    properties:
      group:
        type: string
      kind:
        type: string
      name:
        type: string
      version:
        type: string
    type: object
  models.ServiceTestTarget:
    properties:
      service_module:
        type: string
      service_name:
        type: string
    type: object
  models.ShareStorage:
    properties:
      name:
        type: string
      path:
        type: string
    type: object
  models.ShareStorageInfo:
    properties:
      enabled:
        type: boolean
      share_storages:
        items:
          $ref: '#/definitions/models.ShareStorage'
        type: array
    type: object
  models.Sprint:
    properties:
      create_time:
        type: integer
      created_by:
        $ref: '#/definitions/types.UserBriefInfo'
      id:
        type: string
      is_archived:
        type: boolean
      key:
        type: string
      key_initials:
        type: string
      name:
        type: string
      project_name:
        type: string
      stages:
        items:
          $ref: '#/definitions/models.SprintStage'
        type: array
      template_id:
        type: string
      update_time:
        type: integer
      updated_by:
        $ref: '#/definitions/types.UserBriefInfo'
    type: object
  models.SprintStage:
    properties:
      id:
        type: string
      name:
        type: string
      workflows:
        items:
          $ref: '#/definitions/models.SprintWorkflow'
        type: array
      workitem_ids:
        items:
          type: string
        type: array
    type: object
  models.SprintStageTemplate:
    properties:
      id:
        type: string
      name:
        type: string
      workflows:
        items:
          $ref: '#/definitions/models.SprintWorkflow'
        type: array
    type: object
  models.SprintTemplate:
    properties:
      create_time:
        type: integer
      created_by:
        $ref: '#/definitions/types.UserBriefInfo'
      id:
        type: string
      key:
        type: string
      key_initials:
        type: string
      name:
        type: string
      project_name:
        type: string
      stages:
        items:
          $ref: '#/definitions/models.SprintStageTemplate'
        type: array
      update_time:
        type: integer
      updated_by:
        $ref: '#/definitions/types.UserBriefInfo'
    type: object
  models.SprintWorkItem:
    properties:
      create_time:
        type: integer
      description:
        type: string
      id:
        type: string
      owners:
        items:
          $ref: '#/definitions/types.UserBriefInfo'
        type: array
      sprint_id:
        type: string
      stage_id:
        type: string
      title:
        type: string
      update_time:
        type: integer
    type: object
  models.SprintWorkItemActivity:
    properties:
      create_time:
        type: integer
      event:
        type: string
      id:
        type: string
      sprint_workitem_id:
        type: string
      type:
        $ref: '#/definitions/setting.SprintWorkItemActivityType'
      update_time:
        type: integer
      user:
        $ref: '#/definitions/types.UserBriefInfo'
    type: object
  models.SprintWorkItemTask:
    properties:
      create_time:
        type: integer
      creator:
        $ref: '#/definitions/types.UserBriefInfo'
      end_time:
        type: integer
      hash:
        type: string
      id:
        type: string
      service_module_datas:
        items:
          $ref: '#/definitions/models.WorkflowServiceModule'
        type: array
      sprint_workitem_ids:
        items:
          type: string
        type: array
      start_time:
        type: integer
      status:
        $ref: '#/definitions/config.Status'
      workflow_name:
        type: string
      workflow_task_id:
        type: integer
    type: object
  models.SprintWorkflow:
    properties:
      disabled:
        type: boolean
      display_name:
        type: string
      is_deleted:
        type: boolean
      name:
        type: string
    type: object
  models.TarInfo:
    properties:
      file_name:
        type: string
      job_task_name:
        type: string
      name:
        type: string
      task_id:
        type: integer
      url:
        type: string
      workflow_name:
        type: string
      workflow_type:
        type: string
    type: object
  models.TargetArgs:
    properties:
      bin_file:
        type: string
      build:
        $ref: '#/definitions/models.BuildArgs'
      build_name:
        type: string
      deploy:
        items:
          $ref: '#/definitions/models.DeployEnv'
        type: array
      envs:
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      has_build:
        type: boolean
      image:
        type: string
      image_name:
        type: string
      jenkins_build_args:
        $ref: '#/definitions/models.JenkinsBuildArgs'
      name:
        type: string
      product_name:
        type: string
      service_name:
        type: string
      service_type:
        type: string
    type: object
  models.TargetRepo:
    properties:
      envs:
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      service:
        $ref: '#/definitions/models.ServiceModuleTargetBase'
    type: object
  models.TaskArgs:
    properties:
      build_args:
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      builds:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      codehost_id:
        type: integer
      deploy:
        $ref: '#/definitions/models.DeployArgs'
      hook_payload:
        $ref: '#/definitions/models.HookPayload'
      is_qiniu:
        type: boolean
      notification_id:
        type: string
      pipeline_name:
        type: string
      product_name:
        type: string
      req_id:
        type: string
      task_creator:
        type: string
      test:
        $ref: '#/definitions/models.TestArgs'
    type: object
  models.TestArgs:
    properties:
      builds:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      envs:
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      namespace:
        type: string
      test_module_name:
        type: string
    type: object
  models.TestTaskArgs:
    properties:
      branch:
        type: string
      codehost_id:
        type: integer
      commit_id:
        type: string
      event_type:
        type: string
      hook_payload:
        $ref: '#/definitions/models.HookPayload'
      merge_request_id:
        description: webhook触发测试任务时，触发任务的repo、prID和commitID
        type: string
      notification_id:
        type: string
      product_name:
        type: string
      ref:
        type: string
      repo_name:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      req_id:
        type: string
      source:
        type: string
      test_name:
        type: string
      test_task_creator:
        type: string
    type: object
  models.UpdateEnvIstioConfigJobSpec:
    properties:
      base_env:
        type: string
      grayscale_strategy:
        type: string
      header_match_configs:
        items:
          $ref: '#/definitions/models.IstioHeaderMatchConfig'
        type: array
      production:
        type: boolean
      source:
        type: string
      weight_configs:
        items:
          $ref: '#/definitions/models.IstioWeightConfig'
        type: array
    type: object
  models.User:
    properties:
      comment:
        type: string
      group_id:
        type: string
      group_name:
        type: string
      operation_time:
        type: integer
      reject_or_approve:
        $ref: '#/definitions/config.ApprovalStatus'
      type:
        type: string
      user_id:
        type: string
      user_name:
        type: string
    type: object
  models.VersionArgs:
    properties:
      desc:
        type: string
      enabled:
        type: boolean
      labels:
        items:
          type: string
        type: array
      version:
        type: string
    type: object
  models.WebHookType:
    enum:
    - feishu
    - dingding
    - wechat
    type: string
    x-enum-varnames:
    - WebHookTypeFeishu
    - WebHookTypeDingding
    - WebHookTypeWeChat
  models.WebhookNotificationConfig:
    properties:
      address:
        type: string
      token:
        type: string
    type: object
  models.WechatNotificationConfig:
    properties:
      at_users:
        items:
          type: string
        type: array
      hook_address:
        type: string
      is_at_all:
        type: boolean
    type: object
  models.WeeklyDeployStat:
    properties:
      create_time:
        type: integer
      date:
        type: string
      failed:
        type: integer
      production:
        type: boolean
      project_key:
        type: string
      rollback:
        type: integer
      success:
        type: integer
      timeout:
        type: integer
      update_time:
        type: integer
    type: object
  models.WorkWXApproval:
    properties:
      approval_id:
        description: 'ID: workwx im app mongodb id'
        type: string
      approval_node_details:
        items:
          $ref: '#/definitions/workwx.ApprovalNode'
        type: array
      approval_nodes:
        items:
          $ref: '#/definitions/workwx.ApprovalNode'
        type: array
      creator_user:
        $ref: '#/definitions/workwx.ApprovalUser'
      instance_id:
        type: string
      timeout:
        type: integer
    type: object
  models.WorkflowServiceModule:
    properties:
      artifacts:
        items:
          type: string
        type: array
      code_info:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      service_module:
        type: string
      service_name:
        type: string
    type: object
  models.WorkflowStage:
    properties:
      approval:
        $ref: '#/definitions/models.Approval'
      jobs:
        items:
          $ref: '#/definitions/models.Job'
        type: array
      manual_exec:
        $ref: '#/definitions/models.ManualExec'
      name:
        type: string
      parallel:
        type: boolean
    type: object
  models.WorkflowTaskArgs:
    properties:
      artifact_args:
        items:
          $ref: '#/definitions/models.ArtifactArgs'
        type: array
      base_namespace:
        type: string
      callback:
        $ref: '#/definitions/models.CallbackArgs'
      codehost_id:
        type: integer
      commit_id:
        type: string
      committer:
        type: string
      description:
        type: string
      distribute_enabled:
        type: boolean
      env_name:
        type: string
      env_recycle_policy:
        type: string
      env_update_policy:
        type: string
      event_type:
        type: string
      hook_payload:
        allOf:
        - $ref: '#/definitions/models.HookPayload'
        description: github check run
      ignore_cache:
        description: Ignore docker build cache
        type: boolean
      is_parallel:
        type: boolean
      merge_request_id:
        description: webhook触发工作流任务时，触发任务的repo信息、prID和commitID、分支信息
        type: string
      namespace:
        description: 为了兼容老数据，namespace可能会存多个环境名称，用逗号隔开
        type: string
      notification_id:
        description: NotificationID is the id of scmnotify.Notification
        type: string
      product_tmpl_name:
        type: string
      ref:
        type: string
      registry_id:
        type: string
      release_images:
        items:
          $ref: '#/definitions/models.ReleaseImage'
        type: array
      repo_name:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      req_id:
        type: string
      request_mode:
        description: 请求模式，openAPI表示外部客户调用
        type: string
      reset_cache:
        description: Ignore workspace cache and reset volume
        type: boolean
      source:
        type: string
      storage_id:
        type: string
      targets:
        items:
          $ref: '#/definitions/models.TargetArgs'
        type: array
      tests:
        items:
          $ref: '#/definitions/models.TestArgs'
        type: array
      version_args:
        $ref: '#/definitions/models.VersionArgs'
      workflow_name:
        type: string
      workflow_task_creator:
        type: string
    type: object
  models.WorkflowV4:
    properties:
      approval_ticket_id:
        type: string
      base_name:
        type: string
      category:
        $ref: '#/definitions/setting.WorkflowCategory'
      concurrency_limit:
        description: |-
          ConcurrencyLimit is the max number of concurrent runs of this workflow
          -1 means no limit
        type: integer
      create_time:
        type: integer
      created_by:
        type: string
      custom_field:
        $ref: '#/definitions/models.CustomField'
      debug:
        type: boolean
      description:
        type: string
      disabled:
        type: boolean
      display_name:
        type: string
      enable_approval_ticket:
        type: boolean
      general_hook_ctls:
        items:
          $ref: '#/definitions/models.GeneralHook'
        type: array
      hash:
        type: string
      hook_ctl:
        items:
          $ref: '#/definitions/models.WorkflowV4Hook'
        type: array
      hook_payload:
        $ref: '#/definitions/models.HookPayload'
      id:
        type: string
      jira_hook_ctls:
        items:
          $ref: '#/definitions/models.JiraHook'
        type: array
      meego_hook_ctls:
        items:
          $ref: '#/definitions/models.MeegoHook'
        type: array
      name:
        type: string
      notification_id:
        type: string
      notify_ctls:
        items:
          $ref: '#/definitions/models.NotifyCtl'
        type: array
      params:
        items:
          $ref: '#/definitions/models.Param'
        type: array
      project:
        type: string
      remark:
        type: string
      share_storages:
        items:
          $ref: '#/definitions/models.ShareStorage'
        type: array
      stages:
        items:
          $ref: '#/definitions/models.WorkflowStage'
        type: array
      update_time:
        type: integer
      updated_by:
        type: string
    type: object
  models.WorkflowV4Hook:
    properties:
      auto_cancel:
        type: boolean
      check_patch_set_change:
        type: boolean
      description:
        type: string
      enabled:
        type: boolean
      is_manual:
        type: boolean
      main_repo:
        $ref: '#/definitions/models.MainHookRepo'
      name:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      workflow_arg:
        $ref: '#/definitions/models.WorkflowV4'
    type: object
  models.Workload:
    properties:
      env_name:
        type: string
      name:
        type: string
      product_name:
        type: string
      type:
        type: string
    type: object
  models.ZadigBlueGreenDeployEnvInformation:
    properties:
      env:
        type: string
      registry_id:
        type: string
      services:
        items:
          $ref: '#/definitions/models.BlueGreenDeployV2Service'
        type: array
    type: object
  models.ZadigBuildJobSpec:
    properties:
      default_service_and_builds:
        items:
          $ref: '#/definitions/models.ServiceAndBuild'
        type: array
      docker_registry_id:
        type: string
      job_name:
        type: string
      origin_job_name:
        type: string
      ref_repos:
        type: boolean
      service_and_builds:
        items:
          $ref: '#/definitions/models.ServiceAndBuild'
        type: array
      service_and_builds_options:
        items:
          $ref: '#/definitions/models.ServiceAndBuild'
        type: array
      service_module:
        type: string
      service_name:
        type: string
      source:
        $ref: '#/definitions/config.DeploySourceType'
    type: object
  models.ZadigDeployEnvInformation:
    properties:
      env:
        type: string
      env_alias:
        type: string
      production:
        type: boolean
      registry_id:
        type: string
      services:
        items:
          $ref: '#/definitions/models.DeployOptionInfo'
        type: array
    type: object
  models.ZadigDeployJobSpec:
    properties:
      deploy_contents:
        items:
          $ref: '#/definitions/config.DeployContent'
        type: array
      deploy_type:
        type: string
      env:
        type: string
      env_options:
        items:
          $ref: '#/definitions/models.ZadigDeployEnvInformation'
        type: array
      env_source:
        type: string
      job_name:
        description: 当 source 为 fromjob 时需要，指定部署镜像来源是上游哪一个构建任务
        type: string
      merge_strategy_source:
        type: string
      origin_job_name:
        description: save the origin quoted job name
        type: string
      production:
        type: boolean
      service_and_images:
        description: 'TODO: Deprecated in 2.3.0, this field is now used for saving
          the default service module info for deployment.'
        items:
          $ref: '#/definitions/models.ServiceAndImage'
        type: array
      service_variable_config:
        description: |-
          k8s type service only configuration, this is the field to save variable config for each service. The logic is:
          1. if the service is not in the config, use the variable info in the env/service definition
          2. if the service is in the config, but the VariableConfigs field is empty, still use everything in the env/service
          3. if the VariableConfigs is not empty, only show the variables defined in the DeployServiceVariableConfig field
        items:
          $ref: '#/definitions/models.DeployServiceVariableConfig'
        type: array
      services:
        items:
          $ref: '#/definitions/models.DeployServiceInfo'
        type: array
      skip_check_helm_workload_status:
        type: boolean
      skip_check_run_status:
        type: boolean
      source:
        allOf:
        - $ref: '#/definitions/config.DeploySourceType'
        description: fromjob/runtime, runtime 表示运行时输入，fromjob 表示从上游构建任务中获取
      value_merge_strategy:
        description: helm only field
        type: string
    type: object
  models.ZadigDistributeImageJobSpec:
    properties:
      cluster_id:
        type: string
      cluster_source:
        type: string
      custom_annotations:
        items:
          $ref: '#/definitions/util.KeyValue'
        type: array
      custom_labels:
        items:
          $ref: '#/definitions/util.KeyValue'
        type: array
      distribute_method:
        $ref: '#/definitions/config.DistributeImageMethod'
      enable_target_image_tag_rule:
        type: boolean
      job_name:
        description: required when source is `fromjob`, specify which upstream build
          job the distribution image source is
        type: string
      source:
        allOf:
        - $ref: '#/definitions/config.DeploySourceType'
        description: fromjob/runtime, `runtime` means runtime input, `fromjob` means
          that it is obtained from the upstream build job
      source_registry_id:
        description: not required when source is fromjob, directly obtained from upstream
          build job information
        type: string
      strategy_id:
        type: string
      target_image_tag_rule:
        type: string
      target_options:
        items:
          $ref: '#/definitions/models.DistributeTarget'
        type: array
      target_registry_id:
        type: string
      targets:
        items:
          $ref: '#/definitions/models.DistributeTarget'
        type: array
      timeout:
        description: unit is minute.
        type: integer
    type: object
  models.ZadigHelmChartDeployJobSpec:
    properties:
      deploy_helm_charts:
        items:
          $ref: '#/definitions/models.DeployHelmChart'
        type: array
      env:
        type: string
      env_alias:
        type: string
      env_options:
        items:
          $ref: '#/definitions/models.ZadigHelmDeployEnvInformation'
        type: array
      env_source:
        type: string
      production:
        type: boolean
      skip_check_run_status:
        type: boolean
    type: object
  models.ZadigHelmDeployEnvInformation:
    properties:
      env:
        type: string
      services:
        items:
          $ref: '#/definitions/models.DeployHelmChart'
        type: array
    type: object
  models.ZadigScanningJobSpec:
    properties:
      job_name:
        type: string
      origin_job_name:
        type: string
      ref_repos:
        type: boolean
      scanning_options:
        items:
          $ref: '#/definitions/models.ScanningModule'
        type: array
      scanning_type:
        $ref: '#/definitions/config.ScanningModuleType'
      scannings:
        description: Scannings used only for normal scanning. for service scanning
          we use
        items:
          $ref: '#/definitions/models.ScanningModule'
        type: array
      service_and_scannings:
        description: ServiceAndScannings is the configured field for this job. It
          includes all the services along with its configured scanning.
        items:
          $ref: '#/definitions/models.ServiceAndScannings'
        type: array
      service_scanning_options:
        items:
          $ref: '#/definitions/models.ServiceAndScannings'
        type: array
      source:
        $ref: '#/definitions/config.DeploySourceType'
      target_services:
        description: selected service in service scanning
        items:
          $ref: '#/definitions/models.ServiceTestTarget'
        type: array
    type: object
  models.ZadigVMDeployEnvInformation:
    properties:
      env:
        type: string
      services:
        items:
          $ref: '#/definitions/models.ServiceAndVMDeploy'
        type: array
    type: object
  models.ZadigVMDeployJobSpec:
    properties:
      env:
        type: string
      env_alias:
        type: string
      env_options:
        items:
          $ref: '#/definitions/models.ZadigVMDeployEnvInformation'
        type: array
      job_name:
        description: 当 source 为 fromjob 时需要，指定部署镜像来源是上游哪一个构建任务
        type: string
      origin_job_name:
        description: save the origin quoted job name
        type: string
      production:
        type: boolean
      s3_storage_id:
        type: string
      service_and_vm_deploys:
        items:
          $ref: '#/definitions/models.ServiceAndVMDeploy'
        type: array
      source:
        allOf:
        - $ref: '#/definitions/config.DeploySourceType'
        description: fromjob/runtime, runtime 表示运行时输入，fromjob 表示从上游构建任务中获取
    type: object
  os.FileMode:
    enum:
    - 2147483648
    - 1073741824
    - 536870912
    - 268435456
    - 134217728
    - 67108864
    - 33554432
    - 16777216
    - 8388608
    - 4194304
    - 2097152
    - 1048576
    - 524288
    - 2401763328
    - 511
    - 2147483648
    - 1073741824
    - 536870912
    - 268435456
    - 134217728
    - 67108864
    - 33554432
    - 16777216
    - 8388608
    - 4194304
    - 2097152
    - 1048576
    - 524288
    - 2401763328
    - 511
    type: integer
    x-enum-comments:
      ModeAppend: 'a: append-only'
      ModeCharDevice: 'c: Unix character device, when ModeDevice is set'
      ModeDevice: 'D: device file'
      ModeDir: 'd: is a directory'
      ModeExclusive: 'l: exclusive use'
      ModeIrregular: '?: non-regular file; nothing else is known about this file'
      ModeNamedPipe: 'p: named pipe (FIFO)'
      ModePerm: Unix permission bits, 0o777
      ModeSetgid: 'g: setgid'
      ModeSetuid: 'u: setuid'
      ModeSocket: 'S: Unix domain socket'
      ModeSticky: 't: sticky'
      ModeSymlink: 'L: symbolic link'
      ModeTemporary: 'T: temporary file; Plan 9 only'
    x-enum-varnames:
    - ModeDir
    - ModeAppend
    - ModeExclusive
    - ModeTemporary
    - ModeSymlink
    - ModeDevice
    - ModeNamedPipe
    - ModeSocket
    - ModeSetuid
    - ModeSetgid
    - ModeCharDevice
    - ModeSticky
    - ModeIrregular
    - ModeType
    - ModePerm
  resource.Backend:
    properties:
      service_name:
        type: string
      service_port:
        type: string
    type: object
  resource.Container:
    properties:
      finished_at:
        description: Time at which the container last terminated
        type: integer
      image:
        type: string
      message:
        description: Message regarding the last termination of the container
        type: string
      name:
        type: string
      ports:
        items:
          $ref: '#/definitions/resource.ContainerPort'
        type: array
      ready:
        type: boolean
      reason:
        description: reason from the last termination of the container
        type: string
      restart_count:
        type: integer
      started_at:
        description: Time at which previous execution of the container started
        type: integer
      status:
        type: string
    type: object
  resource.ContainerPort:
    properties:
      containerPort:
        description: |-
          Number of port to expose on the pod's IP address.
          This must be a valid port number, 0 < x < 65536.
        type: integer
      hostIP:
        description: |-
          What host IP to bind the external port to.
          +optional
        type: string
      hostPort:
        description: |-
          Number of port to expose on the host.
          If specified, this must be a valid port number, 0 < x < 65536.
          If HostNetwork is specified, this must match ContainerPort.
          Most containers do not need this.
          +optional
        type: integer
      name:
        description: |-
          If specified, this must be an IANA_SVC_NAME and unique within the pod. Each
          named port in a pod must have a unique name. Name for the port that can be
          referred to by services.
          +optional
        type: string
      protocol:
        allOf:
        - $ref: '#/definitions/resource.Protocol'
        description: |-
          Protocol for port. Must be UDP, TCP, or SCTP.
          Defaults to "TCP".
          +optional
          +default="TCP"
    type: object
  resource.HostInfo:
    properties:
      backend:
        items:
          $ref: '#/definitions/resource.Backend'
        type: array
      host:
        type: string
    type: object
  resource.Pod:
    properties:
      age:
        type: string
      containers:
        items:
          $ref: '#/definitions/resource.Container'
        type: array
      containers_message:
        type: string
      containers_ready:
        type: boolean
      createtime:
        type: integer
      enable_debug_container:
        type: boolean
      host_ip:
        type: string
      ip:
        type: string
      kind:
        type: string
      labels:
        additionalProperties:
          type: string
        type: object
      name:
        type: string
      node_name:
        type: string
      pod_ready:
        type: boolean
      status:
        type: string
    type: object
  resource.Protocol:
    enum:
    - TCP
    - UDP
    - SCTP
    type: string
    x-enum-varnames:
    - ProtocolTCP
    - ProtocolUDP
    - ProtocolSCTP
  service.AddSAEAppToEnvRequest:
    properties:
      app_ids:
        items:
          type: string
        type: array
    type: object
  service.AdvancedConfig:
    properties:
      cluster_access_yaml:
        type: string
      enable_irsa:
        type: boolean
      irsa_role_arn:
        type: string
      node_labels:
        items:
          type: string
        type: array
      project_names:
        items:
          type: string
        type: array
      schedule_strategy:
        items:
          $ref: '#/definitions/service.ScheduleStrategy'
        type: array
      schedule_workflow:
        type: boolean
      strategy:
        type: string
      tolerations:
        type: string
    type: object
  service.BuildResp:
    properties:
      cluster_id:
        type: string
      id:
        type: string
      infrastructure:
        type: string
      key_vals:
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      name:
        type: string
      pipelines:
        items:
          type: string
        type: array
      productName:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      targets:
        items:
          $ref: '#/definitions/models.ServiceModuleTarget'
        type: array
      update_by:
        type: string
      update_time:
        type: integer
    type: object
  service.BulkHelmServiceCreationArgs:
    properties:
      auto_sync:
        type: boolean
      createFrom: {}
      createdBy:
        type: string
      production:
        type: boolean
      source:
        $ref: '#/definitions/service.LoadSource'
      valuesData:
        $ref: '#/definitions/service.ValuesDataArgs'
    type: object
  service.BulkHelmServiceCreationResponse:
    properties:
      failedServices:
        items:
          $ref: '#/definitions/service.FailedService'
        type: array
      successServices:
        items:
          type: string
        type: array
    type: object
  service.ClusterDeletionInfo:
    properties:
      deletable:
        type: boolean
      env_in_use:
        items:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_aslan_core_multicluster_service.EnvInfo'
        type: array
    type: object
  service.ClusterStrategyReference:
    properties:
      has_references:
        type: boolean
      strategy_id:
        type: string
      strategy_name:
        type: string
    type: object
  service.ContainerWithBuilds:
    properties:
      build_names:
        items:
          type: string
        type: array
      image:
        type: string
      image_name:
        type: string
      imagePath:
        $ref: '#/definitions/models.ImagePathSpec'
      name:
        type: string
      type:
        $ref: '#/definitions/setting.ContainerType'
    type: object
  service.ConvertVaraibleKVAndYamlActionType:
    enum:
    - toKV
    - toYaml
    type: string
    x-enum-varnames:
    - ConvertVaraibleKVAndYamlActionTypeToKV
    - ConvertVaraibleKVAndYamlActionTypeToYaml
  service.ConvertVaraibleKVAndYamlArgs:
    properties:
      action:
        $ref: '#/definitions/service.ConvertVaraibleKVAndYamlActionType'
      kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      yaml:
        type: string
    required:
    - action
    - kvs
    type: object
  service.CreateK8SDeliveryVersionArgs:
    properties:
      desc:
        type: string
      envName:
        type: string
      imageRegistryID:
        type: string
      labels:
        items:
          type: string
        type: array
      productName:
        type: string
      production:
        type: boolean
      retry:
        type: boolean
      version:
        type: string
      yamlDatas:
        items:
          $ref: '#/definitions/service.CreateK8SDeliveryVersionYamlData'
        type: array
    type: object
  service.CreateK8SDeliveryVersionYamlData:
    properties:
      imageDatas:
        items:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_aslan_core_delivery_service.ImageData'
        type: array
      serviceName:
        type: string
      yamlContent:
        type: string
    type: object
  service.CreateSingleProductArg:
    properties:
      alias:
        type: string
      base_env_name:
        type: string
      base_name:
        description: for collaboration mode
        type: string
      chartValues:
        items:
          $ref: '#/definitions/service.ProductHelmServiceCreationInfo'
        type: array
      cluster_id:
        type: string
      default_values:
        type: string
      defaultValues:
        description: TODO fix me
        type: string
      env_configs:
        description: New Since v1.13.0
        items:
          $ref: '#/definitions/models.CreateUpdateCommonEnvCfgArgs'
        type: array
      env_name:
        type: string
      global_variables:
        description: for k8s products
        items:
          $ref: '#/definitions/types.GlobalVariableKV'
        type: array
      is_existed:
        type: boolean
      istio_grayscale:
        allOf:
        - $ref: '#/definitions/models.IstioGrayscale'
        description: New Since v2.1.0
      namespace:
        type: string
      product_name:
        type: string
      production:
        type: boolean
      registry_id:
        type: string
      services:
        items:
          items:
            $ref: '#/definitions/service.ProductK8sServiceCreationInfo'
          type: array
        type: array
      share_env:
        allOf:
        - $ref: '#/definitions/models.ProductShareEnv'
        description: New Since v1.12.0
      valuesData:
        allOf:
        - $ref: '#/definitions/service.ValuesDataArgs'
        description: for helm products
    type: object
  service.CreateSprintResponse:
    properties:
      id:
        type: string
    type: object
  service.DelSAEAppFromEnvRequest:
    properties:
      app_ids:
        items:
          type: string
        type: array
    type: object
  service.DeployDashboard:
    properties:
      data:
        items:
          $ref: '#/definitions/models.WeeklyDeployStat'
        type: array
      success:
        type: integer
      total:
        type: integer
    type: object
  service.DeployStatus:
    enum:
    - deployed
    - undeployed
    type: string
    x-enum-varnames:
    - StatusDeployed
    - StatusUnDeployed
  service.DeployableEnv:
    properties:
      cluster_id:
        type: string
      env_name:
        type: string
      global_variable_kvs:
        items:
          $ref: '#/definitions/types.GlobalVariableKV'
        type: array
      namespace:
        type: string
      services:
        items:
          $ref: '#/definitions/types.ServiceWithVariable'
        type: array
    type: object
  service.DeployableEnvResp:
    properties:
      envs:
        items:
          $ref: '#/definitions/service.DeployableEnv'
        type: array
    type: object
  service.DiffEnvServiceVersionsResponse:
    properties:
      containers_a:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      containers_b:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      create_by_a:
        type: string
      create_by_b:
        type: string
      create_time_a:
        type: integer
      create_time_b:
        type: integer
      type:
        type: string
      variable_yaml_a:
        type: string
      variable_yaml_b:
        type: string
      yaml_a:
        type: string
      yaml_b:
        type: string
    type: object
  service.DiffServiceVersionsResponse:
    properties:
      type:
        type: string
      variable_yaml_a:
        type: string
      variable_yaml_b:
        type: string
      yaml_a:
        type: string
      yaml_b:
        type: string
    type: object
  service.EnvAnalysisCronArg:
    properties:
      cron:
        type: string
      enable:
        type: boolean
    type: object
  service.EnvAnalysisRespone:
    properties:
      result:
        type: string
    type: object
  service.EnvConfigsArgs:
    properties:
      analysis_config:
        $ref: '#/definitions/models.AnalysisConfig'
      notification_configs:
        items:
          $ref: '#/definitions/models.NotificationConfig'
        type: array
    type: object
  service.EnvDefinition:
    properties:
      cluster_name:
        type: string
      env_key:
        type: string
      namespace:
        type: string
    type: object
  service.EnvRendersetArg:
    properties:
      chartValues:
        items:
          $ref: '#/definitions/service.HelmSvcRenderArg'
        type: array
      defaultValues:
        type: string
      deployType:
        type: string
      updateServiceTmpl:
        type: boolean
      valuesData:
        $ref: '#/definitions/service.ValuesDataArgs'
    type: object
  service.EnvResp:
    properties:
      alias:
        type: string
      base_name:
        type: string
      base_refs:
        items:
          type: string
        type: array
      cluster_id:
        type: string
      clusterName:
        type: string
      error:
        type: string
      is_existed:
        type: boolean
      is_favorite:
        type: boolean
      isPublic:
        type: boolean
      istio_grayscale_base_env:
        type: string
      istio_grayscale_enable:
        description: New Since v2.1.0
        type: boolean
      istio_grayscale_is_base:
        type: boolean
      name:
        type: string
      namespace:
        type: string
      production:
        type: boolean
      projectName:
        type: string
      registry_id:
        type: string
      share_env_base_env:
        type: string
      share_env_enable:
        description: New Since v1.11.0
        type: boolean
      share_env_is_base:
        type: boolean
      shared_ns:
        type: boolean
      source:
        type: string
      status:
        type: string
      updateBy:
        type: string
      updateTime:
        type: integer
    type: object
  service.EnvServices:
    properties:
      env_name:
        type: string
      product_name:
        type: string
      services:
        items:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_aslan_core_common_service.EnvService'
        type: array
    type: object
  service.EnvSleepCronArg:
    properties:
      awake_cron:
        type: string
      awake_cron_enable:
        type: boolean
      sleep_cron:
        type: string
      sleep_cron_enable:
        type: boolean
    type: object
  service.EstimateValuesArg:
    properties:
      chartName:
        type: string
      chartRepo:
        type: string
      chartVersion:
        type: string
      defaultValues:
        type: string
      overrideValues:
        items:
          $ref: '#/definitions/service.KVPair'
        type: array
      overrideYaml:
        type: string
    type: object
  service.ExecVmServiceCommandResponse:
    properties:
      error:
        type: string
      is_success:
        type: boolean
      is_timeout:
        type: boolean
      stderr:
        type: string
      stdout:
        type: string
    type: object
  service.FailedService:
    properties:
      error:
        type: string
      path:
        type: string
    type: object
  service.FileInfo:
    properties:
      is_dir:
        description: abbreviation for Mode().IsDir()
        type: boolean
      mod_time:
        description: modification time
        type: integer
      mode:
        allOf:
        - $ref: '#/definitions/os.FileMode'
        description: file mode bits
      name:
        description: base name of the file
        type: string
      parent:
        description: parent path of the file
        type: string
      size:
        description: length in bytes for regular files; system-dependent for others
        type: integer
    type: object
  service.GetBizDirServiceDetailResponse:
    properties:
      chart_version:
        type: string
      env_alias:
        type: string
      env_name:
        type: string
      error:
        type: string
      images:
        items:
          type: string
        type: array
      name:
        type: string
      production:
        type: boolean
      project_name:
        type: string
      status:
        type: string
      type:
        type: string
      update_time:
        type: integer
    type: object
  service.GetEnvServiceVersionYamlResponse:
    properties:
      containers:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      create_by:
        type: string
      create_time:
        type: integer
      deploy_type:
        type: string
      detail:
        type: string
      operation:
        $ref: '#/definitions/config.EnvOperation'
      override_kvs:
        type: string
      type:
        type: string
      variable_yaml:
        type: string
      yaml:
        type: string
    type: object
  service.GetGlobalVariableCandidatesRespone:
    properties:
      key_name:
        type: string
      related_service:
        items:
          type: string
        type: array
    type: object
  service.GetHelmValuesDifferenceResp:
    properties:
      current:
        type: string
      latest:
        type: string
      latest_flat_map:
        additionalProperties: true
        type: object
    type: object
  service.GetPortalServiceResponse:
    properties:
      default_gateway_address:
        type: string
      servers:
        items:
          $ref: '#/definitions/service.SetupPortalServiceRequest'
        type: array
    type: object
  service.GetReleaseInstanceDeployStatusResponse:
    properties:
      chart_name:
        type: string
      chart_version:
        type: string
      release_name:
        type: string
      status:
        type: string
      values:
        type: string
    type: object
  service.GetRollbackStatDetailResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/types.OpenAPIRollBackStat'
        type: array
      total:
        type: integer
    required:
    - data
    - total
    type: object
  service.GetServiceVersionYamlResponse:
    properties:
      type:
        type: string
      variable_yaml:
        type: string
      yaml:
        type: string
    type: object
  service.GetSvcRenderArg:
    properties:
      is_helm_chart_deploy:
        type: boolean
      service_or_release_name:
        type: string
    type: object
  service.GetSvcRenderRequest:
    properties:
      get_svc_render_args:
        items:
          $ref: '#/definitions/service.GetSvcRenderArg'
        type: array
    type: object
  service.GetWebhookConfigReponse:
    properties:
      secret:
        type: string
      url:
        type: string
    type: object
  service.GitRepoInfo:
    properties:
      branches:
        items:
          $ref: '#/definitions/client.Branch'
        type: array
      codehost_id:
        type: integer
      default_branch:
        type: string
      error_msg:
        description: get repo message fail message
        type: string
      filter_regexp:
        description: FilterRegexp is the regular expression filter for the branches
          and tags
        type: string
      key:
        type: string
      project_uuid:
        type: string
      prs:
        items:
          $ref: '#/definitions/client.PullRequest'
        type: array
      repo:
        type: string
      repo_id:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      repo_uuid:
        type: string
      source:
        type: string
      tags:
        items:
          $ref: '#/definitions/client.Tag'
        type: array
    type: object
  service.GroupDetail:
    properties:
      group_name:
        type: string
      projects:
        items:
          $ref: '#/definitions/models.ProjectDetail'
        type: array
    type: object
  service.HelmChartEditInfo:
    properties:
      file_content:
        type: string
      file_path:
        type: string
      production:
        type: boolean
    type: object
  service.HelmDeployStatusCheckRequest:
    properties:
      cluster_id:
        type: string
      env_name:
        type: string
      namespace:
        type: string
      services:
        items:
          type: string
        type: array
    type: object
  service.HelmService:
    properties:
      file_infos:
        items:
          $ref: '#/definitions/types.FileInfo'
        type: array
      service_infos:
        items:
          $ref: '#/definitions/models.Service'
        type: array
      services:
        items:
          items:
            type: string
          type: array
        type: array
    type: object
  service.HelmServiceCreationArgs:
    properties:
      auto_sync:
        type: boolean
      createFrom: {}
      createdBy:
        type: string
      name:
        type: string
      production:
        type: boolean
      source:
        $ref: '#/definitions/service.LoadSource'
      valuesData:
        $ref: '#/definitions/service.ValuesDataArgs'
    type: object
  service.HelmServiceModule:
    properties:
      service:
        $ref: '#/definitions/models.Service'
      service_modules:
        items:
          $ref: '#/definitions/service.ServiceModule'
        type: array
    type: object
  service.HelmSvcRenderArg:
    properties:
      chart_name:
        type: string
      chart_repo:
        type: string
      chartVersion:
        type: string
      deploy_strategy:
        description: New since 1.16.0, used to determine if the service will be installed
        type: string
      envName:
        type: string
      is_chart_deploy:
        type: boolean
      overrideValues:
        items:
          $ref: '#/definitions/service.KVPair'
        type: array
      overrideYaml:
        type: string
      release_name:
        type: string
      serviceName:
        type: string
      valuesData:
        $ref: '#/definitions/service.ValuesDataArgs'
      variable_yaml:
        type: string
      yaml_data:
        $ref: '#/definitions/template.CustomYaml'
    type: object
  service.IngressInfo:
    properties:
      host_info:
        items:
          $ref: '#/definitions/resource.HostInfo'
        type: array
    type: object
  service.IstioGatewayInfo:
    properties:
      servers:
        items:
          $ref: '#/definitions/service.IstioGatewayServer'
        type: array
    type: object
  service.IstioGatewayServer:
    properties:
      host:
        type: string
      port_number:
        type: integer
      port_protocol:
        type: string
    type: object
  service.IstioGrayscaleChecks:
    properties:
      namespace_has_istio_label:
        type: boolean
      pods_have_istio_proxy:
        type: boolean
      workloads_have_k8s_service:
        type: boolean
      workloads_ready:
        type: boolean
    type: object
  service.IstioGrayscaleReady:
    properties:
      checks:
        $ref: '#/definitions/service.IstioGrayscaleChecks'
      is_ready:
        type: boolean
    type: object
  service.JiraBoardResp:
    properties:
      id:
        type: integer
      name:
        type: string
      type:
        type: string
    type: object
  service.JiraProjectResp:
    properties:
      key:
        type: string
      name:
        type: string
    type: object
  service.JiraSprintResp:
    properties:
      completeDate:
        type: string
      endDate:
        type: string
      id:
        type: integer
      name:
        type: string
      originBoardId:
        type: integer
      startDate:
        type: string
      state:
        type: string
    type: object
  service.K8SCluster:
    properties:
      advanced_config:
        $ref: '#/definitions/service.AdvancedConfig'
      cache:
        $ref: '#/definitions/types.Cache'
      config:
        type: string
      createdAt:
        type: integer
      createdBy:
        type: string
      description:
        type: string
      dind_cfg:
        $ref: '#/definitions/models.DindCfg'
      id:
        type: string
      last_connection_time:
        type: integer
      local:
        type: boolean
      name:
        type: string
      production:
        type: boolean
      provider:
        type: integer
      share_storage:
        $ref: '#/definitions/types.ShareStorage'
      status:
        $ref: '#/definitions/setting.K8SClusterStatus'
      type:
        description: new field in 1.14, intended to enable kubeconfig for cluster
          management
        type: string
      update_hubagent_error_msg:
        type: string
    type: object
  service.K8sDeployStatusCheckRequest:
    properties:
      env_name:
        type: string
      services:
        items:
          $ref: '#/definitions/service.K8sSvcRenderArg'
        type: array
    type: object
  service.K8sSvcRenderArg:
    properties:
      deploy_strategy:
        description: New since 1.16.0, used to determine if the service will be installed
        type: string
      env_name:
        type: string
      latest_variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      latest_variable_yaml:
        type: string
      service_name:
        type: string
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      variable_yaml:
        type: string
    type: object
  service.K8sWorkloadsArgs:
    properties:
      cluster_id:
        type: string
      env_name:
        type: string
      namespace:
        type: string
      product_name:
        type: string
      registry_id:
        type: string
      workLoads:
        items:
          $ref: '#/definitions/models.Workload'
        type: array
    type: object
  service.KVPair:
    properties:
      key:
        type: string
      value: {}
    type: object
  service.ListEnvServiceVersionsResponse:
    properties:
      containers:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      create_by:
        type: string
      create_time:
        type: integer
      deploy_type:
        type: string
      detail:
        type: string
      env_name:
        type: string
      operation:
        $ref: '#/definitions/config.EnvOperation'
      project_name:
        type: string
      revision:
        type: integer
      service_name:
        type: string
    type: object
  service.ListPodsInfoRespone:
    properties:
      create_time:
        type: integer
      images:
        items:
          type: string
        type: array
      name:
        type: string
      ready:
        type: string
      status:
        type: string
    type: object
  service.ListReleasePlanResp:
    properties:
      list:
        items:
          $ref: '#/definitions/models.ReleasePlan'
        type: array
      total:
        type: integer
    type: object
  service.ListSAEAppsResponse:
    properties:
      applications:
        items:
          $ref: '#/definitions/models.SAEApplication'
        type: array
      current_page:
        type: integer
      total_size:
        type: integer
    type: object
  service.ListScanningRespItem:
    properties:
      cluster_id:
        type: string
      created_at:
        type: integer
      description:
        type: string
      id:
        type: string
      key_vals:
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      name:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      statistics:
        $ref: '#/definitions/service.ScanningStatistic'
      updated_at:
        type: integer
    type: object
  service.ListSprintResp:
    properties:
      list:
        items:
          $ref: '#/definitions/models.Sprint'
        type: array
      total:
        type: integer
    type: object
  service.ListSprintTemplateResp:
    properties:
      list:
        items:
          $ref: '#/definitions/models.SprintTemplate'
        type: array
    type: object
  service.ListSprintWorkItemTaskResponse:
    properties:
      count:
        type: integer
      tasks:
        items:
          $ref: '#/definitions/models.SprintWorkItemTask'
        type: array
    type: object
  service.LoadServiceFromYamlTemplateReq:
    properties:
      auto_sync:
        type: boolean
      project_name:
        type: string
      service_name:
        type: string
      service_variable_kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      template_id:
        type: string
      variable_yaml:
        type: string
    type: object
  service.LoadSource:
    enum:
    - repo
    - gerrit
    - publicRepo
    - chartTemplate
    - chartRepo
    type: string
    x-enum-comments:
      LoadFromRepo: exclude gerrit
    x-enum-varnames:
    - LoadFromRepo
    - LoadFromGerrit
    - LoadFromPublicRepo
    - LoadFromChartTemplate
    - LoadFromChartRepo
  service.MeegoProject:
    properties:
      key:
        type: string
      name:
        type: string
    type: object
  service.MeegoProjectResp:
    properties:
      projects:
        items:
          $ref: '#/definitions/service.MeegoProject'
        type: array
    type: object
  service.MeegoWorkItem:
    properties:
      current_state:
        type: string
      id:
        type: integer
      name:
        type: string
    type: object
  service.MeegoWorkItemResp:
    properties:
      work_items:
        items:
          $ref: '#/definitions/service.MeegoWorkItem'
        type: array
    type: object
  service.MeegoWorkItemType:
    properties:
      name:
        type: string
      type_key:
        type: string
    type: object
  service.MeegoWorkItemTypeResp:
    properties:
      work_item_types:
        items:
          $ref: '#/definitions/service.MeegoWorkItemType'
        type: array
    type: object
  service.OpenAPICluster:
    properties:
      cluster_id:
        type: string
      created_by:
        type: string
      created_time:
        type: integer
      description:
        type: string
      local:
        type: boolean
      name:
        type: string
      production:
        type: boolean
      project_names:
        items:
          type: string
        type: array
      provider:
        type: integer
      provider_name:
        type: string
      status:
        type: string
      type:
        type: string
    type: object
  service.OpenAPICreateClusterRequest:
    properties:
      description:
        type: string
      kube_config:
        type: string
      name:
        type: string
      production:
        type: boolean
      project_names:
        items:
          type: string
        type: array
      provider:
        type: integer
      type:
        type: string
    type: object
  service.OpenAPICreateClusterResponse:
    properties:
      agent_cmd:
        type: string
      cluster:
        $ref: '#/definitions/service.OpenAPICluster'
    type: object
  service.OpenAPICreateHelmDeliveryVersionChartData:
    properties:
      image_datas:
        items:
          $ref: '#/definitions/service.OpenAPIDeliveryVersionImageData'
        type: array
      service_name:
        type: string
      version:
        type: string
    type: object
  service.OpenAPICreateHelmDeliveryVersionRequest:
    properties:
      chart_datas:
        items:
          $ref: '#/definitions/service.OpenAPICreateHelmDeliveryVersionChartData'
        type: array
      chart_repo_name:
        type: string
      desc:
        type: string
      env_name:
        type: string
      image_registry_id:
        type: string
      labels:
        items:
          type: string
        type: array
      production:
        type: boolean
      project_key:
        type: string
      retry:
        type: boolean
      version_name:
        type: string
    type: object
  service.OpenAPICreateK8SDeliveryVersionRequest:
    properties:
      desc:
        type: string
      env_name:
        type: string
      image_registry_id:
        type: string
      labels:
        items:
          type: string
        type: array
      production:
        type: boolean
      project_key:
        type: string
      retry:
        type: boolean
      version_name:
        type: string
      yaml_datas:
        items:
          $ref: '#/definitions/service.OpenAPICreateK8SDeliveryVersionYamlData'
        type: array
    type: object
  service.OpenAPICreateK8SDeliveryVersionYamlData:
    properties:
      image_datas:
        items:
          $ref: '#/definitions/service.OpenAPIDeliveryVersionImageData'
        type: array
      service_name:
        type: string
    type: object
  service.OpenAPIDeliveryVersionImageData:
    properties:
      container_name:
        type: string
      disable_image_dist:
        type: boolean
      image_name:
        type: string
      image_tag:
        type: string
    type: object
  service.OpenAPIEnvGlobalVariables:
    properties:
      global_variables:
        items:
          $ref: '#/definitions/types.GlobalVariableKV'
        type: array
    type: object
  service.OpenAPIInitializeProjectReq:
    properties:
      description:
        type: string
      env_list:
        items:
          $ref: '#/definitions/service.EnvDefinition'
        type: array
      is_public:
        type: boolean
      project_key:
        type: string
      project_name:
        type: string
      service_list:
        items:
          $ref: '#/definitions/service.ServiceDefinition'
        type: array
    type: object
  service.Overview:
    properties:
      artifact_count:
        type: integer
      cluster_count:
        type: integer
      env_count:
        type: integer
      project_count:
        type: integer
      service_count:
        type: integer
      workflow_count:
        type: integer
    type: object
  service.PreviewServiceArgs:
    properties:
      env_name:
        type: string
      product_name:
        type: string
      service_modules:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      service_name:
        type: string
      update_service_revision:
        type: boolean
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
    type: object
  service.Product:
    properties:
      base_name:
        type: string
      chartValues:
        items:
          $ref: '#/definitions/service.HelmSvcRenderArg'
        type: array
      collaboration_mode:
        type: string
      collaboration_type:
        $ref: '#/definitions/config.CollaborationType'
      default_values:
        type: string
      deploy_type:
        type: string
      global_variables:
        items:
          $ref: '#/definitions/types.GlobalVariableKV'
        type: array
      name:
        type: string
      services:
        items:
          $ref: '#/definitions/service.K8sSvcRenderArg'
        type: array
      valuesData:
        $ref: '#/definitions/service.ValuesDataArgs'
      yaml_data:
        $ref: '#/definitions/template.CustomYaml'
    type: object
  service.ProductHelmServiceCreationInfo:
    properties:
      chart_name:
        type: string
      chart_repo:
        type: string
      chartVersion:
        type: string
      deploy_strategy:
        type: string
      envName:
        type: string
      is_chart_deploy:
        type: boolean
      overrideValues:
        items:
          $ref: '#/definitions/service.KVPair'
        type: array
      overrideYaml:
        type: string
      release_name:
        type: string
      serviceName:
        type: string
      valuesData:
        $ref: '#/definitions/service.ValuesDataArgs'
      variable_yaml:
        type: string
      yaml_data:
        $ref: '#/definitions/template.CustomYaml'
    type: object
  service.ProductK8sServiceCreationInfo:
    properties:
      containers:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      deploy_strategy:
        type: string
      env_configs:
        items:
          $ref: '#/definitions/models.EnvConfig'
        type: array
      error:
        type: string
      product_name:
        type: string
      release_name:
        type: string
      render:
        allOf:
        - $ref: '#/definitions/template.ServiceRender'
        description: New since 1.9.0 used to replace service renders in render_set
      rendered_yaml:
        type: string
      resources:
        items:
          $ref: '#/definitions/models.ServiceResource'
        type: array
      revision:
        type: integer
      service_name:
        type: string
      type:
        type: string
      updatable:
        type: boolean
      update_time:
        type: integer
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      variable_yaml:
        type: string
    type: object
  service.ProjectDetailedRepresentation:
    properties:
      alias:
        type: string
      deployType:
        type: string
      desc:
        type: string
      envs:
        items:
          type: string
        type: array
      name:
        type: string
      onboard:
        type: boolean
      public:
        type: boolean
      updatedAt:
        type: integer
      updatedBy:
        type: string
    type: object
  service.ProjectDetailedResponse:
    properties:
      projects:
        items:
          $ref: '#/definitions/service.ProjectDetailedRepresentation'
        type: array
      total:
        type: integer
    type: object
  service.ReleaseDashboard:
    properties:
      average_duration:
        type: number
      data:
        items:
          $ref: '#/definitions/models.MonthlyReleaseStat'
        type: array
      total:
        type: integer
    type: object
  service.RepoConfig:
    properties:
      branch:
        type: string
      codehostID:
        type: integer
      namespace:
        type: string
      owner:
        type: string
      repo:
        type: string
      valuesPaths:
        items:
          type: string
        type: array
    type: object
  service.RepoImgResp:
    properties:
      created:
        type: string
      digest:
        type: string
      host:
        type: string
      name:
        type: string
      owner:
        type: string
      tag:
        type: string
    type: object
  service.RepoInfoList:
    properties:
      infos:
        items:
          $ref: '#/definitions/service.GitRepoInfo'
        type: array
    type: object
  service.ResourceDeployStatus:
    properties:
      name:
        type: string
      override_kvs:
        items:
          $ref: '#/definitions/service.KVPair'
        type: array
      override_yaml:
        type: string
      status:
        $ref: '#/definitions/service.DeployStatus'
      type:
        type: string
    type: object
  service.SAEAppGroup:
    properties:
      group_id:
        type: string
      group_name:
        type: string
      group_type:
        type: integer
      image_url:
        type: string
      instances:
        items:
          $ref: '#/definitions/service.SAEAppInstance'
        type: array
      package_type:
        type: string
      package_url:
        type: string
      package_version:
        type: string
      replicas:
        type: integer
      running_instances:
        type: integer
    type: object
  service.SAEAppInstance:
    properties:
      create_timestamp:
        type: integer
      eip:
        type: string
      finish_timestamp:
        type: integer
      group_id:
        type: string
      image_url:
        type: string
      instance_container_ip:
        type: string
      instance_container_restarts:
        type: integer
      instance_container_status:
        type: string
      instance_health_status:
        type: string
      instance_id:
        type: string
      package_version:
        type: string
    type: object
  service.SAEAppVersion:
    properties:
      build_package_url:
        type: string
      create_time:
        type: string
      id:
        type: string
      type:
        type: string
      war_url:
        type: string
    type: object
  service.SAENamespace:
    properties:
      namespace_description:
        type: string
      namespace_id:
        type: string
      namespace_name:
        type: string
      namespace_short_id:
        type: string
    type: object
  service.Scanning:
    properties:
      advanced_settings:
        $ref: '#/definitions/models.ScanningAdvancedSetting'
      check_quality_gate:
        type: boolean
      description:
        type: string
      enable_scanner:
        type: boolean
      envs:
        description: Envs is the user defined key/values
        items:
          $ref: '#/definitions/models.KeyVal'
        type: array
      id:
        type: string
      image_id:
        type: string
      infrastructure:
        type: string
      installs:
        items:
          $ref: '#/definitions/models.Item'
        type: array
      name:
        type: string
      notify_ctls:
        items:
          $ref: '#/definitions/models.NotifyCtl'
        type: array
      outputs:
        items:
          $ref: '#/definitions/models.Output'
        type: array
      parameter:
        description: Parameter is for sonarQube type only
        type: string
      project_name:
        type: string
      repos:
        items:
          $ref: '#/definitions/types.Repository'
        type: array
      scanner_type:
        type: string
      script:
        type: string
      script_type:
        $ref: '#/definitions/types.ScriptType'
      sonar_id:
        type: string
      template_id:
        description: template IDs
        type: string
      vm_labels:
        items:
          type: string
        type: array
    type: object
  service.ScanningStatistic:
    properties:
      run_time_average:
        type: integer
      times_run:
        type: integer
    type: object
  service.ScheduleStrategy:
    properties:
      default:
        type: boolean
      node_labels:
        items:
          type: string
        type: array
      strategy:
        type: string
      strategy_id:
        type: string
      strategy_name:
        type: string
      tolerations:
        type: string
    type: object
  service.SearchBizDirByServiceGroup:
    properties:
      group_name:
        type: string
      projects:
        items:
          $ref: '#/definitions/service.SearchBizDirByServiceProject'
        type: array
    type: object
  service.SearchBizDirByServiceProject:
    properties:
      project:
        $ref: '#/definitions/models.ProjectDetail'
      services:
        items:
          type: string
        type: array
    type: object
  service.ServiceDefinition:
    properties:
      auto_sync:
        type: boolean
      service_name:
        type: string
      source:
        type: string
      template_name:
        type: string
      values_yaml:
        type: string
      variable_yaml:
        items:
          $ref: '#/definitions/util.KeyValue'
        type: array
      yaml:
        type: string
    type: object
  service.ServiceDeployStatus:
    properties:
      resources:
        items:
          $ref: '#/definitions/service.ResourceDeployStatus'
        type: array
      service_name:
        type: string
    type: object
  service.ServiceLabelResp:
    properties:
      id:
        type: string
      key:
        type: string
      label_id:
        type: string
      value:
        type: string
    type: object
  service.ServiceModule:
    properties:
      build_names:
        items:
          type: string
        type: array
      image:
        type: string
      image_name:
        type: string
      imagePath:
        $ref: '#/definitions/models.ImagePathSpec'
      name:
        type: string
      type:
        $ref: '#/definitions/setting.ContainerType'
    type: object
  service.ServiceModuleAndBuildResp:
    properties:
      image_name:
        type: string
      module_builds:
        items:
          $ref: '#/definitions/service.BuildResp'
        type: array
      service_module:
        type: string
      service_name:
        type: string
    type: object
  service.ServiceOption:
    properties:
      service:
        $ref: '#/definitions/models.Service'
      service_module:
        items:
          $ref: '#/definitions/service.ServiceModule'
        type: array
      service_variable_kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      system_variable:
        items:
          $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_aslan_core_service_service.Variable'
        type: array
      variable_yaml:
        type: string
      yaml:
        type: string
    type: object
  service.ServiceProductMap:
    properties:
      auto_sync:
        type: boolean
      branch_name:
        type: string
      codehost_id:
        type: integer
      containers:
        items:
          $ref: '#/definitions/service.ContainerWithBuilds'
        type: array
      create_from: {}
      estimated_merged_variable:
        description: estimated merged variable is set when the service is created
          from template
        type: string
      estimated_merged_variable_kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      gerrit_remote_name:
        type: string
      is_dir:
        type: boolean
      load_path:
        type: string
      product:
        items:
          type: string
        type: array
      product_name:
        type: string
      repo_name:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      repo_uuid:
        type: string
      service_name:
        type: string
      source:
        type: string
      type:
        type: string
    type: object
  service.ServiceResp:
    properties:
      deploy_strategy:
        type: string
      env_configs:
        items:
          $ref: '#/definitions/models.EnvConfig'
        type: array
      env_name:
        type: string
      env_statuses:
        items:
          $ref: '#/definitions/models.EnvStatus'
        type: array
      error:
        type: string
      images:
        items:
          type: string
        type: array
      ingress:
        $ref: '#/definitions/service.IngressInfo'
      is_helm_chart_deploy:
        type: boolean
      istio_gateway:
        $ref: '#/definitions/service.IstioGatewayInfo'
      product_name:
        type: string
      ready:
        description: deprecated
        type: string
      release_name:
        type: string
      revision:
        type: integer
      service_display_name:
        type: string
      service_name:
        type: string
      status:
        type: string
      type:
        type: string
      updatable:
        type: boolean
      workLoadType:
        type: string
      zadigx_release_tag:
        type: string
      zadigx_release_type:
        description: |-
          ZadigXReleaseType represents the service contain created by zadigx release workflow
          frontend should limit some operations on these services
        type: string
    type: object
  service.ServiceTmplObject:
    properties:
      env_configs:
        items:
          $ref: '#/definitions/models.EnvConfig'
        type: array
      env_name:
        type: string
      env_statuses:
        items:
          $ref: '#/definitions/models.EnvStatus'
        type: array
      from:
        type: string
      health_checks:
        items:
          $ref: '#/definitions/models.PmHealthCheck'
        type: array
      product_name:
        type: string
      restart_cmd:
        type: string
      revision:
        type: integer
      service_name:
        type: string
      service_variable_kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      start_cmd:
        type: string
      stop_cmd:
        type: string
      type:
        type: string
      username:
        type: string
      variable_yaml:
        type: string
      visibility:
        type: string
    type: object
  service.ServiceTmplResp:
    properties:
      data:
        items:
          $ref: '#/definitions/service.ServiceProductMap'
        type: array
      total:
        type: integer
    type: object
  service.SetupPortalServiceRequest:
    properties:
      host:
        type: string
      port_number:
        type: integer
      port_protocol:
        type: string
    type: object
  service.SharedNSEnvs:
    properties:
      env_name:
        type: string
      production:
        type: boolean
      project_name:
        type: string
    type: object
  service.Sprint:
    properties:
      create_time:
        type: integer
      created_by:
        $ref: '#/definitions/types.UserBriefInfo'
      id:
        type: string
      is_archived:
        type: boolean
      is_deleted:
        type: boolean
      name:
        type: string
      stages:
        items:
          $ref: '#/definitions/service.SprintStage'
        type: array
      update_time:
        type: integer
      updated_by:
        $ref: '#/definitions/types.UserBriefInfo'
    type: object
  service.SprintStage:
    properties:
      id:
        type: string
      name:
        type: string
      workflows:
        items:
          $ref: '#/definitions/models.SprintWorkflow'
        type: array
      workitems:
        items:
          $ref: '#/definitions/service.SprintWorkitem'
        type: array
    type: object
  service.SprintWorkItem:
    properties:
      activities:
        items:
          $ref: '#/definitions/models.SprintWorkItemActivity'
        type: array
      create_time:
        type: integer
      description:
        type: string
      id:
        type: string
      owners:
        items:
          $ref: '#/definitions/types.UserBriefInfo'
        type: array
      stage_id:
        type: string
      title:
        type: string
      update_time:
        type: integer
    type: object
  service.SprintWorkitem:
    properties:
      create_time:
        type: integer
      created_by:
        $ref: '#/definitions/types.UserBriefInfo'
      id:
        type: string
      owners:
        items:
          $ref: '#/definitions/types.UserBriefInfo'
        type: array
      sprint_id:
        type: string
      sprint_name:
        type: string
      stage_id:
        type: string
      stage_index:
        type: integer
      title:
        type: string
      update_time:
        type: integer
      updated_by:
        $ref: '#/definitions/types.UserBriefInfo'
    type: object
  service.SvcDiffResult:
    properties:
      chart_name:
        type: string
      current:
        $ref: '#/definitions/service.TmplYaml'
      deployed_from_chart:
        type: boolean
      error:
        type: string
      latest:
        $ref: '#/definitions/service.TmplYaml'
      release_name:
        type: string
      service_name:
        type: string
    type: object
  service.SvcResources:
    properties:
      kind:
        type: string
      name:
        type: string
    type: object
  service.SvcRevision:
    properties:
      containers:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      current_revision:
        type: integer
      deleted:
        type: boolean
      deploy_strategy:
        type: string
      error:
        type: string
      new:
        type: boolean
      next_revision:
        type: integer
      service_name:
        type: string
      type:
        type: string
      updatable:
        type: boolean
      update_service_tmpl:
        type: boolean
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      variable_yaml:
        type: string
    type: object
  service.SyncCollaborationInstanceArgs:
    properties:
      products:
        items:
          $ref: '#/definitions/service.Product'
        type: array
    type: object
  service.TemplateSvcResp:
    properties:
      auto_sync:
        type: boolean
      branch_name:
        type: string
      build_name:
        type: string
      codehost_id:
        type: integer
      commit:
        $ref: '#/definitions/models.Commit'
      containers:
        items:
          $ref: '#/definitions/models.Container'
        type: array
      create_by:
        type: string
      create_from: {}
      create_time:
        type: integer
      deploy_time:
        type: integer
      description:
        type: string
      env_configs:
        items:
          $ref: '#/definitions/models.EnvConfig'
        type: array
      env_name:
        type: string
      env_statuses:
        items:
          $ref: '#/definitions/models.EnvStatus'
        type: array
      gerrit_branch_name:
        type: string
      gerrit_codeHost_id:
        type: integer
      gerrit_path:
        type: string
      gerrit_remote_name:
        type: string
      gerrit_repo_name:
        type: string
      gitee_path:
        type: string
      gui_config:
        $ref: '#/definitions/models.GUIConfig'
      hash256:
        type: string
      health_checks:
        items:
          $ref: '#/definitions/models.PmHealthCheck'
        type: array
      helm_chart:
        $ref: '#/definitions/models.HelmChart'
      is_dir:
        type: boolean
      load_path:
        type: string
      product_name:
        type: string
      release_naming:
        type: string
      repo_name:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      repo_uuid:
        type: string
      resources:
        items:
          $ref: '#/definitions/service.SvcResources'
        type: array
      restart_cmd:
        type: string
      revision:
        type: integer
      service_name:
        type: string
      service_variable_kvs:
        description: New since 1.18.0, stores the variable kvs of k8s services
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      service_vars:
        description: DEPRECATED, New since 1.16.0, stores keys in variables which
          can be set in env
        items:
          type: string
        type: array
      source:
        type: string
      src_path:
        type: string
      start_cmd:
        type: string
      status:
        type: string
      stop_cmd:
        type: string
      team:
        type: string
      template_id:
        type: string
      type:
        type: string
      variable_yaml:
        description: New since 1.16.0, stores the variable yaml of k8s services
        type: string
      visibility:
        description: DEPRECATED since 1.17.0
        type: string
      workload_type:
        description: WorkloadType is set in host projects
        type: string
      yaml:
        type: string
    type: object
  service.TmplYaml:
    properties:
      revision:
        type: integer
      update_by:
        type: string
      yaml:
        type: string
    type: object
  service.UpdateEnv:
    properties:
      comment:
        type: string
      env_name:
        type: string
      services:
        items:
          $ref: '#/definitions/service.UpdateServiceArg'
        type: array
    type: object
  service.UpdateMultiHelmProductArg:
    properties:
      chartValues:
        items:
          $ref: '#/definitions/service.HelmSvcRenderArg'
        type: array
      comment:
        type: string
      deletedServices:
        items:
          type: string
        type: array
      envNames:
        items:
          type: string
        type: array
      productName:
        type: string
      replacePolicy:
        description: TODO logic not implemented
        type: string
    type: object
  service.UpdateServiceArg:
    properties:
      deploy_strategy:
        type: string
      service_name:
        type: string
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
    type: object
  service.UpdateWorkloadsArgs:
    properties:
      cluster_id:
        type: string
      namespace:
        type: string
      workLoads:
        items:
          $ref: '#/definitions/models.Workload'
        type: array
    type: object
  service.ValuesDataArgs:
    properties:
      autoSync:
        type: boolean
      gitRepoConfig:
        $ref: '#/definitions/service.RepoConfig'
      source_id:
        type: string
      yamlSource:
        type: string
    type: object
  service.YamlValidatorReq:
    properties:
      service_name:
        type: string
      variable_yaml:
        type: string
      yaml:
        type: string
    type: object
  service.dashboardBuild:
    properties:
      data:
        items:
          $ref: '#/definitions/service.dashboardBuildDaily'
        type: array
      success:
        type: integer
      total:
        type: integer
    type: object
  service.dashboardBuildDaily:
    properties:
      date:
        type: string
      failure:
        type: integer
      success:
        type: integer
      total:
        type: integer
    type: object
  service.testDashboard:
    properties:
      average_duration:
        type: integer
      success:
        type: integer
      total_case_count:
        type: integer
      total_exec_count:
        type: integer
    type: object
  setting.ContainerType:
    enum:
    - init
    - ""
    type: string
    x-enum-varnames:
    - ContainerTypeInit
    - ContainerTypeNormal
  setting.IntegrationLevel:
    enum:
    - system
    - project
    type: string
    x-enum-varnames:
    - IntegrationLevelSystem
    - IntegrationLevelProject
  setting.K8SClusterStatus:
    enum:
    - disconnected
    - pending
    - normal
    - abnormal
    type: string
    x-enum-varnames:
    - Disconnected
    - Pending
    - Normal
    - Abnormal
  setting.NotifyWebHookType:
    enum:
    - dingding
    - feishu
    - feishu_person
    - feishu_app
    - wechat
    - msteams
    - mail
    - webhook
    type: string
    x-enum-varnames:
    - NotifyWebHookTypeDingDing
    - NotifyWebHookTypeFeishu
    - NotifyWebHookTypeFeishuPerson
    - NotifyWebhookTypeFeishuApp
    - NotifyWebHookTypeWechatWork
    - NotifyWebHookTypeMSTeam
    - NotifyWebHookTypeMail
    - NotifyWebHookTypeWebook
  setting.PMHostStatus:
    enum:
    - normal
    - abnormal
    type: string
    x-enum-varnames:
    - PMHostStatusNormal
    - PMHostStatusAbnormal
  setting.Request:
    enum:
    - high
    - medium
    - low
    - min
    - default
    - define
    type: string
    x-enum-varnames:
    - HighRequest
    - MediumRequest
    - LowRequest
    - MinRequest
    - DefaultRequest
    - DefineRequest
  setting.RequestSpec:
    properties:
      cpu_limit:
        type: integer
      cpu_req:
        type: integer
      gpu_limit:
        description: 'gpu request, eg: "nvidia.com/gpu: 1"'
        type: string
      memory_limit:
        type: integer
      memory_req:
        type: integer
    type: object
  setting.SQLExecStatus:
    enum:
    - success
    - failed
    - not_exec
    type: string
    x-enum-varnames:
    - SQLExecStatusSuccess
    - SQLExecStatusFailed
    - SQLExecStatusNotExec
  setting.SprintWorkItemActivityType:
    enum:
    - event
    - comment
    type: string
    x-enum-varnames:
    - SprintWorkItemActivityTypeEvent
    - SprintWorkItemActivityTypeComment
  setting.WorkflowCategory:
    enum:
    - ""
    - release
    type: string
    x-enum-varnames:
    - CustomWorkflow
    - ReleaseWorkflow
  systemconfig.CodeHost:
    properties:
      access_token:
        type: string
      address:
        type: string
      alias:
        type: string
      application_id:
        description: the field and tag not consistent because of db field
        type: string
      auth_type:
        $ref: '#/definitions/types.AuthType'
      client_secret:
        type: string
      enable_proxy:
        description: the field determine whether the proxy is enabled
        type: boolean
      id:
        type: integer
      namespace:
        type: string
      password:
        type: string
      perforce_host:
        description: perforce Type parameters
        type: string
      perforce_port:
        type: integer
      private_access_token:
        type: string
      refresh_token:
        type: string
      region:
        type: string
      ssh_key:
        type: string
      type:
        type: string
      updated_at:
        type: integer
      username:
        type: string
    type: object
  template.AutoDeployPolicy:
    properties:
      enable:
        type: boolean
    type: object
  template.CustomRule:
    properties:
      branch_rule:
        type: string
      commit_rule:
        type: string
      jenkins_rule:
        type: string
      pr_and_branch_rule:
        type: string
      pr_rule:
        type: string
      tag_rule:
        type: string
    type: object
  template.CustomYaml:
    properties:
      auto_sync:
        type: boolean
      auto_sync_yaml:
        type: string
      render_variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      source:
        type: string
      source_detail: {}
      source_id:
        type: string
      yaml_content:
        description: |-
          helm:
          - in service: user override's values in advance setting
          - in env: user input's override yaml
          k8s: variable yaml
        type: string
    type: object
  template.DeliveryVersionHook:
    properties:
      enable:
        type: boolean
      hook_host:
        type: string
      path:
        type: string
    type: object
  template.EnvRenderKV:
    properties:
      env_name:
        type: string
      vars:
        items:
          $ref: '#/definitions/template.RenderKV'
        type: array
    type: object
  template.GitRepoConfig:
    properties:
      branch:
        type: string
      codehost_id:
        type: integer
      namespace:
        description: records the actual namespace of repo, used to generate correct
          project name
        type: string
      owner:
        type: string
      repo:
        type: string
      values_paths:
        items:
          type: string
        type: array
    type: object
  template.ImageSearchingRule:
    properties:
      image:
        type: string
      inUse:
        type: boolean
      namespace:
        type: string
      presetId:
        type: integer
      repo:
        type: string
      tag:
        type: string
    type: object
  template.Product:
    properties:
      admins:
        description: created after 1.8.0, used to create default project admins
        items:
          type: string
        type: array
      auto_deploy:
        $ref: '#/definitions/template.AutoDeployPolicy'
      chart_infos:
        items:
          $ref: '#/definitions/template.ServiceRender'
        type: array
      ci_pipeline_id:
        description: CI场景的onboarding流程创建的ci工作流id，用于前端跳转
        type: string
      cluster_ids:
        items:
          type: string
        type: array
      create_time:
        type: integer
      custom_image_rule:
        $ref: '#/definitions/template.CustomRule'
      custom_tar_rule:
        $ref: '#/definitions/template.CustomRule'
      delivery_version_hook:
        $ref: '#/definitions/template.DeliveryVersionHook'
      desc:
        type: string
      enabled:
        type: boolean
      env_vars:
        items:
          $ref: '#/definitions/template.EnvRenderKV'
        type: array
      global_variables:
        description: New since 1.18.0 used to store global variables for test services
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      group_name:
        type: string
      image_searching_rules:
        items:
          $ref: '#/definitions/template.ImageSearchingRule'
        type: array
      is_opensource:
        type: boolean
      latest_build_update_by:
        type: string
      latest_build_update_time:
        type: integer
      latest_env_update_by:
        type: string
      latest_env_update_time:
        type: integer
      latest_service_update_by:
        type: string
      latest_service_update_time:
        type: integer
      latest_test_update_by:
        type: string
      latest_test_update_time:
        type: integer
      latest_workflow_update_by:
        type: string
      latest_workflow_update_time:
        type: integer
      onboarding_status:
        description: onboarding状态，0表示onboarding完成，1、2、3、4代表当前onboarding所在的步骤
        type: integer
      permissionUUIDs:
        items:
          type: string
        type: array
      product_feature:
        $ref: '#/definitions/template.ProductFeature'
      product_name:
        type: string
      production_global_variables:
        description: New since 1.18.0 used to store global variables for production
          services
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      production_services:
        items:
          items:
            type: string
          type: array
        type: array
      project_name:
        type: string
      project_name_pinyin:
        type: string
      project_name_pinyin_first_letter:
        type: string
      public:
        type: boolean
      revision:
        type: integer
      role:
        type: string
      services:
        items:
          items:
            type: string
          type: array
        type: array
      shared_services:
        description: Deprecated since 1.17
        items:
          $ref: '#/definitions/template.ServiceInfo'
        type: array
      timeout:
        type: integer
      total_build_num:
        type: integer
      total_env_num:
        type: integer
      total_env_template_service_num:
        type: integer
      total_service_num:
        type: integer
      total_test_num:
        type: integer
      total_workflow_num:
        type: integer
      update_by:
        type: string
      update_time:
        type: integer
      vars:
        description: Deprecated since 1.17
        items:
          $ref: '#/definitions/template.RenderKV'
        type: array
      visibility:
        type: string
    type: object
  template.ProductFeature:
    properties:
      app_type:
        type: string
      basic_facility:
        description: 基础设施，kubernetes 或者 cloud_host
        type: string
      create_env_type:
        description: 创建环境方式,system/external(系统创建/外部环境)
        type: string
      deploy_type:
        description: 部署方式，basic_facility=kubernetes时填写，k8s 或者 helm
        type: string
    type: object
  template.RenderKV:
    properties:
      alias:
        type: string
      key:
        type: string
      services:
        items:
          type: string
        type: array
      state:
        type: string
      value:
        type: string
    type: object
  template.ServiceInfo:
    properties:
      name:
        type: string
      owner:
        type: string
    type: object
  template.ServiceRender:
    properties:
      chart_name:
        type: string
      chart_repo:
        description: '---- for helm services begin ----'
        type: string
      chart_version:
        type: string
      is_helm_chart_deploy:
        type: boolean
      override_values:
        description: ValuesYaml     string `bson:"values_yaml,omitempty"     json:"values_yaml,omitempty"`       //
          full helm service values yaml, only record, not actually used in calculation
        type: string
      override_yaml:
        allOf:
        - $ref: '#/definitions/template.CustomYaml'
        description: OverrideYaml will be used in both helm and k8s projects
      release_name:
        type: string
      service_name:
        type: string
    type: object
  template.YamlDetail:
    properties:
      content:
        type: string
      id:
        type: string
      name:
        type: string
      service_variable_kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      variable_yaml:
        type: string
    type: object
  template.YamlTemplate:
    properties:
      content:
        type: string
      name:
        type: string
      service_variable_kvs:
        items:
          $ref: '#/definitions/types.ServiceVariableKV'
        type: array
      variable_yaml:
        type: string
    type: object
  types.AuthType:
    enum:
    - SSH
    - PrivateAccessToken
    type: string
    x-enum-varnames:
    - SSHAuthType
    - PrivateAccessTokenAuthType
  types.Cache:
    properties:
      medium_type:
        $ref: '#/definitions/types.MediumType'
      nfs_properties:
        $ref: '#/definitions/types.NFSProperties'
      object_properties:
        $ref: '#/definitions/types.ObjectProperties'
    type: object
  types.CacheDirType:
    enum:
    - workspace
    - user_defined
    type: string
    x-enum-varnames:
    - WorkspaceCacheDir
    - UserDefinedCacheDir
  types.FileInfo:
    properties:
      is_dir:
        description: abbreviation for Mode().IsDir()
        type: boolean
      mod_time:
        description: modification time
        type: integer
      mode:
        allOf:
        - $ref: '#/definitions/os.FileMode'
        description: file mode bits
      name:
        description: base name of the file
        type: string
      parent:
        description: parent path of the file
        type: string
      size:
        description: length in bytes for regular files; system-dependent for others
        type: integer
    type: object
  types.GlobalVariableKV:
    properties:
      desc:
        type: string
      key:
        type: string
      options:
        items:
          type: string
        type: array
      related_services:
        items:
          type: string
        type: array
      type:
        $ref: '#/definitions/types.ServiceVariableKVType'
      value: {}
    type: object
  types.JenkinsBuildParam:
    properties:
      auto_generate:
        type: boolean
      choice_option:
        items:
          type: string
        type: array
      choice_value:
        items:
          type: string
        type: array
      name:
        type: string
      type:
        $ref: '#/definitions/types.JenkinsParamType'
      value: {}
    type: object
  types.JenkinsParamType:
    enum:
    - string
    - choice
    type: string
    x-enum-varnames:
    - Str
    - Choice
  types.MediumType:
    enum:
    - object
    - nfs
    type: string
    x-enum-varnames:
    - ObjectMedium
    - NFSMedium
  types.NFSProperties:
    properties:
      provision_type:
        $ref: '#/definitions/types.ProvisionType'
      pvc:
        type: string
      storage_class:
        type: string
      storage_size_in_gib:
        type: integer
      subpath:
        type: string
    type: object
  types.ObjectProperties:
    properties:
      id:
        type: string
    type: object
  types.ObjectStoragePathDetail:
    properties:
      abs_file_path:
        type: string
      dest_path:
        type: string
      file_path:
        type: string
    type: object
  types.OpenAPIContainer:
    properties:
      image:
        description: 完整镜像地址
        type: string
      image_name:
        description: 镜像名称
        type: string
      name:
        description: 容器名称
        type: string
      type:
        allOf:
        - $ref: '#/definitions/setting.ContainerType'
        description: 容器类型
    required:
    - image
    - image_name
    - name
    - type
    type: object
  types.OpenAPIEnvService:
    properties:
      containers:
        description: 镜像信息
        items:
          $ref: '#/definitions/types.OpenAPIContainer'
        type: array
      override_values:
        description: 覆盖的键值对，内容格式为json，仅用于helm和helm chart类型服务
        type: string
      release_name:
        description: release名称，用于helm chart类型服务
        type: string
      rendered_yaml:
        description: 渲染后的yaml，仅用于k8s类型服务
        type: string
      service_name:
        description: 服务名称
        type: string
      update_time:
        description: 服务更新时间
        type: integer
      values_yaml:
        description: values内容，仅用于helm和helm chart类型服务
        type: string
    required:
    - containers
    - release_name
    - service_name
    - update_time
    type: object
  types.OpenAPIRollBackStat:
    properties:
      create_by:
        allOf:
        - $ref: '#/definitions/types.OpenAPIUserBriefInfo'
        description: 创建者信息
      create_time:
        description: 创建时间
        type: integer
      env_name:
        description: 环境名称
        type: string
      env_type:
        allOf:
        - $ref: '#/definitions/config.EnvType'
        description: 环境类型
      operation_type:
        allOf:
        - $ref: '#/definitions/config.EnvOperationType'
        description: 操作类型
      origin_sae_app:
        allOf:
        - $ref: '#/definitions/types.OpenAPISaeApplication'
        description: 回滚之前的sae应用信息
      origin_service:
        allOf:
        - $ref: '#/definitions/types.OpenAPIEnvService'
        description: 回滚之前的服务信息
      production:
        description: 是否是生产环境
        type: boolean
      project_key:
        description: 项目标识
        type: string
      service_name:
        description: 服务名称或应用名称
        type: string
      service_type:
        allOf:
        - $ref: '#/definitions/config.ServiceType'
        description: 服务类型
      update_sae_app:
        allOf:
        - $ref: '#/definitions/types.OpenAPISaeApplication'
        description: 回滚之后的sae应用信息
      update_service:
        allOf:
        - $ref: '#/definitions/types.OpenAPIEnvService'
        description: 回滚之后的服务信息
    required:
    - create_by
    - create_time
    - env_name
    - env_type
    - operation_type
    - production
    - project_key
    - service_name
    - service_type
    type: object
  types.OpenAPISaeApplication:
    properties:
      app_id:
        description: 应用ID
        type: string
      app_name:
        description: 应用名称
        type: string
      image_url:
        description: 镜像地址
        type: string
      instances:
        description: 实例数
        type: integer
      package_url:
        description: 包地址
        type: string
    required:
    - app_id
    - app_name
    - image_url
    - instances
    - package_url
    type: object
  types.OpenAPIUserBriefInfo:
    properties:
      account:
        description: 用户账号 (登陆名)
        type: string
      identity_type:
        description: 用户身份类型
        type: string
      name:
        description: 用户名称 (昵称)
        type: string
      uid:
        description: 用户ID
        type: string
    required:
    - account
    - identity_type
    - name
    - uid
    type: object
  types.ProvisionType:
    enum:
    - dynamic
    - static
    type: string
    x-enum-varnames:
    - DynamicProvision
    - StaticProvision
  types.RenderVariableKV:
    properties:
      desc:
        type: string
      is_reference_variable:
        type: boolean
      key:
        type: string
      options:
        items:
          type: string
        type: array
      type:
        $ref: '#/definitions/types.ServiceVariableKVType'
      use_global_variable:
        type: boolean
      value: {}
    type: object
  types.RepoSource:
    enum:
    - ""
    - param
    - job
    type: string
    x-enum-varnames:
    - RepoSourceRuntime
    - RepoSourceParam
    - RepoSourceJob
  types.Repository:
    properties:
      address:
        type: string
      auth_type:
        allOf:
        - $ref: '#/definitions/types.AuthType'
        description: The address of the code base input of the other type
      author_name:
        type: string
      branch:
        type: string
      changelist_id:
        type: integer
      checkout_path:
        type: string
      checkout_ref:
        type: string
      codehost_id:
        type: integer
      commit_id:
        type: string
      commit_message:
        type: string
      depot_type:
        description: perforce settings
        type: string
      enable_commit:
        description: EnableCommit marks if the pull uses a commit instead of branch/pr
        type: boolean
      enable_proxy:
        description: Now EnableProxy is not something we store. We decide this on
          runtime
        type: boolean
      filter_regexp:
        description: FilterRegexp is the regular expression filter for the branches
          and tags
        type: string
      hidden:
        description: Hidden defines whether the frontend needs to hide this repo
        type: boolean
      is_primary:
        description: IsPrimary used to generated image and package name, each build
          has one primary repo
        type: boolean
      job_name:
        type: string
      oauth_token:
        description: add
        type: string
      param_name:
        type: string
      password:
        type: string
      perforce_host:
        description: perforce host & port
        type: string
      perforce_port:
        type: integer
      pr:
        type: integer
      private_access_token:
        type: string
      prs:
        items:
          type: integer
        type: array
      remote_name:
        type: string
      repo_index:
        type: integer
      repo_name:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      service_module:
        type: string
      service_name:
        type: string
      shelve_id:
        type: integer
      source:
        type: string
      source_from:
        allOf:
        - $ref: '#/definitions/types.RepoSource'
        description: repo can come from params or other job, introduced in 1.3.1
      ssh_key:
        type: string
      stream:
        description: Stream is used for stream type depot
        type: string
      submission_id:
        type: string
      submodules:
        type: boolean
      tag:
        type: string
      use_default:
        description: UseDefault defines if the repo can be configured in start pipeline
          task page
        type: boolean
      username:
        description: username/password authorization for git/perforce
        type: string
      view_mapping:
        description: ViewMapping is used for local type depot
        type: string
    type: object
  types.RequestBodyType:
    enum:
    - json
    - yaml
    type: string
    x-enum-varnames:
    - RequestBodyTypeJSON
    - RequestBodyTypeYAML
  types.ScriptType:
    enum:
    - shell
    - batch_file
    - powershell
    type: string
    x-enum-varnames:
    - ScriptTypeShell
    - ScriptTypeBatchFile
    - ScriptTypePowerShell
  types.ServiceVariableKV:
    properties:
      desc:
        type: string
      key:
        type: string
      options:
        items:
          type: string
        type: array
      type:
        $ref: '#/definitions/types.ServiceVariableKVType'
      value: {}
    type: object
  types.ServiceVariableKVType:
    enum:
    - bool
    - string
    - enum
    - yaml
    type: string
    x-enum-varnames:
    - ServiceVariableKVTypeBoolean
    - ServiceVariableKVTypeString
    - ServiceVariableKVTypeEnum
    - ServiceVariableKVTypeYaml
  types.ServiceWithVariable:
    properties:
      service_name:
        type: string
      variable_kvs:
        items:
          $ref: '#/definitions/types.RenderVariableKV'
        type: array
      variable_yaml:
        type: string
    type: object
  types.ShareStorage:
    properties:
      medium_type:
        $ref: '#/definitions/types.MediumType'
      nfs_properties:
        $ref: '#/definitions/types.NFSProperties'
    type: object
  types.UserBriefInfo:
    properties:
      account:
        type: string
      identity_type:
        type: string
      name:
        type: string
      uid:
        type: string
    type: object
  util.KeyValue:
    properties:
      key:
        type: string
      value: {}
    type: object
  v1.NodeSelectorOperator:
    enum:
    - In
    - NotIn
    - Exists
    - DoesNotExist
    - Gt
    - Lt
    type: string
    x-enum-varnames:
    - NodeSelectorOpIn
    - NodeSelectorOpNotIn
    - NodeSelectorOpExists
    - NodeSelectorOpDoesNotExist
    - NodeSelectorOpGt
    - NodeSelectorOpLt
  webhooknotify.WebHookNotify:
    properties:
      event:
        $ref: '#/definitions/webhooknotify.WebHookNotifyEvent'
      object_kind:
        $ref: '#/definitions/webhooknotify.WebHookNotifyObjectKind'
      workflow:
        $ref: '#/definitions/webhooknotify.WorkflowNotify'
    type: object
  webhooknotify.WebHookNotifyEvent:
    enum:
    - workflow
    type: string
    x-enum-varnames:
    - WebHookNotifyEventWorkflow
  webhooknotify.WebHookNotifyObjectKind:
    enum:
    - workflow
    type: string
    x-enum-varnames:
    - WebHookNotifyObjectKindWorkflow
  webhooknotify.WorkflowNotify:
    properties:
      create_time:
        type: integer
      detail_url:
        type: string
      end_time:
        type: integer
      error:
        type: string
      project_display_name:
        type: string
      project_name:
        type: string
      remark:
        type: string
      stages:
        items:
          $ref: '#/definitions/webhooknotify.WorkflowNotifyStage'
        type: array
      start_time:
        type: integer
      status:
        $ref: '#/definitions/config.Status'
      task_creator:
        type: string
      task_creator_email:
        type: string
      task_creator_id:
        type: string
      task_creator_phone:
        type: string
      task_id:
        type: integer
      task_type:
        $ref: '#/definitions/config.CustomWorkflowTaskType'
      workflow_display_name:
        type: string
      workflow_name:
        type: string
    type: object
  webhooknotify.WorkflowNotifyDeployServiceModule:
    properties:
      image:
        type: string
      service_module:
        type: string
    type: object
  webhooknotify.WorkflowNotifyJobTask:
    properties:
      display_name:
        type: string
      end_time:
        type: integer
      error:
        type: string
      name:
        type: string
      spec: {}
      start_time:
        type: integer
      status:
        $ref: '#/definitions/config.Status'
      type:
        type: string
    type: object
  webhooknotify.WorkflowNotifyJobTaskBuildSpec:
    properties:
      image:
        type: string
      repositories:
        items:
          $ref: '#/definitions/webhooknotify.WorkflowNotifyRepository'
        type: array
    type: object
  webhooknotify.WorkflowNotifyJobTaskDeploySpec:
    properties:
      env:
        type: string
      service_modules:
        items:
          $ref: '#/definitions/webhooknotify.WorkflowNotifyDeployServiceModule'
        type: array
      service_name:
        type: string
    type: object
  webhooknotify.WorkflowNotifyRepository:
    properties:
      branch:
        type: string
      commit_id:
        type: string
      commit_message:
        type: string
      commit_url:
        type: string
      prs:
        items:
          type: integer
        type: array
      repo_name:
        type: string
      repo_namespace:
        type: string
      repo_owner:
        type: string
      source:
        type: string
      tag:
        type: string
    type: object
  webhooknotify.WorkflowNotifyStage:
    properties:
      end_time:
        type: integer
      error:
        type: string
      jobs:
        items:
          $ref: '#/definitions/webhooknotify.WorkflowNotifyJobTask'
        type: array
      name:
        type: string
      start_time:
        type: integer
      status:
        $ref: '#/definitions/config.Status'
    type: object
  workflow.HelmDeployJobMergeImageResponse:
    properties:
      values:
        type: string
    type: object
  workflow.ManualExecWorkflowTaskV4Request:
    properties:
      jobs:
        items:
          $ref: '#/definitions/models.Job'
        type: array
    type: object
  workflow.TaskInfo:
    properties:
      create_time:
        type: integer
      end_time:
        type: integer
      pipelineName:
        type: string
      running_time:
        type: integer
      start_time:
        type: integer
      status:
        type: string
      task_creator:
        type: string
      taskID:
        type: integer
    type: object
  workflow.Workflow:
    properties:
      averageExecutionTime:
        type: number
      base_name:
        type: string
      base_refs:
        items:
          type: string
        type: array
      createTime:
        type: integer
      description:
        type: string
      disabled:
        type: boolean
      display_name:
        type: string
      enable_approval_ticket:
        type: boolean
      enabledStages:
        items:
          type: string
        type: array
      isFavorite:
        type: boolean
      name:
        type: string
      never_run:
        type: boolean
      projectName:
        type: string
      recentFailedTask:
        $ref: '#/definitions/workflow.TaskInfo'
      recentSuccessfulTask:
        $ref: '#/definitions/workflow.TaskInfo'
      recentTask:
        $ref: '#/definitions/workflow.TaskInfo'
      recentTasks:
        items:
          $ref: '#/definitions/workflow.TaskInfo'
        type: array
      schedulerEnabled:
        type: boolean
      schedules:
        $ref: '#/definitions/models.ScheduleCtrl'
      successRate:
        type: number
      updateBy:
        type: string
      updateTime:
        type: integer
      workflow_type:
        type: string
    type: object
  workwx.ApprovalNode:
    properties:
      apv_rel:
        $ref: '#/definitions/workwx.ApprovalRel'
      status:
        $ref: '#/definitions/workwx.ApprovalNodeStatus'
      sub_nodes:
        items:
          $ref: '#/definitions/workwx.ApprovalSubNode'
        type: array
      type:
        $ref: '#/definitions/workwx.ApprovalType'
      userid:
        items:
          type: string
        type: array
      users:
        items:
          $ref: '#/definitions/workwx.ApprovalUser'
        type: array
    type: object
  workwx.ApprovalNodeStatus:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 11
    - 12
    - 13
    - 14
    - 15
    type: integer
    x-enum-varnames:
    - ApprovalNodeStatusWaiting
    - ApprovalNodeStatusApproved
    - ApprovalNodeStatusRejected
    - ApprovalNodeStatusForwarded
    - ApprovalNodeStatusThrowBackForwarded
    - ApprovalNodeStatusAddApprover
    - ApprovalNodeStatusApprovedAndAddApprover
    - ApprovalNodeStatusProcessing
    - ApprovalNodeStatusMoved
  workwx.ApprovalRel:
    enum:
    - 1
    - 2
    type: integer
    x-enum-varnames:
    - ApprovalRelAnd
    - ApprovalRelOr
  workwx.ApprovalSubNode:
    properties:
      speech:
        type: string
      status:
        $ref: '#/definitions/workwx.ApprovalSubNodeStatus'
      timestamp:
        type: integer
      user_info:
        properties:
          user_id:
            type: string
        type: object
    type: object
  workwx.ApprovalSubNodeStatus:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 11
    - 12
    - 13
    - 14
    - 15
    type: integer
    x-enum-varnames:
    - ApprovalSubNodeStatusWaiting
    - ApprovalSubNodeStatusApproved
    - ApprovalSubNodeStatusRejected
    - ApprovalSubNodeStatusForwarded
    - ApprovalSubNodeStatusThrowBackForwarded
    - ApprovalSubNodeStatusAddApprover
    - ApprovalSubNodeStatusApprovedAndAddApprover
    - ApprovalSubNodeStatusProcessing
    - ApprovalSubNodeStatusMoved
  workwx.ApprovalType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-varnames:
    - ApprovalTypeApprove
    - ApprovalTypeCC
  workwx.ApprovalUser:
    properties:
      id:
        type: string
      name:
        type: string
    type: object
info:
  contact:
    email: <EMAIL>
  description: |-
    The API doc is targeting for Zadig developers rather than Zadig users.
    The majority of these APIs are not designed for public use, there is no guarantee on version compatibility.
    Please reach <NAME_EMAIL> before you decide to depend on these APIs directly.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  title: Zadig aslan service REST APIs
  version: "1.0"
paths:
  /api/aslan/build/build:
    delete:
      consumes:
      - application/json
      description: Delete a build module by name and project name
      parameters:
      - description: Build module name
        in: query
        name: name
        required: true
        type: string
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete Build Module
      tags:
      - build
    get:
      consumes:
      - application/json
      description: Retrieve a list of build modules with optional filters
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: Build module name
        in: query
        name: name
        type: string
      - description: Filter by targets
        in: query
        name: targets
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of build modules
          schema:
            items:
              $ref: '#/definitions/service.BuildResp'
            type: array
      summary: List Build Modules
      tags:
      - build
    post:
      consumes:
      - application/json
      description: Create a new build module
      parameters:
      - description: Build module configuration
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Build'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create Build Module
      tags:
      - build
    put:
      consumes:
      - application/json
      description: 如果仅需要更新服务和代码信息，则只需要更新target_repos字段
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Build'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 更新构建
      tags:
      - build
  /api/aslan/build/build/{name}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 项目标识
        in: query
        name: projectKey
        required: true
        type: string
      - description: 构建标识
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.Build'
      summary: 获取构建详情
      tags:
      - build
  /api/aslan/build/serviceModule:
    get:
      consumes:
      - application/json
      description: Retrieve a list of build modules grouped by service modules, optionally
        for a specific environment
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: Encrypted key for sensitive data
        in: query
        name: encryptedKey
        type: string
      - description: Environment name to filter services
        in: query
        name: envName
        type: string
      - description: Exclude Jenkins build modules
        in: query
        name: excludeJenkins
        type: boolean
      - description: Use latest service revision instead of environment's current
        in: query
        name: updateServiceRevision
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: List of service modules with their associated build modules
          schema:
            items:
              $ref: '#/definitions/service.ServiceModuleAndBuildResp'
            type: array
      summary: List Build Modules By Service Module
      tags:
      - build
  /api/aslan/check_ephemeral_containers:
    get:
      description: 检查指定项目环境下的临时容器支持
      parameters:
      - description: 项目名称
        in: query
        name: projectName
        required: true
        type: string
      - description: 环境名称
        in: query
        name: envName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      summary: 检查集群是否支持临时容器
      tags:
      - cluster
  /api/aslan/cluster/clusters:
    get:
      consumes:
      - application/json
      description: 获取当前用户有权限访问的集群列表，可按项目筛选
      parameters:
      - description: 项目名称，用于权限过滤
        in: query
        name: projectName
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.K8SCluster'
            type: array
      summary: 获取集群列表
      tags:
      - cluster
    post:
      consumes:
      - application/json
      description: 创建一个新的 Kubernetes 集群信息
      parameters:
      - description: 集群信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.K8SCluster'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.K8SCluster'
      summary: 创建集群
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}:
    delete:
      description: 根据 ID 删除集群，仅系统管理员或具有删除权限的用户可执行
      parameters:
      - description: Cluster ID
        in: path
        name: id
        required: true
        type: string
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: 删除集群
      tags:
      - cluster
    get:
      consumes:
      - application/json
      description: 根据集群 ID 获取集群详细信息
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.K8SCluster'
      summary: 获取指定集群信息
      tags:
      - cluster
    put:
      consumes:
      - application/json
      description: 更新指定集群的配置与信息
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: 更新后的集群信息
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.K8SCluster'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.K8SCluster'
      summary: 更新集群信息
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/agent/upgrade:
    put:
      description: 对指定集群的 Agent 执行升级操作，仅管理员或具有编辑权限的用户可执行
      parameters:
      - description: Cluster ID
        in: path
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      summary: 升级集群 Agent
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/cache:
    put:
      consumes:
      - application/json
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/types.Cache'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.K8SCluster'
      summary: 更新集群缓存
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/deletionInfo:
    get:
      consumes:
      - application/json
      description: 获取指定集群在删除前的依赖信息与提示
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ClusterDeletionInfo'
      summary: 获取集群删除提示信息
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/dind:
    put:
      consumes:
      - application/json
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.DindCfg'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.K8SCluster'
      summary: 更新集群Dind配置
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/disconnect:
    put:
      description: 断开指定集群的连接，仅系统管理员或有编辑权限的用户可执行
      parameters:
      - description: Cluster ID
        in: path
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      summary: 断开集群连接
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/reconnect:
    put:
      description: 尝试重新连接指定集群，仅系统管理员或有编辑权限的用户可执行
      parameters:
      - description: Cluster ID
        in: path
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      summary: 重连集群
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/references:
    get:
      description: 获取指定集群被哪些策略引用
      parameters:
      - description: Cluster ID
        in: path
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ClusterStrategyReference'
            type: array
      summary: 获取集群策略引用信息
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/storage:
    put:
      consumes:
      - application/json
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/types.ShareStorage'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.K8SCluster'
      summary: 更新集群共享存储
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/strategy:
    delete:
      consumes:
      - application/json
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: 调度策略ID
        in: query
        name: strategyID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.K8SCluster'
      summary: 删除集群调度策略
      tags:
      - cluster
    put:
      consumes:
      - application/json
      parameters:
      - description: 集群ID
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.ScheduleStrategy'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.K8SCluster'
      summary: 添加/更新集群调度策略
      tags:
      - cluster
  /api/aslan/cluster/clusters/{id}/yaml:
    get:
      description: 获取集群部署或连接所需的 YAML 文件
      parameters:
      - description: Cluster ID
        in: path
        name: id
        required: true
        type: string
      - description: YAML 类型（deploy/connect）
        in: query
        name: type
        type: string
      responses:
        "200":
          description: YAML 内容
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
      summary: 获取集群 YAML
      tags:
      - cluster
  /api/aslan/cluster/clusters/irsa_info:
    get:
      description: 获取集群对应的 IRSA（IAM Role for Service Account）配置信息，仅管理员或有查看权限的用户可执行
      parameters:
      - description: Cluster ID
        in: query
        name: id
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
      summary: 获取集群 IRSA 信息
      tags:
      - cluster
  /api/aslan/code/codehost:
    get:
      consumes:
      - application/json
      description: Retrieves a list of all configured code hosts with masked sensitive
        information
      produces:
      - application/json
      responses:
        "200":
          description: List of code hosts
          schema:
            items:
              $ref: '#/definitions/systemconfig.CodeHost'
            type: array
      summary: List Code Hosts
      tags:
      - codehost
  /api/aslan/code/codehost/{codehostId}/branches:
    get:
      consumes:
      - application/json
      description: Retrieves a list of branches from the specified repository
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Repository owner/namespace
        in: query
        name: repoOwner
        required: true
        type: string
      - description: Repository name
        in: query
        name: repoName
        required: true
        type: string
      - default: 100
        description: Number of results per page
        in: query
        name: per_page
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - description: Keyword to filter branches
        in: query
        name: key
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of branches
          schema:
            items:
              $ref: '#/definitions/client.Branch'
            type: array
      summary: List Repository Branches
      tags:
      - codehost
  /api/aslan/code/codehost/{codehostId}/branches/regular/check:
    post:
      consumes:
      - application/json
      description: Retrieves branches that match the specified regular expression
        pattern
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Branch matching request with regular expression
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.MatchBranchesListRequest'
      produces:
      - application/json
      responses:
        "200":
          description: List of matching branches
          schema:
            items:
              $ref: '#/definitions/client.Branch'
            type: array
      summary: Match Branches with Regular Expression
      tags:
      - codehost
  /api/aslan/code/codehost/{codehostId}/commits:
    get:
      consumes:
      - application/json
      description: Retrieves a list of commits from the specified repository branch
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Repository namespace
        in: query
        name: repoNamespace
        required: true
        type: string
      - description: Repository name
        in: query
        name: repoName
        required: true
        type: string
      - description: Branch name to get commits from
        in: query
        name: branchName
        type: string
      - default: 100
        description: Number of results per page
        in: query
        name: per_page
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of commits
          schema:
            items:
              $ref: '#/definitions/client.Commit'
            type: array
      summary: List Repository Commits
      tags:
      - codehost
  /api/aslan/code/codehost/{codehostId}/namespaces:
    get:
      consumes:
      - application/json
      description: Retrieves a list of namespaces from the specified code host
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Keyword to filter namespaces
        in: query
        name: key
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of namespaces
          schema:
            items:
              $ref: '#/definitions/client.Namespace'
            type: array
      summary: List Code Host Namespaces
      tags:
      - codehost
  /api/aslan/code/codehost/{codehostId}/projects:
    get:
      consumes:
      - application/json
      description: Retrieves a list of projects from the specified code host namespace
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Repository owner/namespace
        in: query
        name: repoOwner
        required: true
        type: string
      - default: group
        description: Namespace type (user/group/org/enterprise)
        in: query
        name: type
        type: string
      - default: 30
        description: Number of results per page
        in: query
        name: per_page
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - description: Keyword to filter projects
        in: query
        name: key
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of projects
          schema:
            items:
              $ref: '#/definitions/client.Project'
            type: array
      summary: List Code Host Projects
      tags:
      - codehost
  /api/aslan/code/codehost/{codehostId}/prs:
    get:
      consumes:
      - application/json
      description: Retrieves a list of pull requests from the specified repository
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Repository owner/namespace
        in: query
        name: repoOwner
        required: true
        type: string
      - description: Repository name
        in: query
        name: repoName
        required: true
        type: string
      - description: Target branch to filter PRs
        in: query
        name: targetBranch
        type: string
      - default: 100
        description: Number of results per page
        in: query
        name: per_page
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - description: Keyword to filter pull requests
        in: query
        name: key
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of pull requests
          schema:
            items:
              $ref: '#/definitions/client.PullRequest'
            type: array
      summary: List Repository Pull Requests
      tags:
      - codehost
  /api/aslan/code/codehost/{codehostId}/tags:
    get:
      consumes:
      - application/json
      description: Retrieves a list of tags from the specified repository
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Repository owner/namespace
        in: query
        name: repoOwner
        required: true
        type: string
      - description: Repository name
        in: query
        name: repoName
        required: true
        type: string
      - default: 100
        description: Number of results per page
        in: query
        name: per_page
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - description: Keyword to filter tags
        in: query
        name: key
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of tags
          schema:
            items:
              $ref: '#/definitions/client.Tag'
            type: array
      summary: List Repository Tags
      tags:
      - codehost
  /api/aslan/code/codehost/infos:
    put:
      consumes:
      - application/json
      description: Retrieves detailed information for multiple repositories including
        branches, tags, and PRs
      parameters:
      - description: List of repository information requests
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.RepoInfoList'
      produces:
      - application/json
      responses:
        "200":
          description: List of repository information
          schema:
            items:
              $ref: '#/definitions/service.GitRepoInfo'
            type: array
      summary: List Repository Information
      tags:
      - codehost
  /api/aslan/code/workspace/file:
    get:
      consumes:
      - application/json
      description: Downloads a file from the pipeline workspace
      parameters:
      - description: Pipeline name
        in: query
        name: pipelineName
        required: true
        type: string
      - description: File path relative to workspace
        in: query
        name: file
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: File content
          schema:
            type: file
      summary: Get Workspace File
      tags:
      - workspace
  /api/aslan/code/workspace/getcontents/{codehostId}:
    get:
      consumes:
      - application/json
      description: Retrieves YAML file contents from a repository path, combining
        multiple YAML files if it's a directory
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Repository name
        in: query
        name: repoName
        required: true
        type: string
      - description: Repository owner
        in: query
        name: repoOwner
        type: string
      - description: Branch name
        in: query
        name: branchName
        type: string
      - description: File or directory path
        in: query
        name: path
        type: string
      - description: Whether the path is a directory
        in: query
        name: isDir
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Combined YAML content
          schema:
            type: string
      summary: Get Repository Contents
      tags:
      - workspace
  /api/aslan/code/workspace/git/{codehostId}:
    get:
      consumes:
      - application/json
      description: Retrieves file and directory information from a Git repository
      parameters:
      - description: Code host ID
        in: path
        name: codehostId
        required: true
        type: integer
      - description: Repository name
        in: query
        name: repoName
        required: true
        type: string
      - description: Branch name
        in: query
        name: branchName
        required: true
        type: string
      - description: Remote name
        in: query
        name: remoteName
        required: true
        type: string
      - description: Repository owner
        in: query
        name: repoOwner
        type: string
      - description: Repository namespace
        in: query
        name: repoNamespace
        type: string
      - default: '"/"'
        description: Directory path
        in: query
        name: dir
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of files and directories
          schema:
            items:
              $ref: '#/definitions/service.FileInfo'
            type: array
      summary: Get Git Repository Information
      tags:
      - workspace
  /api/aslan/code/workspace/tree:
    get:
      consumes:
      - application/json
      description: Retrieves the file tree structure of a repository or public repository
      parameters:
      - description: Code host ID (required if not using repoLink)
        in: query
        name: codehost_id
        type: integer
      - description: Repository owner (required if not using repoLink)
        in: query
        name: owner
        type: string
      - description: Repository namespace
        in: query
        name: namespace
        type: string
      - description: Repository name (required if not using repoLink)
        in: query
        name: repo
        type: string
      - default: '""'
        description: Path within repository
        in: query
        name: path
        type: string
      - description: Branch name (required if not using repoLink)
        in: query
        name: branch
        type: string
      - description: Public repository link (alternative to codehost_id/owner/repo)
        in: query
        name: repoLink
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Repository tree structure
          schema:
            items:
              $ref: '#/definitions/git.TreeNode'
            type: array
      summary: Get Repository Tree
      tags:
      - workspace
  /api/aslan/collaboration/collaborations/sync:
    post:
      consumes:
      - application/json
      description: Sync Collaboration Instance
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.SyncCollaborationInstanceArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Sync Collaboration Instance
      tags:
      - collaboration
  /api/aslan/delivery/releases/check:
    get:
      consumes:
      - application/json
      description: Check Delivery Version
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: version
        in: query
        name: version
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Check Delivery Version
      tags:
      - delivery
  /api/aslan/delivery/releases/k8s:
    post:
      consumes:
      - application/json
      description: Create K8S Delivery Version
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.CreateK8SDeliveryVersionArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create K8S Delivery Version
      tags:
      - delivery
  /api/aslan/environment/environments:
    get:
      consumes:
      - application/json
      description: Retrieve a list of envs
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of environments
          schema:
            items:
              $ref: '#/definitions/service.EnvResp'
            type: array
      summary: List Envs
      tags:
      - environment
    post:
      consumes:
      - application/json
      description: Create Product(environment)
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: type
        in: query
        name: type
        required: true
        type: string
      - description: env type
        in: query
        name: envType
        type: string
      - description: scene
        in: query
        name: scene
        type: string
      - description: is auto
        in: query
        name: auto
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          items:
            $ref: '#/definitions/service.CreateSingleProductArg'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create Product(environment)
      tags:
      - environment
    put:
      consumes:
      - application/json
      description: Update Multi products
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: type
        in: query
        name: type
        type: string
      - description: is force
        in: query
        name: force
        required: true
        type: boolean
      - description: updateMultiK8sEnv body
        in: body
        name: k8s_body
        required: true
        schema:
          items:
            $ref: '#/definitions/service.UpdateEnv'
          type: array
      - description: updateMultiHelmEnv body
        in: body
        name: helm_body
        required: true
        schema:
          $ref: '#/definitions/service.UpdateMultiHelmProductArg'
      - description: updateMultiCvmEnv body
        in: body
        name: pm_body
        required: true
        schema:
          items:
            $ref: '#/definitions/service.UpdateEnv'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update Multi products
      tags:
      - environment
  /api/aslan/environment/environments/:name/helm/releases:
    delete:
      consumes:
      - application/json
      description: Delete helm release from envrionment
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: release names
        in: query
        name: releaseNames
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete helm release from envrionment
      tags:
      - environment
  /api/aslan/environment/environments/{name}:
    delete:
      consumes:
      - application/json
      description: Delete Product
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: is delete
        in: query
        name: is_delete
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete Product
      tags:
      - environment
    get:
      consumes:
      - application/json
      description: Get Product
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/github_com_koderover_zadig_v2_pkg_microservice_aslan_core_environment_service.ProductResp'
      summary: Get Product
      tags:
      - environment
    put:
      consumes:
      - application/json
      description: Update product variables for PM products in the specified environment
      parameters:
      - description: Environment name
        in: path
        name: name
        required: true
        type: string
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: Product update parameters
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.UpdateProductParams'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Update Product Variables
      tags:
      - environment
  /api/aslan/environment/environments/{name}/analysis:
    post:
      consumes:
      - application/json
      description: Run environment Analysis
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.EnvAnalysisRespone'
      summary: Run environment Analysis
      tags:
      - environment
  /api/aslan/environment/environments/{name}/analysis/cron:
    get:
      consumes:
      - application/json
      description: Get Env Analysis Cron
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.EnvAnalysisCronArg'
      summary: Get Env Analysis Cron
      tags:
      - environment
    put:
      consumes:
      - application/json
      description: Upsert Env Analysis Cron
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.EnvAnalysisCronArg'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Upsert Env Analysis Cron
      tags:
      - environment
  /api/aslan/environment/environments/{name}/configs:
    get:
      consumes:
      - application/json
      description: Get environment configs
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.EnvConfigsArgs'
      summary: Get environment configs
      tags:
      - environment
    put:
      consumes:
      - application/json
      description: Update environment configs
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.EnvConfigsArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update environment configs
      tags:
      - environment
  /api/aslan/environment/environments/{name}/estimated-values:
    post:
      consumes:
      - application/json
      parameters:
      - description: 项目标识
        in: query
        name: projectName
        required: true
        type: string
      - description: 环境名称
        in: path
        name: name
        required: true
        type: string
      - description: 服务名称或release名称
        in: query
        name: serviceName
        required: true
        type: string
      - description: 使用场景
        enum:
        - create_env
        - create_service
        - update_service
        in: query
        name: scene
        required: true
        type: string
      - description: 返回格式
        enum:
        - yaml
        - flat_map
        in: query
        name: format
        required: true
        type: string
      - description: 是否是helm实例化部署
        in: query
        name: isHelmChartDeploy
        required: true
        type: boolean
      - description: 是否更新服务配置
        in: query
        name: updateServiceRevision
        required: true
        type: boolean
      - description: 是否为生产环境
        in: query
        name: production
        required: true
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.EstimateValuesArg'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.GetHelmValuesDifferenceResp'
      summary: 预览Helm服务环境变量
      tags:
      - environment
  /api/aslan/environment/environments/{name}/globalVariableCandidates:
    get:
      consumes:
      - application/json
      description: Get global variable candidates
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.ServiceVariableKV'
            type: array
      summary: Get global variable candidates
      tags:
      - environment
  /api/aslan/environment/environments/{name}/groups:
    get:
      consumes:
      - application/json
      description: Get paginated list of groups (e.g. Helm charts/services) in a specific
        environment
      parameters:
      - description: Environment name
        in: path
        name: name
        required: true
        type: string
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: Service name to filter
        in: query
        name: serviceName
        type: string
      - description: Whether it's a production environment
        in: query
        name: production
        type: boolean
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 20)'
        in: query
        name: per_page
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of groups in the environment
          schema:
            $ref: '#/definitions/service.ServiceResp'
      summary: Get service group list in environment
      tags:
      - environment
  /api/aslan/environment/environments/{name}/k8s/globalVariables:
    put:
      consumes:
      - application/json
      description: Update global variables
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.updateK8sProductGlobalVariablesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update global variables
      tags:
      - environment
  /api/aslan/environment/environments/{name}/services:
    get:
      consumes:
      - application/json
      description: List services in env
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.EnvServices'
      summary: List services in env
      tags:
      - environment
    put:
      consumes:
      - application/json
      description: Delete services from envrionment
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.DeleteProductServicesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete services
      tags:
      - environment
  /api/aslan/environment/environments/{name}/services/{serviceName}:
    put:
      consumes:
      - application/json
      description: Update service
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.SvcRevision'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update service
      tags:
      - environment
  /api/aslan/environment/environments/{name}/services/{serviceName}/execmd:
    post:
      consumes:
      - application/json
      description: Exec VM Service Command
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: host id
        in: query
        name: hostId
        required: true
        type: string
      - description: vm service command type
        enum:
        - start
        - stop
        - restart
        in: query
        name: commandType
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ExecVmServiceCommandResponse'
      summary: Exec VM Service Command
      tags:
      - environment
  /api/aslan/environment/environments/{name}/services/{serviceName}/preview:
    post:
      consumes:
      - application/json
      description: Preview service
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.PreviewServiceArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.SvcDiffResult'
      summary: Preview service
      tags:
      - environment
  /api/aslan/environment/environments/{name}/services/{serviceName}/yaml:
    get:
      consumes:
      - application/json
      description: Fetch Service Yaml
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.FetchServiceYamlResponse'
      summary: Fetch Service Yaml
      tags:
      - environment
  /api/aslan/environment/environments/{name}/share/portal/{serviceName}:
    get:
      consumes:
      - application/json
      description: Get Portal Service for Share Env
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.GetPortalServiceResponse'
      summary: Get Portal Service for Share Env
      tags:
      - environment
    post:
      consumes:
      - application/json
      description: Setup Portal Service for Share Env
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          items:
            $ref: '#/definitions/service.SetupPortalServiceRequest'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Setup Portal Service for Share Env
      tags:
      - environment
  /api/aslan/environment/environments/{name}/sleep:
    post:
      consumes:
      - application/json
      description: Environment Sleep
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: enable or disable
        in: query
        name: action
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Environment Sleep
      tags:
      - environment
  /api/aslan/environment/environments/{name}/sleep/cron:
    get:
      consumes:
      - application/json
      description: Get Env Sleep Cron
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.EnvSleepCronArg'
      summary: Get Env Sleep Cron
      tags:
      - environment
    put:
      consumes:
      - application/json
      description: Upsert Env Sleep Cron
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.EnvSleepCronArg'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Upsert Env Sleep Cron
      tags:
      - environment
  /api/aslan/environment/environments/{name}/version/{serviceName}:
    get:
      consumes:
      - application/json
      description: List Environment Service Versions
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: page
        in: query
        name: page
        type: integer
      - description: page size
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ListEnvServiceVersionsResponse'
            type: array
      summary: List Environment Service Versions
      tags:
      - environment
  /api/aslan/environment/environments/{name}/version/{serviceName}/diff:
    get:
      consumes:
      - application/json
      description: Diff Environment Service Versions
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name or release name when isHelmChart is true
        in: path
        name: serviceName
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: revision a
        in: query
        name: revisionA
        required: true
        type: integer
      - description: revision b
        in: query
        name: revisionB
        required: true
        type: integer
      - description: is helm chart type
        in: query
        name: isHelmChart
        required: true
        type: boolean
      - description: release name
        in: query
        name: releaseName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.DiffEnvServiceVersionsResponse'
      summary: Diff Environment Service Versions
      tags:
      - environment
  /api/aslan/environment/environments/{name}/version/{serviceName}/revision/{revision}:
    get:
      consumes:
      - application/json
      description: Get Environment Service Version Yaml
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name or release name when isHelmChart is true
        in: path
        name: serviceName
        required: true
        type: string
      - description: revision
        in: path
        name: revision
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: is helm chart type
        in: query
        name: isHelmChart
        required: true
        type: boolean
      - description: release name
        in: query
        name: releaseName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.GetEnvServiceVersionYamlResponse'
            type: array
      summary: Get Environment Service Version Yaml
      tags:
      - environment
  /api/aslan/environment/environments/{name}/version/{serviceName}/rollback:
    post:
      consumes:
      - application/json
      description: Rollback Environment Service Version
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name or release name when isHelmChart is true
        in: path
        name: serviceName
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: revision
        in: query
        name: revision
        required: true
        type: integer
      - description: is helm chart type
        in: query
        name: isHelmChart
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Rollback Environment Service Version
      tags:
      - environment
  /api/aslan/environment/environments/sae:
    get:
      consumes:
      - application/json
      description: List SAE Envs
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.EnvResp'
            type: array
      summary: List SAE Envs
      tags:
      - environment
    post:
      consumes:
      - application/json
      description: Create SAE Env
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SAEEnv'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create SAE Env
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}:
    delete:
      consumes:
      - application/json
      description: Delete SAE Env
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete SAE Env
      tags:
      - environment
    get:
      consumes:
      - application/json
      description: Get SAE Env
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SAEEnv'
      summary: Get SAE Env
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app:
    post:
      consumes:
      - application/json
      description: Add SAE App to Env
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.AddSAEAppToEnvRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Add SAE App to Env
      tags:
      - environment
    put:
      consumes:
      - application/json
      description: Delete SAE App from Env
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.DelSAEAppFromEnvRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete SAE App from Env
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app/{appID}/instance:
    get:
      consumes:
      - application/json
      description: List SAE Application Instances
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: app ID
        in: path
        name: appID
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.SAEAppGroup'
            type: array
      summary: List SAE Application Instances
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app/{appID}/instance/{instanceID}/log:
    get:
      consumes:
      - application/json
      description: Get SAE Application Instance Log
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: app ID
        in: path
        name: appID
        required: true
        type: string
      - description: instance ID
        in: path
        name: instanceID
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: string
      summary: Get SAE Application Instance Log
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app/{appID}/instance/{instanceID}/restart:
    post:
      consumes:
      - application/json
      description: Restart SAE Application Instance
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: app ID
        in: path
        name: appID
        required: true
        type: string
      - description: instance ID
        in: path
        name: instanceID
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Restart SAE Application Instance
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app/{appID}/rescale:
    post:
      consumes:
      - application/json
      description: Rescale SAE Application
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: app ID
        in: path
        name: appID
        required: true
        type: string
      - description: replicas
        in: query
        name: replicas
        required: true
        type: integer
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Rescale SAE Application
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app/{appID}/restart:
    post:
      consumes:
      - application/json
      description: Restart SAE Application
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: app ID
        in: path
        name: appID
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Restart SAE Application
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app/{appID}/rollback:
    post:
      consumes:
      - application/json
      description: Rollback SAE Application
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: app ID
        in: path
        name: appID
        required: true
        type: string
      - description: version ID
        in: query
        name: versionID
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Rollback SAE Application
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app/{appID}/serviceBind:
    post:
      consumes:
      - application/json
      parameters:
      - description: 项目标识
        in: query
        name: projectName
        required: true
        type: string
      - description: 是否生产环境，目前必须为true
        in: query
        name: production
        required: true
        type: string
      - description: 环境名称
        in: path
        name: name
        required: true
        type: string
      - description: SAE应用ID
        in: path
        name: appID
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.BindSAEAppToServiceReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 关联SAE应用到服务
      tags:
      - environment
  /api/aslan/environment/environments/sae/{name}/app/{appID}/versions:
    get:
      consumes:
      - application/json
      description: List SAE Application Version
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: app ID
        in: path
        name: appID
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.SAEAppVersion'
            type: array
      summary: List SAE Application Verions
      tags:
      - environment
  /api/aslan/environment/environments/sae/app:
    get:
      consumes:
      - application/json
      description: List SAE Apps
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: query
        name: envName
        type: string
      - description: namespace
        in: query
        name: namespace
        required: true
        type: string
      - description: region id
        in: query
        name: regionID
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      - description: is add app
        in: query
        name: isAddApp
        required: true
        type: boolean
      - description: app name
        in: query
        name: appName
        type: string
      - description: current page
        in: query
        name: currentPage
        required: true
        type: string
      - description: page size
        in: query
        name: pageSize
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ListSAEAppsResponse'
      summary: List SAE Apps
      tags:
      - environment
  /api/aslan/environment/environments/sae/namespace:
    get:
      consumes:
      - application/json
      description: List SAE Namespaces
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: region id
        in: query
        name: regionID
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.SAENamespace'
            type: array
      summary: List SAE Namespaces
      tags:
      - environment
  /api/aslan/environment/init/type/{envType}:
    post:
      consumes:
      - application/json
      description: Initialize Environment
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env type
        in: path
        name: envType
        required: true
        type: string
      - description: application name, used only in vm env type
        enum:
        - host
        - mobile
        in: query
        name: appType
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Initialize Environment
      tags:
      - environment
  /api/aslan/environment/init_info/{name}:
    get:
      consumes:
      - application/json
      description: Get init product
      parameters:
      - description: project template name
        in: path
        name: name
        required: true
        type: string
      - description: env type
        in: query
        name: envType
        required: true
        type: string
      - description: is base env
        in: query
        name: isBaseEnv
        required: true
        type: string
      - description: base env
        in: query
        name: baseEnv
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.getInitProductResponse'
      summary: Get init product
      tags:
      - environment
  /api/aslan/environment/kube/helm/releaseInstances:
    post:
      consumes:
      - application/json
      description: Get Release Instance Deploy Status
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.HelmDeployStatusCheckRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.GetReleaseInstanceDeployStatusResponse'
            type: array
      summary: Get Release Instance Deploy Status
      tags:
      - environment
  /api/aslan/environment/kube/k8s/resources:
    post:
      consumes:
      - application/json
      description: Get Resource Deploy Status
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.K8sDeployStatusCheckRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ServiceDeployStatus'
            type: array
      summary: Get Resource Deploy Status
      tags:
      - environment
  /api/aslan/environment/kube/pods:
    get:
      consumes:
      - application/json
      description: Get Pods Info
      parameters:
      - description: projectName
        in: query
        name: projectName
        required: true
        type: string
      - description: envName
        in: query
        name: envName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ListPodsInfoRespone'
            type: array
      summary: Get Pods Info
      tags:
      - environment
  /api/aslan/environment/kube/pods/{podName}:
    get:
      consumes:
      - application/json
      description: Get Pods Detail Info
      parameters:
      - description: projectName
        in: query
        name: projectName
        required: true
        type: string
      - description: envName
        in: query
        name: envName
        required: true
        type: string
      - description: podName
        in: path
        name: podName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/resource.Pod'
      summary: Get Pods Detail Info
      tags:
      - environment
  /api/aslan/environment/production/environments/{name}/check/istioGrayscale/{op}/ready:
    get:
      consumes:
      - application/json
      description: Check Istio Grayscale Ready
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: operation
        in: path
        name: op
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.IstioGrayscaleReady'
      summary: Check Istio Grayscale Ready
      tags:
      - environment
  /api/aslan/environment/production/environments/{name}/helm/charts:
    put:
      consumes:
      - application/json
      description: Update helm product charts
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.EnvRendersetArg'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update helm product charts
      tags:
      - environment
  /api/aslan/environment/production/environments/{name}/istioGrayscale/config:
    get:
      consumes:
      - application/json
      description: Get Istio Grayscale Config
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.IstioGrayscale'
      summary: Get Istio Grayscale Config
      tags:
      - environment
    post:
      consumes:
      - application/json
      description: Set Istio Grayscale Config
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/kube.SetIstioGrayscaleConfigRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Set Istio Grayscale Config
      tags:
      - environment
  /api/aslan/environment/production/environments/{name}/istioGrayscale/enable:
    delete:
      consumes:
      - application/json
      description: Disable Istio Grayscale
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Disable Istio Grayscale
      tags:
      - environment
    post:
      consumes:
      - application/json
      description: Enable Istio Grayscale
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Enable Istio Grayscale
      tags:
      - environment
  /api/aslan/environment/production/environments/{name}/istioGrayscale/portal/{serviceName}:
    get:
      consumes:
      - application/json
      description: Get Portal Service for Istio Grayscale
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.GetPortalServiceResponse'
      summary: Get Portal Service for Istio Grayscale
      tags:
      - environment
    post:
      consumes:
      - application/json
      description: Setup Portal Service for Istio Grayscale
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          items:
            $ref: '#/definitions/service.SetupPortalServiceRequest'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Setup Portal Service for Istio Grayscale
      tags:
      - environment
  /api/aslan/environment/production/environments/{name}/workloads:
    get:
      consumes:
      - application/json
      description: List Workloads In Env
      parameters:
      - description: env name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ServiceResp'
            type: array
      summary: List Workloads In Env
      tags:
      - environment
  /api/aslan/environment/production/rendersets/renderchart:
    post:
      consumes:
      - application/json
      description: Get service render charts
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: query
        name: envName
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.GetSvcRenderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.HelmSvcRenderArg'
            type: array
      summary: Get Service render charts
      tags:
      - environment
  /api/aslan/environment/rendersets/globalVariables:
    get:
      consumes:
      - application/json
      description: Get global variable from environment, current only used for k8s
        project
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: query
        name: envName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.getGlobalVariablesRespone'
      summary: Get global variable
      tags:
      - environment
  /api/aslan/environment/rendersets/variables:
    get:
      consumes:
      - application/json
      description: Get service variables
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: env name
        in: query
        name: envName
        type: string
      - description: service name
        in: query
        name: serviceName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.K8sSvcRenderArg'
            type: array
      summary: Get service variables
      tags:
      - environment
  /api/aslan/placeholder/apollo_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] ApolloJobSpec'
      parameters:
      - description: body
        in: body
        name: apollo_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.ApolloJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  ApolloJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/blue_green_deploy_v2_jobSpec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] BlueGreenDeployV2JobSpec'
      parameters:
      - description: body
        in: body
        name: blue_green_deploy_v2_jobSpec
        required: true
        schema:
          $ref: '#/definitions/models.BlueGreenDeployV2JobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  BlueGreenDeployV2JobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/canary_deploy_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] CanaryDeployJobSpec'
      parameters:
      - description: body
        in: body
        name: canary_deploy_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.CanaryDeployJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  CanaryDeployJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/custom_deploy_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] CustomDeployJobSpec'
      parameters:
      - description: body
        in: body
        name: deploy_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.CustomDeployJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  CustomDeployJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/deploy_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] ZadigDeployJobSpec'
      parameters:
      - description: body
        in: body
        name: deploy_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.ZadigDeployJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  ZadigDeployJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/gray_release_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] GrayReleaseJobSpec'
      parameters:
      - description: body
        in: body
        name: gray_release_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.GrayReleaseJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  GrayReleaseJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/gray_rollback_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] GrayRollbackJobSpec'
      parameters:
      - description: body
        in: body
        name: gray_rollback_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.GrayRollbackJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  GrayRollbackJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/k8s_patch_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] K8sPatchJobSpec'
      parameters:
      - description: body
        in: body
        name: deploy_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.K8sPatchJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  K8sPatchJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/sql_job_task_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] JobTaskSQLSpec'
      parameters:
      - description: body
        in: body
        name: sql_task_spec
        required: true
        schema:
          $ref: '#/definitions/models.JobTaskSQLSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE] JobTaskSQLSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/update_env_istio_config_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] UpdateEnvIstioConfigJobSpec'
      parameters:
      - description: body
        in: body
        name: update_env_istio_config_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.UpdateEnvIstioConfigJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  UpdateEnvIstioConfigJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/zadig_build_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] ZadigBuildJobSpec'
      parameters:
      - description: body
        in: body
        name: deploy_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.ZadigBuildJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  ZadigBuildJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/zadig_distribute_image_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] ZadigDistributeImageJobSpec'
      parameters:
      - description: body
        in: body
        name: zadig_distribute_image_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.ZadigDistributeImageJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  ZadigDistributeImageJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/zadig_helm_chart_deploy_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] ZadigHelmChartDeployJobSpec'
      parameters:
      - description: body
        in: body
        name: deploy_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.ZadigHelmChartDeployJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  ZadigHelmChartDeployJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/zadig_scanning_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] ZadigScanningJobSpec'
      parameters:
      - description: body
        in: body
        name: zadig_scanning_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.ZadigScanningJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  ZadigScanningJobSpec'
      tags:
      - placeholder
  /api/aslan/placeholder/zadig_vm_deploy_job_spec:
    post:
      consumes:
      - application/json
      description: '[DONT USE] ZadigVMDeployJobSpec'
      parameters:
      - description: body
        in: body
        name: zadig_vm_deploy_job_spec
        required: true
        schema:
          $ref: '#/definitions/models.ZadigVMDeployJobSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: '[DONT USE]  ZadigVMDeployJobSpec'
      tags:
      - placeholder
  /api/aslan/project/bizdir:
    get:
      consumes:
      - application/json
      description: Get Bussiness Directory
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.GroupDetail'
            type: array
      summary: Get Bussiness Directory
      tags:
      - project
  /api/aslan/project/bizdir/search/project:
    get:
      consumes:
      - application/json
      description: Bussiness Directory Search By Project
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.GroupDetail'
            type: array
      summary: Bussiness Directory Search By Project
      tags:
      - project
  /api/aslan/project/bizdir/search/service:
    get:
      consumes:
      - application/json
      description: Bussiness Directory Search By Service
      parameters:
      - description: service name
        in: query
        name: serviceName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.SearchBizDirByServiceGroup'
            type: array
      summary: Bussiness Directory Search By Service
      tags:
      - project
  /api/aslan/project/bizdir/service/detail:
    get:
      consumes:
      - application/json
      description: Get Bussiness Directory Searvice Detail
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: service name
        in: query
        name: serviceName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.GetBizDirServiceDetailResponse'
            type: array
      summary: Get Bussiness Directory Searvice Detail
      tags:
      - project
  /api/aslan/project/bizdir/services:
    get:
      consumes:
      - application/json
      description: Get Bussiness Directory Project Services
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      summary: Get Bussiness Directory Project Services
      tags:
      - project
  /api/aslan/project/integration/{name}/codehosts:
    get:
      consumes:
      - application/json
      description: List Project CodeHost
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      - description: encrypted key
        in: query
        name: encryptedKey
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.CodeHost'
            type: array
      summary: List Project CodeHost
      tags:
      - project
    post:
      consumes:
      - application/json
      description: Create Project CodeHost
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.CodeHost'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create Project CodeHost
      tags:
      - project
  /api/aslan/project/integration/{name}/codehosts/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Project CodeHost
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      - description: code host id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete Project CodeHost
      tags:
      - project
    get:
      consumes:
      - application/json
      description: Get Project CodeHost
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      - description: code host id
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.CodeHost'
      summary: Get Project CodeHost
      tags:
      - project
    patch:
      consumes:
      - application/json
      description: Update Project CodeHost
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      - description: code host id
        in: path
        name: id
        required: true
        type: integer
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.CodeHost'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update Project CodeHost
      tags:
      - project
  /api/aslan/project/integration/{name}/codehosts/available:
    get:
      consumes:
      - application/json
      description: List Available CodeHost
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      - description: encrypted key
        in: query
        name: encryptedKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.CodeHost'
            type: array
      summary: List Available CodeHost
      tags:
      - project
  /api/aslan/project/products:
    post:
      consumes:
      - application/json
      description: Create a new product template with the provided details
      parameters:
      - description: Product template details
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/template.Product'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Create Product Template
      tags:
      - product
  /api/aslan/project/products/{name}/globalProductionGlobalVariables:
    get:
      consumes:
      - application/json
      description: Get global variable candidates
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.GetGlobalVariableCandidatesRespone'
            type: array
      summary: Get production_global variable candidates
      tags:
      - project
  /api/aslan/project/products/{name}/globalVariableCandidates:
    get:
      consumes:
      - application/json
      description: Get global variable candidates
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.GetGlobalVariableCandidatesRespone'
            type: array
      summary: Get global variable candidates
      tags:
      - project
  /api/aslan/project/products/{name}/globalVariables:
    get:
      consumes:
      - application/json
      description: Get global variables
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.ServiceVariableKV'
            type: array
      summary: Get global variables
      tags:
      - project
    put:
      consumes:
      - application/json
      description: Update global variables
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.updateGlobalVariablesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update global variables
      tags:
      - project
  /api/aslan/project/products/{name}/productionGlobalVariables:
    get:
      consumes:
      - application/json
      description: Get global variables
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.ServiceVariableKV'
            type: array
      summary: Get global production_variables
      tags:
      - project
    put:
      consumes:
      - application/json
      description: Update global variables
      parameters:
      - description: project name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.updateGlobalVariablesRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update production_global variables
      tags:
      - project
  /api/aslan/project/projects:
    get:
      consumes:
      - application/json
      description: Retrieves a list of projects based on the user's authorization,
        with support for filtering, grouping, and verbosity level.
      parameters:
      - description: Ignore projects without environments
        in: query
        name: ignore_no_envs
        type: boolean
      - description: Ignore projects without versions
        in: query
        name: ignore_no_versions
        type: boolean
      - description: 'Verbosity level: minimal, brief, detailed'
        in: query
        name: verbosity
        type: string
      - description: Page number for pagination
        in: query
        name: page_num
        type: integer
      - description: Page size for pagination
        in: query
        name: page_size
        type: integer
      - description: Keyword to filter project names
        in: query
        name: filter
        type: string
      - description: Name of the project group
        in: query
        name: group_name
        type: string
      - description: Whether to list ungrouped projects only
        in: query
        name: ungrouped
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: Detailed project list if verbosity=detailed
          schema:
            $ref: '#/definitions/service.ProjectDetailedResponse'
      summary: List Projects
      tags:
      - project
  /api/aslan/release_plan/v1:
    get:
      consumes:
      - application/json
      description: List Release Plans
      parameters:
      - description: page num
        in: query
        name: pageNum
        required: true
        type: integer
      - description: page size
        in: query
        name: pageSize
        required: true
        type: integer
      - description: search type
        enum:
        - name
        - manager
        - success_time
        - update_time
        - status
        in: query
        name: type
        required: true
        type: string
      - description: search keyword, 当类型为success_time时，值应为'开始时间戳-结束时间戳'的形式
        in: query
        name: keyword
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ListReleasePlanResp'
      summary: List Release Plans
      tags:
      - releasePlan
  /api/aslan/service/deploy_history:
    get:
      consumes:
      - application/json
      description: List Service Deploy History
      parameters:
      - description: project name
        in: query
        name: project_name
        type: string
      - description: env name
        in: query
        name: env_name
        type: string
      - description: service name
        in: query
        name: service_name
        type: string
      - description: create account name
        in: query
        name: create_by
        type: string
      - description: page
        in: query
        name: page
        type: integer
      - description: page size
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handler.ServiceDeployHistoryResp'
            type: array
      summary: List Service Deploy History
      tags:
      - service
  /api/aslan/service/helm/{productName}:
    get:
      consumes:
      - application/json
      description: Retrieves a list of all Helm services in the specified project
      parameters:
      - description: Project name
        in: path
        name: productName
        required: true
        type: string
      - default: false
        description: Whether to list production services
        in: query
        name: production
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: List of Helm services with file info
          schema:
            $ref: '#/definitions/service.HelmService'
      summary: List Helm Services
      tags:
      - helm
  /api/aslan/service/helm/{productName}/{serviceName}/fileContent:
    get:
      consumes:
      - application/json
      description: Retrieves the content of a specific file in a Helm service
      parameters:
      - description: Project name
        in: path
        name: productName
        required: true
        type: string
      - description: Service name
        in: path
        name: serviceName
        required: true
        type: string
      - default: false
        description: Whether this is for production environment
        in: query
        name: production
        type: boolean
      - description: File path relative to service root
        in: query
        name: filePath
        required: true
        type: string
      - description: File name
        in: query
        name: fileName
        required: true
        type: string
      - default: 0
        description: Service revision number
        in: query
        name: revision
        type: integer
      - default: false
        description: Whether to get delivery version
        in: query
        name: deliveryVersion
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: File content
          schema:
            type: string
      summary: Get Helm Service File Content
      tags:
      - helm
  /api/aslan/service/helm/{productName}/{serviceName}/filePath:
    get:
      consumes:
      - application/json
      description: Retrieves the file and directory structure of a Helm service
      parameters:
      - description: Project name
        in: path
        name: productName
        required: true
        type: string
      - description: Service name
        in: path
        name: serviceName
        required: true
        type: string
      - default: false
        description: Whether this is for production environment
        in: query
        name: production
        type: boolean
      - default: 0
        description: Service revision number
        in: query
        name: revision
        type: integer
      - default: '""'
        description: Directory path to browse
        in: query
        name: dir
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of files and directories
          schema:
            items:
              $ref: '#/definitions/types.FileInfo'
            type: array
      summary: Get Helm Service File Path
      tags:
      - helm
  /api/aslan/service/helm/{productName}/{serviceName}/serviceModule:
    get:
      consumes:
      - application/json
      description: Retrieves detailed module information for a specific Helm service
        including containers and build configurations
      parameters:
      - description: Project name
        in: path
        name: productName
        required: true
        type: string
      - description: Service name
        in: path
        name: serviceName
        required: true
        type: string
      - default: false
        description: Whether this is for production environment
        in: query
        name: production
        type: boolean
      - default: 0
        description: Service revision number
        in: query
        name: revision
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Service module information
          schema:
            $ref: '#/definitions/service.HelmServiceModule'
      summary: Get Helm Service Module
      tags:
      - helm
  /api/aslan/service/helm/{serviceName}/file:
    put:
      consumes:
      - application/json
      description: Updates the content of a specific file in a Helm service (currently
        only supports values.yaml)
      parameters:
      - description: Service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - default: false
        description: Whether this is for production environment
        in: query
        name: production
        type: boolean
      - description: File edit information
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.HelmChartEditInfo'
      produces:
      - application/json
      responses:
        "200":
          description: Success response
          schema:
            type: string
      summary: Update Helm Service File Content
      tags:
      - helm
  /api/aslan/service/helm/services:
    post:
      consumes:
      - application/json
      description: Creates a new Helm service or updates an existing one from various
        sources (repo, chart template, chart repo)
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - default: false
        description: Whether this is for production environment
        in: query
        name: production
        type: boolean
      - description: Helm service creation arguments
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.HelmServiceCreationArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Service creation response
          schema:
            $ref: '#/definitions/service.BulkHelmServiceCreationResponse'
      summary: Create or Update Helm Service
      tags:
      - helm
    put:
      consumes:
      - application/json
      description: Updates an existing Helm service with new configuration
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - default: false
        description: Whether this is for production environment
        in: query
        name: production
        type: boolean
      - description: Helm service update arguments
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.HelmServiceCreationArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Service update response
          schema:
            $ref: '#/definitions/service.BulkHelmServiceCreationResponse'
      summary: Update Helm Service
      tags:
      - helm
  /api/aslan/service/helm/services/bulk:
    post:
      consumes:
      - application/json
      description: Creates or updates multiple Helm services in bulk from chart templates
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - default: false
        description: Whether this is for production environment
        in: query
        name: production
        type: boolean
      - description: Bulk Helm service creation arguments
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.BulkHelmServiceCreationArgs'
      produces:
      - application/json
      responses:
        "200":
          description: Bulk service creation response
          schema:
            $ref: '#/definitions/service.BulkHelmServiceCreationResponse'
      summary: Create or Update Bulk Helm Services
      tags:
      - helm
  /api/aslan/service/labels:
    get:
      consumes:
      - application/json
      parameters:
      - description: 项目标识
        in: query
        name: projectKey
        required: true
        type: string
      - description: 服务名称
        in: query
        name: serviceName
        required: true
        type: string
      - description: 是否为生产服务，true:生产服务，false:非生产服务
        in: query
        name: production
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ServiceLabelResp'
      summary: 获取服务的标签列表
      tags:
      - service
    post:
      consumes:
      - application/json
      description: 全部参数都是必传
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.addServiceLabelReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 服务添加标签
      tags:
      - service
  /api/aslan/service/labels/{id}:
    delete:
      consumes:
      - application/json
      description: Delete an existing label from a service
      parameters:
      - description: Label binding ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete service label
      tags:
      - label
    put:
      consumes:
      - application/json
      description: Update the value of an existing label for a service
      parameters:
      - description: Label binding ID
        in: path
        name: id
        required: true
        type: string
      - description: New label value
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.updateServiceLabelReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update service label
      tags:
      - label
  /api/aslan/service/services:
    get:
      consumes:
      - application/json
      description: List service templates for a project, optionally filtered by production
        status
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: 'Is production environment (default: false)'
        in: query
        name: production
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ServiceTmplResp'
      summary: List service templates
      tags:
      - service
    post:
      consumes:
      - application/json
      description: Create service template
      parameters:
      - description: is force to create service template
        in: query
        name: force
        required: true
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.createServiceTemplateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ServiceOption'
      summary: Create service template
      tags:
      - service
    put:
      consumes:
      - application/json
      description: Update environment status for a service template, used internally
        by cron jobs or system processes.
      parameters:
      - description: Service template object with updated env status
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.ServiceTmplObject'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update service template (env status)
      tags:
      - service
  /api/aslan/service/services/{name}:
    get:
      consumes:
      - application/json
      description: Get options for a specific service template (e.g., build names,
        variables)
      parameters:
      - description: Service template name
        in: path
        name: name
        required: true
        type: string
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: 'Is production service (default: false)'
        in: query
        name: production
        type: boolean
      - description: 'Revision number (default: 0 for latest)'
        in: query
        name: revision
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ServiceOption'
      summary: Get service template options
      tags:
      - service
  /api/aslan/service/services/{name}/{type}:
    delete:
      consumes:
      - application/json
      description: Delete a service template by name, type, and project
      parameters:
      - description: Service template name
        in: path
        name: name
        required: true
        type: string
      - description: Service template type
        in: path
        name: type
        required: true
        type: string
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: 'Is production service (default: false)'
        in: query
        name: production
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete service template
      tags:
      - service
    get:
      consumes:
      - application/json
      description: Get a specific service template by name, type, project, and revision
      parameters:
      - description: Service template name
        in: path
        name: name
        required: true
        type: string
      - description: Service template type (e.g., k8s, helm, pm)
        in: path
        name: type
        required: true
        type: string
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: 'Is production service (default: false)'
        in: query
        name: production
        type: boolean
      - description: 'Revision number (default: 0 for latest)'
        in: query
        name: revision
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.TemplateSvcResp'
      summary: Get service template
      tags:
      - service
  /api/aslan/service/services/{name}/environments/deployable:
    get:
      consumes:
      - application/json
      description: Get Deployable Envs
      parameters:
      - description: service name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.DeployableEnvResp'
      summary: Get Deployable Envs
      tags:
      - service
  /api/aslan/service/services/{name}/variable:
    put:
      consumes:
      - application/json
      description: Update service varaible
      parameters:
      - description: service name
        in: path
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.updateServiceVariableRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update service varaible
      tags:
      - service
  /api/aslan/service/services/variable/convert:
    post:
      consumes:
      - application/json
      description: convert varaible kv and yaml
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.ConvertVaraibleKVAndYamlArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ConvertVaraibleKVAndYamlArgs'
      summary: convert varaible kv and yaml
      tags:
      - service
  /api/aslan/service/services/yaml/validator:
    put:
      consumes:
      - application/json
      description: Validates the syntax and structure of a Kubernetes YAML definition
      parameters:
      - description: YAML content to validate
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.YamlValidatorReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handler.ValidatorResp'
            type: array
      summary: Validate K8s YAML
      tags:
      - service
  /api/aslan/service/template/load:
    post:
      consumes:
      - application/json
      description: Load service from yaml template
      parameters:
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.LoadServiceFromYamlTemplateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Load service from yaml template
      tags:
      - service
  /api/aslan/service/template/reload:
    post:
      consumes:
      - application/json
      description: Reload service from yaml template
      parameters:
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.LoadServiceFromYamlTemplateReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Reload service from yaml template
      tags:
      - service
  /api/aslan/service/version/{serviceName}:
    get:
      consumes:
      - application/json
      description: List Service Versions
      parameters:
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: page size
        in: query
        name: page
        type: integer
      - description: page
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Service'
            type: array
      summary: List Service Versions
      tags:
      - service
  /api/aslan/service/version/{serviceName}/diff:
    get:
      consumes:
      - application/json
      description: Diff Service Versions
      parameters:
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: revision a
        in: query
        name: revisionA
        required: true
        type: integer
      - description: revision b
        in: query
        name: revisionB
        required: true
        type: integer
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.DiffServiceVersionsResponse'
      summary: Diff Service Versions
      tags:
      - service
  /api/aslan/service/version/{serviceName}/revision/{revision}:
    get:
      consumes:
      - application/json
      description: Get Service Versions Yaml
      parameters:
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: revision
        in: path
        name: revision
        required: true
        type: string
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.GetServiceVersionYamlResponse'
      summary: Get Service Version Yaml
      tags:
      - service
  /api/aslan/service/version/{serviceName}/rollback:
    post:
      consumes:
      - application/json
      description: Rollback Service Version
      parameters:
      - description: service name
        in: path
        name: serviceName
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: revision
        in: query
        name: revision
        required: true
        type: integer
      - description: is production
        in: query
        name: production
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Rollback Service Version
      tags:
      - service
  /api/aslan/service/workloads:
    get:
      consumes:
      - application/json
      description: List workload templates for a project and environment, optionally
        filtered by production status
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: 'Is production environment (default: false)'
        in: query
        name: production
        type: boolean
      - description: Environment name
        in: query
        name: env
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ServiceTmplResp'
      summary: List workload templates
      tags:
      - workload
    post:
      consumes:
      - application/json
      description: Create new Kubernetes workloads in a specified environment
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: 'Is production environment (default: false)'
        in: query
        name: production
        type: boolean
      - description: Kubernetes workload creation arguments
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.K8sWorkloadsArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create K8s workloads in an environment
      tags:
      - workload
    put:
      consumes:
      - application/json
      description: Update workloads (e.g., Deployments, StatefulSets) in a specified
        environment
      parameters:
      - description: Project name
        in: query
        name: projectName
        required: true
        type: string
      - description: Environment name
        in: query
        name: env
        required: true
        type: string
      - description: 'Is production environment (default: false)'
        in: query
        name: production
        type: boolean
      - description: Workload update arguments
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.UpdateWorkloadsArgs'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update workloads in an environment
      tags:
      - workload
  /api/aslan/sprint_management/v1/sprint:
    get:
      consumes:
      - application/json
      description: List Sprint
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: page num
        in: query
        name: pageNum
        required: true
        type: integer
      - description: page size
        in: query
        name: pageSize
        required: true
        type: integer
      - description: is archived
        in: query
        name: isArchived
        required: true
        type: boolean
      - description: search filter
        in: query
        name: filter
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ListSprintResp'
      summary: List Sprint
      tags:
      - SprintManagement
    post:
      consumes:
      - application/json
      description: Create Sprint
      parameters:
      - description: sprint name
        in: query
        name: name
        required: true
        type: string
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint template ID
        in: query
        name: templateID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.CreateSprintResponse'
      summary: Create Sprint
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Sprint
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete Sprint
      tags:
      - SprintManagement
    get:
      consumes:
      - application/json
      description: Get Sprint
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.Sprint'
      summary: Get Sprint
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint/{id}/activate:
    put:
      consumes:
      - application/json
      description: Activate Archived Sprint
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Activate Archived Sprint
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint/{id}/archive:
    put:
      consumes:
      - application/json
      description: Archive Sprint
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Archive Sprint
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint/{id}/name:
    put:
      consumes:
      - application/json
      description: Update Sprint Name
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint name
        in: query
        name: name
        required: true
        type: string
      - description: sprint id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update Sprint Name
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_template:
    get:
      consumes:
      - application/json
      description: List Sprint Templates
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: page num
        in: query
        name: pageNum
        required: true
        type: integer
      - description: page size
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ListSprintTemplateResp'
      summary: List Sprint Templates
      tags:
      - SprintManagement
    post:
      consumes:
      - application/json
      description: Create Sprint Template
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SprintTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create Sprint Template
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_template/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Sprint Template
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint template id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete Sprint Template
      tags:
      - SprintManagement
    get:
      consumes:
      - application/json
      description: Get Sprint Template
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint template id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SprintTemplate'
      summary: Get Sprint Template
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_template/{id}/stage:
    post:
      consumes:
      - application/json
      description: Add Sprint Template Stage
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint template id
        in: path
        name: id
        required: true
        type: string
      - description: sprint template stage name
        in: query
        name: stageName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Add Sprint Template Stage
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_template/{id}/stage/{stageID}:
    delete:
      consumes:
      - application/json
      description: Delete Sprint Template Stage
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint template id
        in: path
        name: id
        required: true
        type: string
      - description: sprint template stage id
        in: path
        name: stageID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete Sprint Template Stage
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_template/{id}/stage/{stageID}/name:
    put:
      consumes:
      - application/json
      description: Update Sprint Template Stage Name
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint template id
        in: path
        name: id
        required: true
        type: string
      - description: sprint template stage id
        in: path
        name: stageID
        required: true
        type: string
      - description: sprint template stage name
        in: query
        name: stageName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SprintTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update Sprint Template Stage Name
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_template/{id}/stage/{stageID}/workflows:
    put:
      consumes:
      - application/json
      description: Update Sprint Template Stage Workflows
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint template id
        in: path
        name: id
        required: true
        type: string
      - description: sprint template stage id
        in: path
        name: stageID
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.UpdateSprintTemplateStageWorkflowsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update Sprint Template Stage Workflows
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_template/default:
    get:
      consumes:
      - application/json
      description: Get Default Sprint Templates
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SprintTemplate'
      summary: Get Default Sprint Templates
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem:
    post:
      consumes:
      - application/json
      description: Create SprintWorkItem
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SprintWorkItem'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create SprintWorkItem
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem/{id}:
    delete:
      consumes:
      - application/json
      description: Delete SprintWorkItem
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete SprintWorkItem
      tags:
      - SprintManagement
    get:
      consumes:
      - application/json
      description: Get SprintWorkItem
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.SprintWorkItem'
      summary: Get SprintWorkItem
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem/{id}/desc:
    put:
      consumes:
      - application/json
      description: Update SprintWorkItem Description
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.UpdateSprintWorkItemDescRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update SprintWorkItem Description
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem/{id}/move:
    put:
      consumes:
      - application/json
      description: Move SprintWorkItem
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint id
        in: query
        name: sprintID
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      - description: sprint workitem stageID
        in: query
        name: stageID
        required: true
        type: string
      - description: sprint workitem index in stage
        in: query
        name: index
        required: true
        type: integer
      - description: sprint update time
        in: query
        name: sprintUpdateTime
        required: true
        type: integer
      - description: sprint workitem update time
        in: query
        name: workitemUpdateTime
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Move SprintWorkItem
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem/{id}/owner:
    put:
      consumes:
      - application/json
      description: Update SprintWorkItem Owner
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      - description: sprint workitem update verb
        enum:
        - add
        - remove
        in: query
        name: verb
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          items:
            $ref: '#/definitions/types.UserBriefInfo'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update SprintWorkItem Owner
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem/{id}/task:
    get:
      consumes:
      - application/json
      description: List Sprint WorkItem Task
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      - description: workflow name
        in: query
        name: workfloName
        required: true
        type: string
      - description: page num
        in: query
        name: pageNum
        required: true
        type: integer
      - description: page size
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ListSprintWorkItemTaskResponse'
      summary: List Sprint WorkItem Task
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem/{id}/task/clone:
    post:
      consumes:
      - application/json
      description: Clone SprintWorkItem Task
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      - description: workflow name
        in: query
        name: workflowName
        required: true
        type: string
      - description: workitem task id
        in: query
        name: taskID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Clone SprintWorkItem Task
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem/{id}/task/exec:
    post:
      consumes:
      - application/json
      description: Exec SprintWorkItem Workflow
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      - collectionFormat: csv
        description: sprint workitem ids
        in: query
        items:
          type: string
        name: ids
        required: true
        type: array
      - description: workflow name
        in: query
        name: workflowName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Exec SprintWorkItem Workflow
      tags:
      - SprintManagement
  /api/aslan/sprint_management/v1/sprint_workitem/{id}/title:
    put:
      consumes:
      - application/json
      description: Update SprintWorkItem Title
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: sprint workitem id
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.UpdateSprintWorkItemTitleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update SprintWorkItem Title
      tags:
      - SprintManagement
  /api/aslan/stat/dashboard/build:
    get:
      consumes:
      - application/json
      description: Retrieve build statistics within a specified date range
      parameters:
      - description: Start date (timestamp)
        in: query
        name: startDate
        required: true
        type: integer
      - description: End date (timestamp)
        in: query
        name: endDate
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.dashboardBuild'
      summary: Get Build Statistics
      tags:
      - stat
  /api/aslan/stat/dashboard/deploy:
    get:
      consumes:
      - application/json
      description: Retrieve deployment statistics within a specified date range
      parameters:
      - description: Start date (timestamp)
        in: query
        name: startDate
        required: true
        type: integer
      - description: End date (timestamp)
        in: query
        name: endDate
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.DeployDashboard'
      summary: Get Deploy Statistics
      tags:
      - stat
  /api/aslan/stat/dashboard/overview:
    get:
      consumes:
      - application/json
      description: Retrieve overview statistics for the system
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.Overview'
      summary: Get Overview Statistics
      tags:
      - stat
  /api/aslan/stat/dashboard/release:
    get:
      consumes:
      - application/json
      description: Retrieve release statistics within a specified date range
      parameters:
      - description: Start date (timestamp)
        in: query
        name: startDate
        required: true
        type: integer
      - description: End date (timestamp)
        in: query
        name: endDate
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.ReleaseDashboard'
      summary: Get Release Dashboard
      tags:
      - stat
  /api/aslan/stat/dashboard/test:
    get:
      consumes:
      - application/json
      description: Retrieve test statistics for the system
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.testDashboard'
      summary: Get Test Dashboard
      tags:
      - stat
  /api/aslan/system/dbinstance/project:
    get:
      consumes:
      - application/json
      description: List DB Instances Info By Project
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.DBInstance'
            type: array
      summary: List DB Instances Info By Project
      tags:
      - system
  /api/aslan/system/helm:
    get:
      consumes:
      - application/json
      description: List all Helm repositories
      parameters:
      - description: Encrypted Key
        in: query
        name: encryptedKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.HelmRepo'
            type: array
      summary: List Helm Repos
      tags:
      - system
    post:
      consumes:
      - application/json
      description: Create a new Helm repository
      parameters:
      - description: Helm Repo Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.HelmRepo'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Create Helm Repo
      tags:
      - system
  /api/aslan/system/helm/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a Helm repository by ID
      parameters:
      - description: Helm Repo ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Delete Helm Repo
      tags:
      - system
    put:
      consumes:
      - application/json
      description: Update an existing Helm repository by ID
      parameters:
      - description: Helm Repo ID
        in: path
        name: id
        required: true
        type: string
      - description: Helm Repo Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.HelmRepo'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Update Helm Repo
      tags:
      - system
  /api/aslan/system/helm/{name}/index:
    get:
      consumes:
      - application/json
      description: List all charts in a specific Helm repository
      parameters:
      - description: Repository Name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Chart Names
          schema:
            items:
              type: string
            type: array
      summary: List Charts
      tags:
      - system
  /api/aslan/system/helm/project:
    get:
      consumes:
      - application/json
      description: List Helm Repos By Project
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.HelmRepo'
            type: array
      summary: List Helm Repos By Project
      tags:
      - system
  /api/aslan/system/helm/public:
    get:
      consumes:
      - application/json
      description: List all public Helm repositories
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.HelmRepo'
            type: array
      summary: List Public Helm Repos
      tags:
      - system
  /api/aslan/system/labels:
    get:
      consumes:
      - application/json
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.Label'
            type: array
      summary: 获取服务标签配置列表
      tags:
      - system
    post:
      consumes:
      - application/json
      description: 只需要传入参数key
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.Label'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 创建服务标签
      tags:
      - system
  /api/aslan/system/lark/{id}/user_group:
    get:
      consumes:
      - application/json
      parameters:
      - description: 飞书应用 ID
        in: path
        name: id
        required: true
        type: string
      - description: 用户组类型
        in: query
        name: type
        required: true
        type: string
      - description: 分页 token
        in: query
        name: page_token
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.GetLarkUserGroupsResp'
      summary: 获取飞书用户组
      tags:
      - system
  /api/aslan/system/lark/{id}/user_group/members:
    get:
      consumes:
      - application/json
      parameters:
      - description: 飞书应用 ID
        in: path
        name: id
        required: true
        type: string
      - description: 用户组 ID
        in: query
        name: user_group_id
        required: true
        type: string
      - description: 成员类型
        in: query
        name: member_type
        required: true
        type: string
      - description: 成员 ID 类型
        in: query
        name: member_id_type
        required: true
        type: string
      - description: 分页 token
        in: query
        name: page_token
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.GetLarkUserGroupMembersResp'
      summary: 获取飞书用户组成员
      tags:
      - system
  /api/aslan/system/llm/integration:
    get:
      consumes:
      - application/json
      description: List llm integrations
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.LLMIntegration'
            type: array
      summary: List llm integrations
      tags:
      - system
    post:
      consumes:
      - application/json
      description: Create a llm integration
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.CreateLLMIntegrationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create a llm integration
      tags:
      - system
  /api/aslan/system/llm/integration/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a llm integration
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete a llm integration
      tags:
      - system
    get:
      consumes:
      - application/json
      description: Get a llm integration
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.LLMIntegration'
      summary: Get a llm integration
      tags:
      - system
    put:
      consumes:
      - application/json
      description: Update a llm integration
      parameters:
      - description: id
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.CreateLLMIntegrationRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update a llm integration
      tags:
      - system
  /api/aslan/system/llm/integration/check:
    get:
      consumes:
      - application/json
      description: Check llm integrations
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.checkLLMIntegrationResponse'
      summary: Check llm integrations
      tags:
      - system
  /api/aslan/system/meego/{id}/projects:
    get:
      consumes:
      - application/json
      description: List Meego Projects
      parameters:
      - description: meego id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.MeegoProjectResp'
      summary: List Meego Projects
      tags:
      - system
  /api/aslan/system/meego/{id}/projects/{projectID}/work_item:
    get:
      consumes:
      - application/json
      description: List Meego Work Items
      parameters:
      - description: meego id
        in: path
        name: id
        required: true
        type: string
      - description: project id
        in: path
        name: projectID
        required: true
        type: string
      - description: type key
        in: query
        name: type_key
        required: true
        type: string
      - description: page num
        in: query
        name: page_num
        required: true
        type: string
      - description: page size
        in: query
        name: page_size
        required: true
        type: string
      - description: item name
        in: query
        name: item_name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.MeegoWorkItemResp'
      summary: List Meego Work Items
      tags:
      - system
  /api/aslan/system/meego/{id}/projects/{projectID}/work_item/types:
    get:
      consumes:
      - application/json
      description: Get Meego Work Item Type List
      parameters:
      - description: meego id
        in: path
        name: id
        required: true
        type: string
      - description: project id
        in: path
        name: projectID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.MeegoWorkItemTypeResp'
      summary: Get Meego Work Item Type List
      tags:
      - system
  /api/aslan/system/project_management/{id}/jira/allStatus:
    get:
      consumes:
      - application/json
      description: Get Jira All Status
      parameters:
      - description: jira id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/jira.Status'
            type: array
      summary: Get Jira All Status
      tags:
      - system
  /api/aslan/system/project_management/{id}/jira/board:
    get:
      consumes:
      - application/json
      description: List Jira Boards
      parameters:
      - description: jira id
        in: path
        name: id
        required: true
        type: string
      - description: jira project key
        in: query
        name: projectKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.JiraBoardResp'
            type: array
      summary: List Jira Boards
      tags:
      - system
  /api/aslan/system/project_management/{id}/jira/board/:boardID/sprint:
    get:
      consumes:
      - application/json
      description: List Jira Sprints
      parameters:
      - description: jira id
        in: path
        name: id
        required: true
        type: string
      - description: jira board id
        in: path
        name: boardID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.JiraSprintResp'
            type: array
      summary: List Jira Sprints
      tags:
      - system
  /api/aslan/system/project_management/{id}/jira/project:
    get:
      consumes:
      - application/json
      description: List Jira Projects
      parameters:
      - description: jira id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.JiraProjectResp'
            type: array
      summary: List Jira Projects
      tags:
      - system
  /api/aslan/system/project_management/{id}/jira/sprint/:sprintID:
    get:
      consumes:
      - application/json
      description: Get Jira Sprint
      parameters:
      - description: jira id
        in: path
        name: id
        required: true
        type: string
      - description: jira sprint id
        in: path
        name: sprintID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.JiraSprintResp'
      summary: Get Jira Sprint
      tags:
      - system
  /api/aslan/system/project_management/{id}/jira/sprint/:sprintID/issue:
    get:
      consumes:
      - application/json
      description: List Jira Sprint Issues
      parameters:
      - description: jira id
        in: path
        name: id
        required: true
        type: string
      - description: jira sprint id
        in: path
        name: sprintID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.JiraSprintResp'
      summary: List Jira Sprint Issues
      tags:
      - system
  /api/aslan/system/project_management/{id}/jira/status:
    get:
      consumes:
      - application/json
      description: Get Jira Project Status
      parameters:
      - description: jira id
        in: path
        name: id
        required: true
        type: string
      - description: jira project id
        in: query
        name: project
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      summary: Get Jira Project Status
      tags:
      - system
  /api/aslan/system/project_management/{id}/jira/type:
    get:
      consumes:
      - application/json
      description: Get Jira Types
      parameters:
      - description: jira id
        in: path
        name: id
        required: true
        type: string
      - description: jira project key
        in: query
        name: project
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/jira.IssueTypeWithStatus'
            type: array
      summary: Get Jira Types
      tags:
      - system
  /api/aslan/system/project_management/project:
    get:
      consumes:
      - application/json
      description: List Project Management For Project
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.ProjectManagement'
            type: array
      summary: List Project Management For Project
      tags:
      - system
  /api/aslan/system/registry/images:
    post:
      consumes:
      - application/json
      description: List images in the registry based on provided names
      parameters:
      - description: Image Names
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.ListImagesOption'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.RepoImgResp'
            type: array
      summary: List Images
      tags:
      - system
  /api/aslan/system/registry/images/repos/{name}:
    get:
      consumes:
      - application/json
      description: List images in a specific repository
      parameters:
      - description: Repository Name
        in: path
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.RepoImgResp'
            type: array
      summary: List Repository Images
      tags:
      - system
  /api/aslan/system/registry/namespaces:
    get:
      consumes:
      - application/json
      description: List all registry namespaces
      parameters:
      - description: Encrypted Key
        in: query
        name: encryptedKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.RegistryNamespace'
            type: array
      summary: List Registry Namespaces
      tags:
      - system
    post:
      consumes:
      - application/json
      description: Create a new registry namespace
      parameters:
      - description: Registry Namespace Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.RegistryNamespace'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Create Registry Namespace
      tags:
      - system
  /api/aslan/system/registry/namespaces/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a registry namespace by ID
      parameters:
      - description: Registry ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Delete Registry Namespace
      tags:
      - system
    put:
      consumes:
      - application/json
      description: Update an existing registry namespace by ID
      parameters:
      - description: Registry ID
        in: path
        name: id
        required: true
        type: string
      - description: Registry Namespace Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.RegistryNamespace'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Update Registry Namespace
      tags:
      - system
  /api/aslan/system/registry/namespaces/default:
    get:
      consumes:
      - application/json
      description: Get the default registry namespace configuration, used for Kodespace
        CLI calls
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.Registry'
      summary: Get Default Registry Namespace
      tags:
      - system
  /api/aslan/system/registry/namespaces/specific/{id}:
    get:
      consumes:
      - application/json
      description: Get the configuration of a specific registry namespace by ID
      parameters:
      - description: Registry ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.Registry'
      summary: Get Specific Registry Namespace
      tags:
      - system
  /api/aslan/system/registry/project:
    get:
      consumes:
      - application/json
      description: List Registries
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.RegistryNamespace'
            type: array
      summary: List Registries
      tags:
      - system
  /api/aslan/system/registry/release/repos:
    get:
      consumes:
      - application/json
      description: List all repositories in the registry
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.RepoImgResp'
            type: array
      summary: List All Repositories
      tags:
      - system
  /api/aslan/system/s3storage:
    get:
      consumes:
      - application/json
      description: List all S3 storage configurations
      parameters:
      - description: Encrypted Key
        in: query
        name: encryptedKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.S3Storage'
            type: array
      summary: List S3 Storage
      tags:
      - system
    post:
      consumes:
      - application/json
      description: Create a new S3 storage configuration
      parameters:
      - description: S3 Storage Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.S3Storage'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Create S3 Storage
      tags:
      - system
  /api/aslan/system/s3storage/{id}:
    delete:
      consumes:
      - application/json
      description: Delete a specific S3 storage configuration by ID
      parameters:
      - description: S3 Storage ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Delete S3 Storage
      tags:
      - system
    get:
      consumes:
      - application/json
      description: Get details of a specific S3 storage configuration by ID
      parameters:
      - description: S3 Storage ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.S3Storage'
      summary: Get S3 Storage
      tags:
      - system
    put:
      consumes:
      - application/json
      description: Update an existing S3 storage configuration by ID
      parameters:
      - description: S3 Storage ID
        in: path
        name: id
        required: true
        type: string
      - description: S3 Storage Info
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.S3Storage'
      produces:
      - application/json
      responses:
        "200":
          description: Success
          schema:
            type: string
      summary: Update S3 Storage
      tags:
      - system
  /api/aslan/system/s3storage/{id}/releases/search:
    post:
      consumes:
      - application/json
      description: List tar files in S3 storage based on provided names
      parameters:
      - description: S3 Storage ID
        in: path
        name: id
        required: true
        type: string
      - description: Kind of tar files
        in: query
        name: kind
        required: true
        type: string
      - description: Tar file names
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.ListTarsOption'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.TarInfo'
            type: array
      summary: List Tars
      tags:
      - system
  /api/aslan/system/s3storage/project:
    get:
      consumes:
      - application/json
      description: List S3 Storage By Project
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.S3Storage'
            type: array
      summary: List S3 Storage By Project
      tags:
      - system
  /api/aslan/system/sae:
    get:
      consumes:
      - application/json
      description: List SAE
      parameters:
      - description: encrypted key
        in: query
        name: encryptedKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.SAE'
            type: array
      summary: List SAE
      tags:
      - system
    post:
      consumes:
      - application/json
      description: Create SAE
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SAE'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create SAE
      tags:
      - system
  /api/aslan/system/sae/{id}:
    delete:
      consumes:
      - application/json
      description: Delete SAE
      parameters:
      - description: sae id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete SAE
      tags:
      - system
    get:
      consumes:
      - application/json
      description: Get SAE
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.SAE'
      summary: Get SAE
      tags:
      - system
    put:
      consumes:
      - application/json
      description: Update SAE
      parameters:
      - description: sae id
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SAE'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update SAE
      tags:
      - system
  /api/aslan/system/sae/detail:
    get:
      consumes:
      - application/json
      description: List SAE Detail
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.SAE'
            type: array
      summary: List SAE Detail
      tags:
      - system
  /api/aslan/system/sae/validate:
    post:
      consumes:
      - application/json
      description: Validate SAE
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.SAE'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Validate SAE
      tags:
      - system
  /api/aslan/system/webhook/config:
    get:
      consumes:
      - application/json
      description: Get webhook config
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.GetWebhookConfigReponse'
      summary: Get webhook config
      tags:
      - system
  /api/aslan/template/release_plan:
    get:
      consumes:
      - application/json
      description: List Release Plan Template
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/models.ReleasePlanTemplate'
            type: array
      summary: List Release Plan Template
      tags:
      - template
    post:
      consumes:
      - application/json
      description: Create Release Plan Template
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.ReleasePlanTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create Release Plan Template
      tags:
      - template
  /api/aslan/template/release_plan/{id}:
    delete:
      consumes:
      - application/json
      description: Delete Release Plan Template by ID
      parameters:
      - description: template ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Delete Release Plan Template by ID
      tags:
      - template
    get:
      consumes:
      - application/json
      description: Get Release Plan Template by ID
      parameters:
      - description: template ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ReleasePlanTemplate'
      summary: Get Release Plan Template by ID
      tags:
      - template
    put:
      consumes:
      - application/json
      description: Update Release Plan Template
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.ReleasePlanTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update Release Plan Template
      tags:
      - template
  /api/aslan/template/scanning/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 代码扫描模版ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/models.ScanningTemplate'
      summary: 获取代码扫描模版
      tags:
      - template
  /api/aslan/template/yaml:
    post:
      consumes:
      - application/json
      description: Create yaml template
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/template.YamlTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Create yaml template
      tags:
      - template
  /api/aslan/template/yaml/{id}:
    get:
      consumes:
      - application/json
      description: Get yaml template detail
      parameters:
      - description: template id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/template.YamlDetail'
      summary: Get yaml template detail
      tags:
      - template
    put:
      consumes:
      - application/json
      description: Update yaml template
      parameters:
      - description: template id
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/template.YamlTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update yaml template
      tags:
      - template
  /api/aslan/template/yaml/{id}/variable:
    put:
      consumes:
      - application/json
      description: Update yaml template variable
      parameters:
      - description: template id
        in: path
        name: id
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/template.YamlTemplate'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Update yaml template variable
      tags:
      - template
  /api/aslan/template/yaml/validateVariable:
    post:
      consumes:
      - application/json
      description: Validate template varaibles
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.getYamlTemplateVariablesReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Validate template varaibles
      tags:
      - service
  /api/aslan/testing/scanning:
    get:
      consumes:
      - application/json
      parameters:
      - description: 项目标识
        in: query
        name: projectName
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/service.ListScanningRespItem'
            type: array
      summary: 获取代码扫描列表
      tags:
      - testing
    post:
      consumes:
      - application/json
      description: 使用模版创建时，template_id为必传
      parameters:
      - description: 项目标识
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.Scanning'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 创建代码扫描
      tags:
      - testing
  /api/aslan/testing/scanning/{id}:
    get:
      consumes:
      - application/json
      parameters:
      - description: 项目标识
        in: query
        name: projectName
        required: true
        type: string
      - description: 代码扫描 ID
        in: query
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.Scanning'
      summary: 获取代码扫描详情
      tags:
      - testing
    put:
      consumes:
      - application/json
      description: body参数与创建代码扫描相同
      parameters:
      - description: 项目标识
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.Scanning'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 更新代码扫描
      tags:
      - testing
  /api/aslan/webhook/notify/buildJobSpec:
    post:
      consumes:
      - application/json
      description: Workflow Webhook Build Job Spec
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/webhooknotify.WorkflowNotifyJobTaskBuildSpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Workflow Webhook Notify Build Job Spec
      tags:
      - webhook
  /api/aslan/webhook/notify/deployJobSpec:
    post:
      consumes:
      - application/json
      description: Workflow Webhook Deploy Job Spec
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/webhooknotify.WorkflowNotifyJobTaskDeploySpec'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Workflow Webhook Notify Deploy Job Spec
      tags:
      - webhook
  /api/aslan/webhook/test:
    post:
      consumes:
      - application/json
      description: Webhook Notify
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/webhooknotify.WebHookNotify'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Webhook Notify
      tags:
      - webhook
  /api/aslan/workflow/v4:
    get:
      consumes:
      - application/json
      description: Retrieves a list of workflows based on the provided filters and
        authorization checks.
      parameters:
      - description: Project name
        in: query
        name: project
        required: true
        type: string
      - description: View name
        in: query
        name: viewName
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.listWorkflowV4Resp'
      summary: List workflows (v4 version)
      tags:
      - workflow
    post:
      consumes:
      - text/plain
      description: 创建工作流
      parameters:
      - description: 项目标识
        in: query
        name: projectName
        required: true
        type: string
      - description: 需要添加的视图名称
        in: query
        name: viewName
        required: true
        type: string
      - description: 工作流Yaml
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.WorkflowV4'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 创建工作流
      tags:
      - workflow
  /api/aslan/workflow/v4/{name}:
    put:
      consumes:
      - text/plain
      description: 更新工作流
      parameters:
      - description: 项目标识
        in: query
        name: projectName
        required: true
        type: string
      - description: 工作流标识
        in: path
        name: name
        required: true
        type: string
      - description: 工作流Yaml
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.WorkflowV4'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: 更新工作流
      tags:
      - workflow
  /api/aslan/workflow/v4/deploy/mergeImage:
    post:
      consumes:
      - application/json
      parameters:
      - description: 项目名称
        in: query
        name: projectName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handler.HelmDeployJobMergeImageRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/workflow.HelmDeployJobMergeImageResponse'
      summary: 工作流Helm部署任务合并镜像到ValuesYaml
      tags:
      - workflow
  /api/aslan/workflow/v4/dynamicVariable/available:
    post:
      consumes:
      - application/json
      description: Get Workflow V4 Dynamic Variable's Available Variables
      parameters:
      - description: job name
        in: query
        name: jobName
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.WorkflowV4'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      summary: Get Workflow V4 Dynamic Variable's Available Variables
      tags:
      - workflow
  /api/aslan/workflow/v4/dynamicVariable/render:
    post:
      consumes:
      - application/json
      description: Render Workflow V4 Variables
      parameters:
      - description: job name
        in: query
        name: jobName
        required: true
        type: string
      - description: service name
        in: query
        name: serviceName
        type: string
      - description: service module name
        in: query
        name: moduleName
        type: string
      - description: render variable key
        in: query
        name: key
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/models.WorkflowV4'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              type: string
            type: array
      summary: Render Workflow V4 Variables
      tags:
      - workflow
  /api/aslan/workflow/v4/workflowtask/manualexec/workflow/{workflowName}/task/{taskID}:
    post:
      consumes:
      - application/json
      description: Manually Execute Workflow Task V4
      parameters:
      - description: project name
        in: query
        name: projectName
        required: true
        type: string
      - description: workflow name
        in: path
        name: workflowName
        required: true
        type: string
      - description: workflow task ID
        in: path
        name: taskID
        required: true
        type: string
      - description: workflow stage name
        in: query
        name: stageName
        required: true
        type: string
      - description: workflow stage jobs
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/workflow.ManualExecWorkflowTaskV4Request'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: Manually Execute Workflow Task V4
      tags:
      - workflow
  /api/hub/agent/connect:
    post:
      description: 将请求转发至 Agent
      responses: {}
      summary: Agent 连接中转
      tags:
      - cluster
  /openapi/delivery/releases:
    get:
      consumes:
      - application/json
      description: OpenAPI List Delivery Version
      parameters:
      - description: project key
        in: query
        name: projectKey
        required: true
        type: string
      - description: page num
        in: query
        name: pageNum
        required: true
        type: integer
      - description: page size
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: OpenAPI List Delivery Version
      tags:
      - OpenAPI
  /openapi/delivery/releases/{id}:
    delete:
      consumes:
      - application/json
      description: OpenAPI Delete Delivery Version
      parameters:
      - description: project key
        in: query
        name: projectKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: OpenAPI Delete Delivery Version
      tags:
      - OpenAPI
    get:
      consumes:
      - application/json
      description: OpenAPI Get Delivery Version
      parameters:
      - description: project key
        in: query
        name: projectKey
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: OpenAPI Get Delivery Version
      tags:
      - OpenAPI
  /openapi/delivery/releases/helm:
    post:
      consumes:
      - application/json
      description: OpenAPI Create Helm Delivery Version
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.OpenAPICreateHelmDeliveryVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: OpenAPI Create Helm Delivery Version
      tags:
      - OpenAPI
  /openapi/delivery/releases/k8s:
    post:
      consumes:
      - application/json
      description: OpenAPI Create K8S Delivery Version
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.OpenAPICreateK8SDeliveryVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: OpenAPI Create K8S Delivery Version
      tags:
      - OpenAPI
  /openapi/environments/{name}/variable:
    put:
      consumes:
      - application/json
      description: OpenAPI Update K8S Environment Global Variables
      parameters:
      - description: project key
        in: query
        name: projectKey
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.OpenAPIEnvGlobalVariables'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: OpenAPI Update K8S Environment Global Variables
      tags:
      - OpenAPI
  /openapi/environments/production/{name}/variable:
    put:
      consumes:
      - application/json
      description: OpenAPI Update Production K8S Environment Global Variables
      parameters:
      - description: project key
        in: query
        name: projectKey
        required: true
        type: string
      - description: env name
        in: path
        name: name
        required: true
        type: string
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.OpenAPIEnvGlobalVariables'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: OpenAPI Update Production K8S Environment Global Variables
      tags:
      - OpenAPI
  /openapi/projects/project/init/yaml:
    post:
      consumes:
      - application/json
      description: OpenAPI Initialize Yaml Project
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.OpenAPIInitializeProjectReq'
      produces:
      - application/json
      responses:
        "200":
          description: OK
      summary: OpenAPI Initialize Yaml Project
      tags:
      - OpenAPI
  /openapi/statistics/v2/rollback/detail:
    get:
      consumes:
      - application/json
      description: 返回值中_sae_app和_service根据环境类型不同，可能为null
      parameters:
      - description: 项目标识
        in: query
        name: projectKey
        type: string
      - description: 环境名称
        in: query
        name: envName
        type: string
      - description: 服务名称
        in: query
        name: serviceName
        type: string
      - description: 开始时间，格式为时间戳
        in: query
        name: startTime
        required: true
        type: integer
      - description: 结束时间，格式为时间戳
        in: query
        name: endTime
        required: true
        type: integer
      - description: 当前页码
        in: query
        name: pageNum
        required: true
        type: integer
      - description: 分页大小
        in: query
        name: pageSize
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.GetRollbackStatDetailResponse'
      summary: 获取回滚统计详情(OpenAPI)
      tags:
      - OpenAPI
  /openapi/system/cluster:
    post:
      consumes:
      - application/json
      description: OpenAPI Create Cluster
      parameters:
      - description: body
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/service.OpenAPICreateClusterRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/service.OpenAPICreateClusterResponse'
      summary: OpenAPI Create Cluster
      tags:
      - OpenAPI
  /openapi/system/operation:
    get:
      consumes:
      - application/json
      description: 获取系统操作日志
      parameters:
      - description: 搜索类型
        enum:
        - all
        - user
        - project
        - function
        - status
        - detail
        in: query
        name: searchType
        required: true
        type: string
      - description: 项目标识
        in: query
        name: projectKey
        type: string
      - description: 用户名
        in: query
        name: username
        type: string
      - description: 功能
        in: query
        name: function
        type: string
      - description: 状态码
        in: query
        name: status
        type: integer
      - description: 每页数量
        in: query
        name: perPage
        required: true
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.OpenAPIGetOperationLogsResponse'
      summary: 获取系统操作日志
      tags:
      - OpenAPI
  /openapi/system/operation/env:
    get:
      consumes:
      - application/json
      description: 获取环境操作日志
      parameters:
      - description: 搜索类型
        enum:
        - all
        - user
        - project
        - function
        - status
        - detail
        in: query
        name: searchType
        required: true
        type: string
      - description: 项目标识
        in: query
        name: projectKey
        required: true
        type: string
      - description: 环境名称
        in: query
        name: envName
        required: true
        type: string
      - description: 用户名
        in: query
        name: username
        type: string
      - description: 功能
        in: query
        name: function
        type: string
      - description: 状态码
        in: query
        name: status
        type: integer
      - description: 详情
        in: query
        name: detail
        type: string
      - description: 每页数量
        in: query
        name: perPage
        required: true
        type: integer
      - description: 页码
        in: query
        name: page
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handler.OpenAPIGetOperationLogsResponse'
      summary: 获取环境操作日志
      tags:
      - OpenAPI
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
