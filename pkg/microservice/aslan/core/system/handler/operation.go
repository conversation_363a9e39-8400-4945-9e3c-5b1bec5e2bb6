/*
Copyright 2021 The KodeRover Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package handler

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"

	models2 "github.com/koderover/zadig/v2/pkg/microservice/aslan/core/system/repository/models"
	"github.com/koderover/zadig/v2/pkg/microservice/aslan/core/system/service"
	internalhandler "github.com/koderover/zadig/v2/pkg/shared/handler"
	e "github.com/koderover/zadig/v2/pkg/tool/errors"
)

type OperationLogResponse struct {
	OperationLogs []*models2.OperationLog `json:"rows"`
	Total         int                     `json:"total"`
}

// @Summary 获取系统操作日志
// @Description 获取系统操作日志
// @Tags 	system
// @Accept 	json
// @Produce json
// @Param 	projectName		query		string								false	"项目标识"
// @Param 	username		query		string								false	"用户名"
// @Param 	function		query		string								false	"功能"
// @Param 	status			query		int									false	"状态码"
// @Param 	page_size		query		int									true	"每页数量"
// @Param 	page			query		int									true	"页码"
// @Success 200 			{object} 	OperationLogResponse
// @Router /api/aslan/system/operation [get]

func GetOperationLogs(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {

		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	// authorization checks
	if !ctx.Resources.IsSystemAdmin {
		ctx.UnAuthorized = true
		return
	}

	status, err := strconv.Atoi(c.Query("status"))
	if err != nil {
		ctx.RespErr = e.ErrFindOperationLog.AddErr(err)
		return
	}

	perPage, err := strconv.Atoi(c.Query("page_size"))
	if err != nil {
		perPage = 50
	}

	page, err := strconv.Atoi(c.Query("page"))
	if err != nil {
		page = 1
	}

	args := &service.OperationLogArgs{
		Username:    c.Query("username"),
		ProductName: c.Query("projectName"),
		Function:    c.Query("function"),
		Status:      status,
		PerPage:     perPage,
		Page:        page,
	}

	if args.PerPage == 0 {
		args.PerPage = 50
	}

	if args.Page == 0 {
		args.Page = 1
	}

	resp, count, err := service.FindOperation(args, ctx.Logger)
	// ctx.Resp = resp
	ctx.Resp = &OperationLogResponse{
		OperationLogs: resp,
		Total:         count,
	}
	ctx.RespErr = err
	c.Writer.Header().Set("X-Total", strconv.Itoa(count))
}

func AddSystemOperationLog(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {

		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	args := new(models2.OperationLog)
	err = c.BindJSON(args)
	if err != nil {
		ctx.RespErr = e.ErrInvalidParam.AddDesc("invalid insertOperationLogs args")
		return
	}

	// authorization checks
	if !ctx.Resources.IsSystemAdmin {
		ctx.UnAuthorized = true
		return
	}

	ctx.Resp, ctx.RespErr = service.InsertOperation(args, ctx.Logger)
}

type updateOperationArgs struct {
	Status int `json:"status"`
}

func UpdateOperationLog(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {

		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	args := new(updateOperationArgs)
	err = c.BindJSON(args)
	if err != nil {
		ctx.RespErr = e.ErrInvalidParam.AddDesc("invalid insertOperationLogs args")
		return
	}

	// authorization checks
	if !ctx.Resources.IsSystemAdmin {
		ctx.UnAuthorized = true
		return
	}

	ctx.RespErr = service.UpdateOperation(c.Param("id"), args.Status, ctx.Logger)
}
