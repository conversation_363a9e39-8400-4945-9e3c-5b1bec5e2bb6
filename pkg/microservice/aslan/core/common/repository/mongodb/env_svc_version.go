/*
Copyright 2021 The KodeRover Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package mongodb

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/koderover/zadig/v2/pkg/microservice/aslan/config"
	"github.com/koderover/zadig/v2/pkg/microservice/aslan/core/common/repository/models"
	"github.com/koderover/zadig/v2/pkg/setting"
	mongotool "github.com/koderover/zadig/v2/pkg/tool/mongo"
)

type EnvVersionColl struct {
	*mongo.Collection
	mongo.Session
	coll string
}

func NewEnvServiceVersionColl() *EnvVersionColl {
	name := models.EnvServiceVersion{}.TableName()
	return &EnvVersionColl{Collection: mongotool.Database(config.MongoDatabase()).Collection(name), coll: name}
}

func NewEnvServiceVersionCollWithSession(session mongo.Session) *EnvVersionColl {
	name := models.EnvServiceVersion{}.TableName()
	return &EnvVersionColl{Collection: mongotool.Database(config.MongoDatabase()).Collection(name), Session: session, coll: name}
}

func (c *EnvVersionColl) GetCollectionName() string {
	return c.coll
}

func (c *EnvVersionColl) EnsureIndex(ctx context.Context) error {
	mod := []mongo.IndexModel{
		{
			Keys: bson.D{
				bson.E{Key: "product_name", Value: 1},
				bson.E{Key: "env_name", Value: 1},
				bson.E{Key: "production", Value: 1},
				bson.E{Key: "service.service_name", Value: 1},
				bson.E{Key: "service.release_name", Value: 1},
				bson.E{Key: "service.type", Value: 1},
			},
			Options: options.Index().SetUnique(false).SetName("idx_service_1"),
		},
		{
			Keys: bson.D{
				bson.E{Key: "product_name", Value: 1},
				bson.E{Key: "env_name", Value: 1},
				bson.E{Key: "production", Value: 1},
				bson.E{Key: "service.service_name", Value: 1},
				bson.E{Key: "service.release_name", Value: 1},
				bson.E{Key: "service.type", Value: 1},
				bson.E{Key: "revision", Value: 1},
			},
			Options: options.Index().SetUnique(true).SetName("idx_service_revision_1"),
		},
	}

	_, _ = c.Indexes().DropOne(ctx, "idx_service")
	_, _ = c.Indexes().DropOne(ctx, "idx_service_revision")

	_, err := c.Indexes().CreateMany(ctx, mod)

	return err
}

func (c *EnvVersionColl) Find(productName, envName, serviceName string, isHelmChart, production bool, revision int64) (*models.EnvServiceVersion, error) {
	res := &models.EnvServiceVersion{}
	query := bson.M{}
	query["env_name"] = envName
	query["product_name"] = productName
	query["production"] = production
	query["revision"] = revision

	if isHelmChart {
		query["service.release_name"] = serviceName
		query["service.type"] = setting.HelmChartDeployType
	} else {
		query["service.service_name"] = serviceName
	}

	err := c.FindOne(context.TODO(), query).Decode(res)
	return res, err
}

func (c *EnvVersionColl) GetLatestRevision(productName, envName, serviceName string, isHelmChart, production bool) (int64, error) {
	match := bson.M{
		"product_name":         productName,
		"env_name":             envName,
		"service.service_name": serviceName,
		"production":           production,
	}
	if isHelmChart {
		delete(match, "service.service_name")
		match["service.release_name"] = serviceName
		match["service.type"] = setting.HelmChartDeployType
	}

	pipeline := []bson.M{
		{
			"$match": match,
		},
		{
			"$group": bson.M{
				"_id":             nil,
				"latest_revision": bson.M{"$max": "$revision"},
			},
		},
	}

	var result struct {
		LatestRevision int64 `bson:"latest_revision"`
	}

	cursor, err := c.Aggregate(context.TODO(), pipeline)
	if err != nil {
		return 0, err
	}
	defer cursor.Close(context.TODO())

	if cursor.Next(context.TODO()) {
		if err := cursor.Decode(&result); err != nil {
			return 0, err
		}
	}

	return result.LatestRevision, nil
}

func (c *EnvVersionColl) ListServiceVersions(productName, envName, serviceName, createBy string, page, pageSize int) ([]*models.EnvServiceVersion, int64, error) {
	var ret []*models.EnvServiceVersion
	query := bson.M{}

	if envName != "" {
		query["env_name"] = envName
	}

	if productName != "" {
		query["product_name"] = productName
	}

	if createBy != "" {
		query["create_by"] = createBy
	}

	if serviceName != "" {
		query["service.service_name"] = serviceName
	}

	// query["production"] = production

	// if isHelmChart {
	// 	query["service.release_name"] = serviceName
	// 	query["service.type"] = setting.HelmChartDeployType
	// } else {
	// 	query["service.service_name"] = serviceName
	// }

	opts := options.Find()
	opts.SetSort(bson.D{{"create_time", -1}})
	if page > 0 {
		opts.SetSkip(int64(pageSize * (page - 1)))
		opts.SetLimit(int64(pageSize))
	}
	ctx := context.Background()

	var (
		err   error
		count int64
	)

	if len(query) == 0 {
		count, err = c.Collection.EstimatedDocumentCount(ctx)
	} else {
		count, err = c.Collection.CountDocuments(ctx, query)
	}

	cursor, err := c.Collection.Find(ctx, query, opts)
	if err != nil {
		return nil, 0, err
	}

	err = cursor.All(ctx, &ret)
	if err != nil {
		return nil, 0, err
	}

	return ret, count, nil
}

func (c *EnvVersionColl) DeleteRevisions(productName, envName, serviceName string, isHelmChart, production bool, revision int64) error {
	query := bson.M{}
	query["env_name"] = envName
	query["product_name"] = productName
	query["production"] = production
	query["revision"] = bson.M{"$lte": revision}

	if isHelmChart {
		query["service.release_name"] = serviceName
		query["service.type"] = setting.HelmChartDeployType
	} else {
		query["service.service_name"] = serviceName
	}

	_, err := c.DeleteMany(mongotool.SessionContext(context.TODO(), c.Session), query)

	return err
}

func (c *EnvVersionColl) Create(args *models.EnvServiceVersion) error {
	// avoid panic issue
	if args == nil {
		return errors.New("nil EnvVersion")
	}

	now := time.Now().Unix()
	args.CreateTime = now
	args.UpdateTime = now
	_, err := c.InsertOne(mongotool.SessionContext(context.TODO(), c.Session), args)

	return err
}
