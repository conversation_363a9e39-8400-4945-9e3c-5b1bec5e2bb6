FROM golang:1.21.1-alpine as build

WORKDIR /app

ENV CGO_ENABLED=0 GOOS=linux
# ENV GOPROXY=https://goproxy.cn,direct
ENV GOCACHE=/gocache

# COPY go.mod go.sum ./
# COPY cmd cmd
# COPY pkg pkg

# RUN go mod download

# RUN --mount=type=cache,id=gobuild,target=/gocache \
#     go build -v -o /user ./cmd/user/main.go

COPY . .

RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -mod=vendor -v -o /user ./cmd/user/main.go

FROM alpine/git:v2.36.3

# https://wiki.alpinelinux.org/wiki/Setting_the_timezone
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo Asia/Shanghai  > /etc/timezone && \
    apk del tzdata

WORKDIR /app

COPY --from=build /user .

ENTRYPOINT ["/app/user"]
